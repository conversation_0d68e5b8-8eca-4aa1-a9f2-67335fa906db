-- Remove monitoring tables and related objects
-- This migration safely removes the data quality, system health, and business logic monitoring infrastructure

-- Drop views first (they depend on tables)
DROP VIEW IF EXISTS data_quality_trends;
DROP VIEW IF EXISTS current_data_quality_status;
DROP VIEW IF EXISTS business_logic_trends;
DROP VIEW IF EXISTS current_business_status;
DROP VIEW IF EXISTS system_health_summary;
DROP VIEW IF EXISTS comprehensive_monitoring_dashboard;

-- Drop functions
DROP FUNCTION IF EXISTS cleanup_old_data_quality_logs();
DROP FUNCTION IF EXISTS cleanup_old_business_logic_logs();
DROP FUNCTION IF EXISTS cleanup_old_health_logs();
DROP FUNCTION IF EXISTS get_data_quality_summary(INTEGER);

-- Drop tables (this will also drop their indexes and policies)
DROP TABLE IF EXISTS data_quality_logs;
DROP TABLE IF EXISTS business_logic_logs;
DROP TABLE IF EXISTS system_health_logs;
