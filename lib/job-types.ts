// Job type constants
export const JOB_TYPES = [
  'Full-time',
  'Part-time',
  'Contract',
  'Freelance',
  'Internship',
  'Temporary',
  'Apprenticeship',
  'Volunteer',
  'Other',
] as const;

export type JobType = (typeof JOB_TYPES)[number];

export const JOB_TYPE_DESCRIPTIONS: Record<JobType, string> = {
  'Full-time': 'Permanent positions with standard working hours',
  'Part-time': 'Positions with reduced or flexible hours',
  Contract: 'Fixed-term or project-based positions',
  Freelance: 'Self-employed or project-based contractual work',
  Internship: 'Temporary learning positions for students or recent graduates',
  Temporary: 'Short-term positions with defined end dates',
  Apprenticeship: 'Training positions combining work and learning',
  Volunteer: 'Unpaid positions for charitable or community work',
  Other: 'Non-standard employment arrangements',
} as const;
