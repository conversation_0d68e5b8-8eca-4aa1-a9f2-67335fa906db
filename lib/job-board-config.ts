// Job Board Configuration
// Defines settings for each of your job boards

import { CAREER_LEVELS } from './career-levels';
import { CURRENCY_CODES } from './data/currencies';
import { VISA_SPONSORSHIP_OPTIONS } from './job-status';
import { JOB_TYPES } from './job-types';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from './workplace';

export interface JobBoardConfig {
  id: string;
  name: string;
  description: string;
  enabled: boolean;

  // Airtable settings
  airtable: {
    baseId: string;
    tableName: string;
    pat?: string; // Optional if using shared PAT
  };

  // Job selection criteria
  filters: {
    // Job types to include
    types?: string[]; // e.g., ["Full-time", "Contract"]

    // Workplace preferences
    workplaceTypes?: string[]; // e.g., ["Remote", "Hybrid"]
    remoteRegions?: string[]; // e.g., ["Worldwide", "US Only"]

    // Location filters
    countries?: string[]; // e.g., ["United States", "Canada"]

    // Career levels
    careerLevels?: string[]; // e.g., ["Senior", "Lead", "Principal"]

    // Salary ranges
    salaryMin?: number;
    salaryMax?: number;
    salaryCurrencies?: string[]; // e.g., ["USD", "EUR"]

    // Keywords for title/description matching
    includeKeywords?: string[]; // e.g., ["React", "Node.js"]
    excludeKeywords?: string[]; // e.g., ["PHP", "WordPress"]

    // Other criteria
    visaSponsorship?: string; // "Yes", "No", "Not specified"
    languages?: string[]; // e.g., ["en", "es"]

    // Source preferences
    sourcePriority?: string[]; // e.g., ["wwr_rss", "jobdata_api"]
  };

  // Posting schedule
  posting: {
    dailyLimit: number; // e.g., 10 jobs per day
    postingTimes?: string[]; // e.g., ["09:00", "14:00"] in UTC
    timezone?: string; // e.g., "America/New_York"

    // Posting strategy
    strategy: 'newest_first' | 'best_match' | 'random';

    // Avoid duplicates
    avoidRepostingDays: number; // Don't repost same company within X days
  };

  // Tracking
  lastPostedAt?: string; // ISO date
  totalPosted?: number;
}

// Example configuration for your job boards
export const JOB_BOARDS: JobBoardConfig[] = [
  {
    id: 'remote-react-jobs',
    name: 'Remote React Jobs',
    description: 'React positions for remote developers',
    enabled: true,
    airtable: {
      baseId: '', // Configure in database
      tableName: 'Jobs',
    },
    filters: {
      types: [JOB_TYPES[0], JOB_TYPES[2]], // "Full-time", "Contract"
      workplaceTypes: [WORKPLACE_TYPES[2]], // "Remote"
      remoteRegions: [REMOTE_REGIONS[0], REMOTE_REGIONS[4], REMOTE_REGIONS[5]], // "Worldwide", "US Only", "EU Only"
      careerLevels: [
        CAREER_LEVELS[6],
        CAREER_LEVELS[7],
        CAREER_LEVELS[9],
        CAREER_LEVELS[10],
      ], // "Mid Level", "Senior", "Lead", "Principal"
      includeKeywords: ['React', 'TypeScript', 'JavaScript', 'Frontend'],
      excludeKeywords: ['PHP', 'Ruby', 'Java', 'C++'],
      salaryMin: 80_000,
      salaryCurrencies: [
        CURRENCY_CODES[0],
        CURRENCY_CODES[1],
        CURRENCY_CODES[2],
      ], // "USD", "EUR", "GBP"
    },
    posting: {
      dailyLimit: 10,
      postingTimes: ['09:00', '15:00'],
      timezone: 'America/New_York',
      strategy: 'newest_first',
      avoidRepostingDays: 30,
    },
  },

  {
    id: 'senior-engineer-jobs',
    name: 'Senior Engineer Jobs',
    description: 'Senior+ engineering positions',
    enabled: true,
    airtable: {
      baseId: process.env.AIRTABLE_SENIOR_JOBS_BASE_ID || '',
      tableName: 'Jobs',
    },
    filters: {
      types: [JOB_TYPES[0]], // "Full-time"
      careerLevels: [
        CAREER_LEVELS[7], // "Senior"
        CAREER_LEVELS[8], // "Staff"
        CAREER_LEVELS[10], // "Principal"
        CAREER_LEVELS[9], // "Lead"
        CAREER_LEVELS[11], // "Manager"
        CAREER_LEVELS[13], // "Director"
      ],
      salaryMin: 150_000,
      salaryCurrencies: [CURRENCY_CODES[0]], // "USD"
      visaSponsorship: VISA_SPONSORSHIP_OPTIONS[0], // "Yes"
    },
    posting: {
      dailyLimit: 10,
      strategy: 'best_match',
      avoidRepostingDays: 45,
    },
  },

  // Add your other 3 job boards here...
];

// Helper functions
export function getActiveJobBoards(): JobBoardConfig[] {
  return JOB_BOARDS.filter((board) => board.enabled);
}

export function getJobBoardById(id: string): JobBoardConfig | undefined {
  return JOB_BOARDS.find((board) => board.id === id);
}

// biome-ignore lint/suspicious/noExplicitAny: Job data structure varies by source and needs dynamic property access
// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex job matching logic with multiple filter conditions is necessary
export function matchesJobBoard(job: any, board: JobBoardConfig): boolean {
  const { filters } = board;

  // Type filter
  if (filters.types && job.type && !filters.types.includes(job.type)) {
    return false;
  }

  // Workplace type filter
  if (
    filters.workplaceTypes &&
    job.workplace_type &&
    !filters.workplaceTypes.includes(job.workplace_type)
  ) {
    return false;
  }

  // Remote region filter
  if (
    filters.remoteRegions &&
    job.remote_region &&
    !filters.remoteRegions.includes(job.remote_region)
  ) {
    return false;
  }

  // Country filter
  if (
    filters.countries &&
    job.workplace_country &&
    !filters.countries.includes(job.workplace_country)
  ) {
    return false;
  }

  // Career level filter
  if (filters.careerLevels && job.career_level) {
    const hasMatchingLevel = job.career_level.some((level: string) =>
      filters.careerLevels?.includes(level)
    );
    if (!hasMatchingLevel) {
      return false;
    }
  }

  // Salary filters
  if (
    filters.salaryMin &&
    job.salary_max &&
    job.salary_max < filters.salaryMin
  ) {
    return false;
  }

  if (
    filters.salaryMax &&
    job.salary_min &&
    job.salary_min > filters.salaryMax
  ) {
    return false;
  }

  if (
    filters.salaryCurrencies &&
    job.salary_currency &&
    !filters.salaryCurrencies.includes(job.salary_currency)
  ) {
    return false;
  }

  // Tag-based filtering (faster and more accurate than keyword matching)
  if (filters.includeKeywords && job.tags && Array.isArray(job.tags)) {
    // First try structured tags (much faster)
    const hasTagMatch = filters.includeKeywords.some((keyword) =>
      job.tags.some((tag: string) =>
        tag.toLowerCase().includes(keyword.toLowerCase())
      )
    );

    // If no tag match, fallback to text search
    if (!hasTagMatch) {
      const jobText = `${job.title} ${job.description} ${
        job.skills || ''
      }`.toLowerCase();
      const hasKeyword = filters.includeKeywords.some((keyword) =>
        jobText.includes(keyword.toLowerCase())
      );
      if (!hasKeyword) {
        return false;
      }
    }
  } else if (filters.includeKeywords) {
    // Fallback to text search if no tags available
    const jobText = `${job.title} ${job.description} ${
      job.skills || ''
    }`.toLowerCase();
    const hasKeyword = filters.includeKeywords.some((keyword) =>
      jobText.includes(keyword.toLowerCase())
    );
    if (!hasKeyword) {
      return false;
    }
  }

  if (filters.excludeKeywords) {
    // Check excluded keywords in tags first
    if (job.tags && Array.isArray(job.tags)) {
      const hasExcludedTag = filters.excludeKeywords.some((keyword) =>
        job.tags.some((tag: string) =>
          tag.toLowerCase().includes(keyword.toLowerCase())
        )
      );
      if (hasExcludedTag) {
        return false;
      }
    }

    // Also check in job text
    const jobText = `${job.title} ${job.description} ${
      job.skills || ''
    }`.toLowerCase();
    const hasExcluded = filters.excludeKeywords.some((keyword) =>
      jobText.includes(keyword.toLowerCase())
    );
    if (hasExcluded) {
      return false;
    }
  }

  // Visa sponsorship filter
  if (
    filters.visaSponsorship &&
    job.visa_sponsorship !== filters.visaSponsorship
  ) {
    return false;
  }

  // Language filter
  if (filters.languages && job.languages) {
    const hasMatchingLang = job.languages.some((lang: string) =>
      filters.languages?.includes(lang)
    );
    if (!hasMatchingLang) {
      return false;
    }
  }

  return true;
}
