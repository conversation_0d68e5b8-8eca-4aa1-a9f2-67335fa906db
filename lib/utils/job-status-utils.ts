import type { JobStatus } from '@/lib/job-status';

export function getJobStatusBadgeClass(status: JobStatus): string {
  switch (status) {
    case 'active':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-500/20 dark:text-emerald-300 dark:border-emerald-400/20';
    case 'draft':
      return 'bg-muted text-foreground border-border';
    case 'inactive':
      return 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-500/20 dark:text-amber-300 dark:border-amber-400/20';
    case 'expired':
      return 'bg-orange-100 text-orange-800 border-orange-200 dark:bg-orange-500/20 dark:text-orange-300 dark:border-orange-400/20';
    case 'filled':
      return 'bg-blue-100 text-blue-800 border-blue-200 dark:bg-blue-500/20 dark:text-blue-300 dark:border-blue-400/20';
    case 'cancelled':
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-500/20 dark:text-red-300 dark:border-red-400/20';
    default:
      return 'bg-muted text-foreground border-border';
  }
}

export function getProcessingStatusBadgeClass(status?: string): string {
  switch (status) {
    case 'completed':
      return 'bg-emerald-100 text-emerald-800 border-emerald-200 dark:bg-emerald-500/20 dark:text-emerald-300 dark:border-emerald-400/20';
    case 'pending':
      return 'bg-amber-100 text-amber-800 border-amber-200 dark:bg-amber-500/20 dark:text-amber-300 dark:border-amber-400/20';
    case 'failed':
      return 'bg-red-100 text-red-800 border-red-200 dark:bg-red-500/20 dark:text-red-300 dark:border-red-400/20';
    default:
      return 'bg-muted text-foreground border-border';
  }
}

export function getProcessingStatusText(status?: string): string {
  switch (status) {
    case 'completed':
      return 'AI Processed';
    case 'pending':
      return 'AI Pending';
    case 'failed':
      return 'AI Failed';
    default:
      return 'AI Unknown';
  }
}

export function formatValue(value: unknown): string {
  if (value === null || value === undefined) {
    return 'N/A';
  }
  if (typeof value === 'boolean') {
    return value ? 'Yes' : 'No';
  }
  if (Array.isArray(value)) {
    return value.join(', ');
  }
  if (typeof value === 'object') {
    return JSON.stringify(value, null, 2);
  }
  return String(value);
}
