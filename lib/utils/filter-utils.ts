/**
 * Shared utility functions for job filtering
 */

/**
 * Convert array to comma-separated string for URL
 */
export const arrayToString = (arr: string[] | undefined): string => {
  return arr && arr.length > 0 ? arr.join(',') : '';
};

/**
 * Convert comma-separated string to array
 */
export const stringToArray = (str: string): string[] => {
  return str
    ? str
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];
};

/**
 * Check if a filter value is active (not empty/default)
 */
export const isFilterActive = (value: unknown): boolean => {
  if (
    value === undefined ||
    value === null ||
    value === '' ||
    value === 'all'
  ) {
    return false;
  }
  if (Array.isArray(value)) {
    return value.length > 0;
  }
  return true;
};

/**
 * Count active filters in a filters object
 */
export const countActiveFilters = (
  filters: Record<string, unknown>
): number => {
  return Object.values(filters).filter(isFilterActive).length;
};

/**
 * Check if any filters are active
 */
export const hasActiveFilters = (filters: Record<string, unknown>): boolean => {
  return Object.values(filters).some(isFilterActive);
};

/**
 * Clear value for URL (convert empty values to null)
 */
export const clearUrlValue = (
  value: string | string[] | number | undefined
): string | number | null => {
  if (value === undefined || value === '' || value === 'all') {
    return null;
  }
  if (Array.isArray(value)) {
    return value.length > 0 ? arrayToString(value) : null;
  }
  return value;
};
