// Job status constants - v0 simplified
export const JOB_STATUSES = [
  'draft',
  'active',
  'inactive',
  'expired',
  'filled',
  'cancelled',
] as const;
export type JobStatus = (typeof JOB_STATUSES)[number];

// Status display configuration
export const STATUS_DISPLAY: Record<
  JobStatus,
  { label: string; color: string }
> = {
  draft: { label: 'Draft', color: 'gray' },
  active: { label: 'Active', color: 'green' },
  inactive: { label: 'Inactive', color: 'yellow' },
  expired: { label: 'Expired', color: 'orange' },
  filled: { label: 'Filled', color: 'blue' },
  cancelled: { label: 'Cancelled', color: 'red' },
};

// Simple status checking
export function isJobExpired(validThrough: string | null): boolean {
  if (!validThrough) {
    return false;
  }
  return new Date(validThrough) < new Date();
}

// Visa sponsorship constants
export const VISA_SPONSORSHIP_OPTIONS = ['Yes', 'No', 'Not specified'] as const;
export type VisaSponsorship = (typeof VISA_SPONSORSHIP_OPTIONS)[number];

export const VISA_SPONSORSHIP_DESCRIPTIONS: Record<VisaSponsorship, string> = {
  Yes: 'Visa sponsorship available',
  No: 'No visa sponsorship',
  'Not specified': 'Visa sponsorship not mentioned',
} as const;
