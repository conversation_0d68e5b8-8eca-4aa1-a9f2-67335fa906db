import { APPLY_METHODS } from './apply-methods';
import { CAREER_LEVELS } from './career-levels';
import { SCHEMA_LIMITS } from './constants';
import { countries } from './data/countries';
import { CURRENCY_CODES } from './data/currencies';
import { LANGUAGE_CODES } from './data/languages';
import { JOB_STATUSES, VISA_SPONSORSHIP_OPTIONS } from './job-status';
import { JOB_TYPES } from './job-types';
import { SALARY_UNITS } from './salary-units';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from './workplace';

// Simplified job extraction prompt
export const JOB_EXTRACTION_PROMPT = `Extract job data as JSON. Use null for missing data, never "Not specified".

REQUIRED DATA TYPES & ENUMS:
- type: string from ${JSON.stringify(JOB_TYPES)} or null
- status: string from ${JSON.stringify(JOB_STATUSES)} (default: "active")
- apply_method: string from ${JSON.stringify(APPLY_METHODS)} or null
- workplace_type: string from ${JSON.stringify(WORKPLACE_TYPES)} or null
- remote_region: string from ${JSON.stringify(REMOTE_REGIONS)} or null
- workplace_country: string from ${JSON.stringify(countries)} or null
- salary_currency: string from ${JSON.stringify(CURRENCY_CODES)} or null
- salary_unit: string from ${JSON.stringify(SALARY_UNITS)} or null
- career_level: ARRAY of strings from ${JSON.stringify(CAREER_LEVELS)} or null (e.g., ["Junior"] or ["Senior", "Manager"])
- languages: ARRAY of strings from ${JSON.stringify(LANGUAGE_CODES.slice(0, 20))}... (ISO 639-1 codes) or null
- visa_sponsorship: string from ${JSON.stringify(VISA_SPONSORSHIP_OPTIONS)} or null
- featured: boolean (true/false) or null
- travel_required: boolean (true/false) or null
- timezone_requirements: string or null
- salary_min: integer or null
- salary_max: integer or null

CRITICAL: Use EXACT enum values only! No abbreviations or variations.

COMMON CONVERSIONS:
- Countries: USA→"United States", UK→"United Kingdom", UAE→"United Arab Emirates"
- For worldwide/global remote jobs: use null for workplace_country
- Currencies: $→USD, €→EUR, £→GBP, ¥→JPY (context-aware)
- Languages: English→"en", Spanish→"es", French→"fr", German→"de", Chinese→"zh"
- Job Types: "Full time"→"Full-time", "Part time"→"Part-time", "Contractor"→"Contract"
- Remote Regions: "US/Canada"→"US/Canada Only", "US and Canada"→"US/Canada Only", "North America"→"US/Canada Only", "worldwide"→"Worldwide", "global"→"Worldwide", "US only"→"US Only", "Europe"→"Europe Only"
- Workplace Types: "remote"→"Remote", "hybrid"→"Hybrid", "on-site"→"On-site", "office"→"On-site"
- Dates: Convert all dates to ISO 8601 datetime format (e.g., "2024-01-15" → "2024-01-15T00:00:00.000Z")
- Apply Methods: Extract apply_url AND classify apply_method - email→"email", phone→"phone", forms→"form", LinkedIn→"platform", website→"link"
- Department: Extract department/team (e.g., "Engineering", "Marketing", "Sales", "Product")
- Travel: Set travel_required to true if job mentions travel requirements, false if explicitly no travel, null if unspecified

CRITICAL DATA TYPE REQUIREMENTS:
- career_level: MUST be an array (e.g., ["Junior"], not "Junior")
- languages: MUST be an array (e.g., ["en"], not "en")  
- featured: MUST be boolean true/false or null (never undefined)
- timezone_requirements: MUST be string or null (never undefined)
- salary_min/salary_max: MUST be integers or null (never strings or undefined)

CHARACTER LIMITS:
- benefits: max ${SCHEMA_LIMITS.benefits} chars
- application_requirements: max ${SCHEMA_LIMITS.application_requirements} chars
- skills: max ${SCHEMA_LIMITS.skills} chars
- qualifications: max ${SCHEMA_LIMITS.qualifications} chars
- education_requirements: max ${SCHEMA_LIMITS.education_requirements} chars
- experience_requirements: max ${SCHEMA_LIMITS.experience_requirements} chars
- responsibilities: max ${SCHEMA_LIMITS.responsibilities} chars

Job content:
---
{content}
---`;

// Job status classifier prompt (Monitoring v0.3)
export const JOB_STATUS_CLASSIFIER_PROMPT = `You are a job status classifier. Given an HTML/text snippet, decide whether the job is still open or not.
Return ONLY valid JSON matching this schema:
{
  "status": "active | closed | filled | unknown",
  "confidence": 0-1
}
Rules:
- "active" if the posting clearly indicates the job is accepting applications.
- "closed" if the job is no longer available, applications are closed/expired.
- "filled" if the position has been filled or a hire has been made.
- Use "unknown" when the snippet doesn't provide enough information.
- confidence must be a number between 0 and 1 (two decimals preferred).
- Do NOT output any other keys.

Snippet:\n---\n{snippet}\n---`;
