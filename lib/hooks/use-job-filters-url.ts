import { parseAsInteger, parseAsString, useQueryStates } from 'nuqs';
import { useCallback, useMemo } from 'react';
import type { JobFilters, JobFiltersUrlUpdate } from '@/lib/types/job-filters';
import {
  clearUrlValue,
  countActiveFilters,
  hasActiveFilters,
  stringToArray,
} from '@/lib/utils/filter-utils';

// URL-friendly parsers for all filter types
const jobFiltersParsers = {
  q: parseAsString.withDefault(''),
  status: parseAsString.withDefault(''),
  processing: parseAsString.withDefault(''),
  source: parseAsString.withDefault(''),
  types: parseAsString.withDefault(''),
  workplace: parseAsString.withDefault(''),
  levels: parseAsString.withDefault(''),
  currencies: parseAsString.withDefault(''),
  countries: parseAsString.withDefault(''),
  languages: parseAsString.withDefault(''),
  include: parseAsString.withDefault(''),
  exclude: parseAsString.withDefault(''),
  salaryMin: parseAsInteger,
  salaryMax: parseAsInteger,
} as const;

export function useJobFiltersUrl() {
  // Use nuqs to manage URL state
  const [urlState, setUrlState] = useQueryStates(jobFiltersParsers, {
    history: 'replace',
    shallow: false,
    throttleMs: 300,
  });

  // Convert URL state to JobFilters format for API compatibility
  const filters: JobFilters = useMemo(
    () => ({
      search: urlState.q || undefined,
      status: urlState.status || undefined,
      processingStatus: urlState.processing || undefined,
      sourceType: urlState.source || undefined,
      jobTypes: stringToArray(urlState.types),
      workplaceTypes: stringToArray(urlState.workplace),
      careerLevels: stringToArray(urlState.levels),
      salaryMin: urlState.salaryMin || undefined,
      salaryMax: urlState.salaryMax || undefined,
      salaryCurrencies: stringToArray(urlState.currencies),
      includeKeywords: stringToArray(urlState.include),
      excludeKeywords: stringToArray(urlState.exclude),
      countries: stringToArray(urlState.countries),
      languages: stringToArray(urlState.languages),
    }),
    [urlState]
  );

  // Count active filters and check if any are active
  const activeFilterCount = useMemo(
    () => countActiveFilters(urlState),
    [urlState]
  );
  const hasFilters = useMemo(() => hasActiveFilters(urlState), [urlState]);

  // Generic update function for all filter types
  const updateFilter = useCallback(
    (key: keyof JobFilters, value: string | string[] | number | undefined) => {
      const updates: JobFiltersUrlUpdate = {};

      switch (key) {
        case 'search':
          updates.q = clearUrlValue(value) as string | null;
          break;
        case 'status':
          updates.status = clearUrlValue(value) as string | null;
          break;
        case 'processingStatus':
          updates.processing = clearUrlValue(value) as string | null;
          break;
        case 'sourceType':
          updates.source = clearUrlValue(value) as string | null;
          break;
        case 'jobTypes':
          updates.types = clearUrlValue(value) as string | null;
          break;
        case 'workplaceTypes':
          updates.workplace = clearUrlValue(value) as string | null;
          break;
        case 'careerLevels':
          updates.levels = clearUrlValue(value) as string | null;
          break;
        case 'salaryMin':
          updates.salaryMin = clearUrlValue(value) as number | null;
          break;
        case 'salaryMax':
          updates.salaryMax = clearUrlValue(value) as number | null;
          break;
        case 'salaryCurrencies':
          updates.currencies = clearUrlValue(value) as string | null;
          break;
        case 'includeKeywords':
          updates.include = clearUrlValue(value) as string | null;
          break;
        case 'excludeKeywords':
          updates.exclude = clearUrlValue(value) as string | null;
          break;
        case 'countries':
          updates.countries = clearUrlValue(value) as string | null;
          break;
        case 'languages':
          updates.languages = clearUrlValue(value) as string | null;
          break;
        default:
          // Handle unexpected keys gracefully
          break;
      }

      setUrlState(updates);
    },
    [setUrlState]
  );

  // Utility functions for common operations
  const toggleArrayFilter = useCallback(
    (key: keyof JobFilters, item: string) => {
      const currentArray = (filters[key] as string[]) || [];
      const newArray = currentArray.includes(item)
        ? currentArray.filter((i) => i !== item)
        : [...currentArray, item];
      updateFilter(key, newArray.length > 0 ? newArray : undefined);
    },
    [filters, updateFilter]
  );

  const addKeyword = useCallback(
    (type: 'includeKeywords' | 'excludeKeywords', keyword: string) => {
      const current = (filters[type] as string[]) || [];
      if (!current.includes(keyword)) {
        updateFilter(type, [...current, keyword]);
      }
    },
    [filters, updateFilter]
  );

  const setSalaryRange = useCallback(
    (min: number | null, max: number | null) =>
      setUrlState({ salaryMin: min, salaryMax: max }),
    [setUrlState]
  );

  // Clear all filters
  const clearAllFilters = useCallback(() => {
    setUrlState({
      q: null,
      status: null,
      processing: null,
      source: null,
      types: null,
      workplace: null,
      levels: null,
      currencies: null,
      countries: null,
      languages: null,
      include: null,
      exclude: null,
      salaryMin: null,
      salaryMax: null,
    });
  }, [setUrlState]);

  const removeKeyword = useCallback(
    (type: 'includeKeywords' | 'excludeKeywords', keyword: string) => {
      const current = (filters[type] as string[]) || [];
      const newKeywords = current.filter((k) => k !== keyword);
      updateFilter(type, newKeywords.length > 0 ? newKeywords : undefined);
    },
    [filters, updateFilter]
  );

  return {
    // Core data
    filters,
    urlState,
    activeFilterCount,
    hasActiveFilters: hasFilters,

    // Core functions
    updateFilter,
    toggleArrayFilter,
    addKeyword,
    removeKeyword,
    setSalaryRange,
    clearAllFilters,

    // Direct URL state setter for advanced use cases
    setUrlState,
  };
}
