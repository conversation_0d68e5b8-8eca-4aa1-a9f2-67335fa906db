import { useCallback } from 'react';
import { toast } from 'sonner';
import type { DatabaseJob } from '@/lib/storage';

/**
 * Interface for bulk processing result
 */
interface BulkProcessingResult {
  success: boolean;
  message: string;
  results?: unknown[];
  errors?: Array<{ jobId: string; error: string }>;
  stats?: {
    total: number;
    successful: number;
    failed: number;
  };
}

/**
 * Show loading toast for bulk processing with estimated time
 */
function showProcessingLoadingToast(jobCount: number) {
  const estimatedTime = jobCount * 5; // ~5 seconds per job
  const timeText =
    estimatedTime > 60
      ? `~${Math.round(estimatedTime / 60)} min`
      : `~${estimatedTime} sec`;

  return toast.loading(`Processing ${jobCount} job(s)...`, {
    description: `Starting AI extraction • Estimated time: ${timeText}`,
  });
}

/**
 * Perform the actual bulk processing API call
 */
async function performBulkProcessing(jobIds: string[]) {
  const response = await fetch('/api/jobs', {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'bulkProcessNow',
      ids: jobIds,
    }),
  });

  if (!response.ok) {
    throw new Error('Failed to process jobs');
  }

  return response.json();
}

/**
 * Show result toasts based on processing outcome
 */
function showProcessingResultToasts(result: BulkProcessingResult) {
  if (result.stats) {
    const { total, successful, failed } = result.stats;

    if (failed === 0) {
      // All successful
      toast.success(`✅ All ${successful} job(s) processed successfully!`, {
        description:
          'AI extraction completed • Jobs are now ready for publishing',
        duration: 6000,
        action: {
          label: 'View Jobs',
          onClick: () => window.location.reload(),
        },
      });
    } else if (successful > 0) {
      // Partial success
      toast.warning('⚠️ Processing completed with mixed results', {
        description: `${successful} successful, ${failed} failed out of ${total} jobs • Check failed jobs for details`,
        duration: 7000,
        action: {
          label: 'Refresh',
          onClick: () => window.location.reload(),
        },
      });
    } else {
      // All failed
      toast.error(`❌ All ${total} job(s) failed to process`, {
        description:
          'No jobs were successfully processed • Check job details and try again',
        duration: 8000,
        action: {
          label: 'Refresh',
          onClick: () => window.location.reload(),
        },
      });
    }

    // Show additional details if there were errors
    if (result.errors && result.errors.length > 0) {
      const errorSample = result.errors.slice(0, 2);
      const errorDetails = errorSample
        .map((err: { jobId: string; error: string }) => `• ${err.error}`)
        .join('\n');
      const moreErrors =
        result.errors.length > 2
          ? `\n... and ${result.errors.length - 2} more`
          : '';

      toast.error('Processing errors detected', {
        description: `${errorDetails}${moreErrors}`,
        duration: 8000,
      });
    }
  } else {
    // Fallback success message
    toast.success('Jobs processing completed!', {
      description: 'AI extraction has been triggered for selected jobs',
      duration: 4000,
    });
  }
}

/**
 * Show error toast for processing failures
 */
function showProcessingErrorToast(error: unknown) {
  toast.error('Failed to process jobs', {
    description:
      error instanceof Error ? error.message : 'Unknown error occurred',
    duration: 6000,
  });
}

interface BulkOperationsProps {
  selectedRows: { original: DatabaseJob }[];
  onRefresh: () => Promise<void>;
  onClearSelection: () => void;
}

export function useBulkOperations({
  selectedRows,
  onRefresh,
  onClearSelection,
}: BulkOperationsProps) {
  const handleBulkDelete = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Delete ${selectedRows.length} selected job(s)? This cannot be undone.`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch('/api/jobs', {
        method: 'DELETE',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ ids: jobIds }),
      });

      if (!response.ok) {
        throw new Error('Failed to delete jobs');
      }

      await onRefresh();
      onClearSelection();

      toast.success(`✅ Deleted ${selectedRows.length} job(s)`, {
        description: 'Jobs have been permanently removed',
        duration: 4000,
      });
    } catch (_error) {
      toast.error('Failed to delete jobs', {
        description: 'An error occurred while deleting the selected jobs',
        duration: 5000,
      });
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkStatusUpdate = useCallback(
    async (newStatus: string) => {
      if (selectedRows.length === 0) {
        return;
      }

      const confirmed = window.confirm(
        `Update status to "${newStatus}" for ${selectedRows.length} selected job(s)?`
      );
      if (!confirmed) {
        return;
      }

      try {
        const jobIds = selectedRows.map((row) => row.original.id);
        const response = await fetch('/api/jobs', {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'bulkUpdateStatus',
            ids: jobIds,
            status: newStatus,
          }),
        });

        if (!response.ok) {
          throw new Error('Failed to update job status');
        }

        await onRefresh();
        onClearSelection();

        toast.success(
          `✅ Updated ${selectedRows.length} job(s) to "${newStatus}"`,
          {
            description: 'Job status has been updated successfully',
            duration: 4000,
          }
        );
      } catch (_error) {
        toast.error('Failed to update job status', {
          description: 'An error occurred while updating the job status',
          duration: 5000,
        });
      }
    },
    [selectedRows, onRefresh, onClearSelection]
  );

  const handleBulkRequeueAI = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Requeue AI processing for ${selectedRows.length} selected job(s)?`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulkRequeueExtraction',
          ids: jobIds,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to requeue AI processing');
      }

      await onRefresh();
      onClearSelection();

      toast.success(
        `✅ Requeued ${selectedRows.length} job(s) for AI processing`,
        {
          description: 'Jobs have been marked for AI extraction',
          duration: 4000,
        }
      );
    } catch (_error) {
      toast.error('Failed to requeue AI processing', {
        description: 'An error occurred while requeuing jobs for AI processing',
        duration: 5000,
      });
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkAirtablePush = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Push ${selectedRows.length} selected job(s) to Airtable?`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulkPushAirtable',
          ids: jobIds,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to push to Airtable');
      }

      await onRefresh();
      onClearSelection();

      toast.success(`✅ Pushed ${selectedRows.length} job(s) to Airtable`, {
        description: 'Jobs have been successfully synced to Airtable',
        duration: 4000,
      });
    } catch (_error) {
      toast.error('Failed to push to Airtable', {
        description: 'An error occurred while syncing jobs to Airtable',
        duration: 5000,
      });
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkResetMonitor = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Reset monitor status for ${selectedRows.length} selected job(s)?`
    );
    if (!confirmed) {
      return;
    }

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const response = await fetch('/api/jobs', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'bulkResetMonitor',
          ids: jobIds,
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to reset monitor');
      }

      await onRefresh();
      onClearSelection();

      toast.success(`✅ Reset monitor for ${selectedRows.length} job(s)`, {
        description: 'Monitor status has been reset successfully',
        duration: 4000,
      });
    } catch (_error) {
      toast.error('Failed to reset monitor', {
        description: 'An error occurred while resetting the monitor status',
        duration: 5000,
      });
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  const handleBulkProcessNow = useCallback(async () => {
    if (selectedRows.length === 0) {
      return;
    }

    const confirmed = window.confirm(
      `Process ${selectedRows.length} selected job(s) now? This will trigger AI extraction for all selected jobs.`
    );
    if (!confirmed) {
      return;
    }

    const loadingToast = showProcessingLoadingToast(selectedRows.length);

    try {
      const jobIds = selectedRows.map((row) => row.original.id);
      const result = await performBulkProcessing(jobIds);

      toast.dismiss(loadingToast);
      showProcessingResultToasts(result);

      await onRefresh();
      onClearSelection();
    } catch (error) {
      toast.dismiss(loadingToast);
      showProcessingErrorToast(error);
    }
  }, [selectedRows, onRefresh, onClearSelection]);

  return {
    handleBulkDelete,
    handleBulkStatusUpdate,
    handleBulkRequeueAI,
    handleBulkAirtablePush,
    handleBulkResetMonitor,
    handleBulkProcessNow,
  };
}
