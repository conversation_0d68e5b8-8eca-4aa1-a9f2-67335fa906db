import { useCallback, useEffect, useState } from 'react';
import type { DatabaseJob } from '@/lib/storage';
import { logger } from '@/lib/utils';

interface UseJobDetailResult {
  job: DatabaseJob | null;
  loading: boolean;
  error: string;
  editForm: Partial<DatabaseJob>;
  isEditing: boolean;
  saving: boolean;
  setEditForm: React.Dispatch<React.SetStateAction<Partial<DatabaseJob>>>;
  setIsEditing: React.Dispatch<React.SetStateAction<boolean>>;
  setSaving: React.Dispatch<React.SetStateAction<boolean>>;
  setError: React.Dispatch<React.SetStateAction<string>>;
  refreshJob: () => Promise<void>;
  handleEditToggle: () => void;
  handleSave: () => Promise<void>;
  handleInputChange: (
    field: keyof DatabaseJob,
    value: string | boolean | null
  ) => void;
}

export function useJobDetail(jobId: string): UseJobDetailResult {
  const [job, setJob] = useState<DatabaseJob | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string>('');
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<Partial<DatabaseJob>>({});
  const [saving, setSaving] = useState(false);

  // Fetch job data
  const fetchJob = useCallback(async () => {
    try {
      const response = await fetch(`/api/jobs/${jobId}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || `HTTP ${response.status}`);
      }

      const data = await response.json();
      setJob(data.job);
      setEditForm(data.job);

      logger.info('Job loaded successfully', { jobId });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load job';
      setError(errorMessage);
      logger.error('Failed to load job:', errorMessage);
    } finally {
      setLoading(false);
    }
  }, [jobId]);

  // Refresh job data (can be called externally)
  const refreshJob = async () => {
    setLoading(true);
    setError('');
    await fetchJob();
  };

  // Initial load
  useEffect(() => {
    fetchJob();
  }, [fetchJob]);

  // Handle edit toggle
  const handleEditToggle = () => {
    if (isEditing) {
      // Reset form to current job data when canceling
      setEditForm(job || {});
    }
    setIsEditing(!isEditing);
  };

  // Handle save
  const handleSave = async () => {
    if (!job) {
      return;
    }

    setSaving(true);
    try {
      const response = await fetch(`/api/jobs/${jobId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(editForm),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to update job');
      }

      const data = await response.json();
      setJob(data.job);
      setEditForm(data.job);
      setIsEditing(false);

      logger.info('Job updated successfully', { jobId });
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to update job';
      setError(errorMessage);
      logger.error('Failed to update job:', errorMessage);
    } finally {
      setSaving(false);
    }
  };

  // Handle input changes
  const handleInputChange = (
    field: keyof DatabaseJob,
    value: string | boolean | null
  ) => {
    setEditForm((prev: Partial<DatabaseJob>) => ({
      ...prev,
      [field]: value,
    }));
  };

  return {
    job,
    loading,
    error,
    editForm,
    isEditing,
    saving,
    setEditForm,
    setIsEditing,
    setSaving,
    setError,
    refreshJob,
    handleEditToggle,
    handleSave,
    handleInputChange,
  };
}
