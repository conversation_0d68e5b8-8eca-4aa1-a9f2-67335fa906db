/**
 * Hook that combines Jobs Zustand store with URL filters
 * Manages syncing between URL state and store state
 */

import type { SortingState } from '@tanstack/react-table';
import { useCallback, useEffect, useRef } from 'react';
import { useJobsStoreHydrated } from '@/lib/stores';
import { useJobFiltersUrl } from './use-job-filters-url';

export function useJobsWithUrl() {
  // Get URL filter state and controls
  const {
    filters: urlFilters,
    activeFilterCount,
    ...urlControls
  } = useJobFiltersUrl();

  // Get Zustand store state and actions
  const store = useJobsStoreHydrated();

  // Refs to track state and prevent loops
  const isInitialLoadRef = useRef(true);
  const filtersDebounceRef = useRef<NodeJS.Timeout | null>(null);

  // Sync URL filters to store (with debounce)
  useEffect(() => {
    // Clear any existing timeout
    if (filtersDebounceRef.current) {
      clearTimeout(filtersDebounceRef.current);
    }

    // Debounce filter updates to prevent too many API calls
    filtersDebounceRef.current = setTimeout(() => {
      store.actions.setFilters(urlFilters);

      // Fetch only if not initial load (initial fetch happens in store)
      if (!isInitialLoadRef.current) {
        store.actions.fetch();
      }
      isInitialLoadRef.current = false;
    }, 300);

    return () => {
      if (filtersDebounceRef.current) {
        clearTimeout(filtersDebounceRef.current);
      }
    };
  }, [urlFilters, store.actions]);

  // Initial load
  useEffect(() => {
    if (isInitialLoadRef.current) {
      store.actions.fetch();
      isInitialLoadRef.current = false;
    }
  }, [store.actions]);

  // Enhanced page change handler that maintains filters
  const handlePageChange = useCallback(
    (page: number) => {
      store.actions.setPage(page);
    },
    [store.actions]
  );

  // Enhanced page size change handler
  const handlePageSizeChange = useCallback(
    (pageSize: number) => {
      store.actions.setPageSize(pageSize);
    },
    [store.actions]
  );

  // Enhanced sorting change handler
  const handleSortingChange = useCallback(
    (sorting: SortingState) => {
      store.actions.setSorting(sorting);
    },
    [store.actions]
  );

  // Refresh function
  const refresh = useCallback(() => {
    store.actions.fetch();
  }, [store.actions]);

  return {
    // Data from store
    jobs: store.jobs,
    loading: store.loading,
    error: store.error,
    pagination: store.pagination,
    sqlInfo: store.sqlInfo,
    selectedJobIds: store.selectedJobIds,

    // Filter state
    filters: store.filters,
    activeFilterCount,

    // URL controls (for filter components)
    ...urlControls,

    // Page/Sort handlers
    handlePageChange,
    handlePageSizeChange,
    handleSortingChange,

    // Actions
    processJob: store.actions.processJob,
    processBatch: store.actions.processBatch,
    deleteBatch: store.actions.deleteBatch,
    exportBatch: store.actions.exportBatch,
    selectJob: store.actions.selectJob,
    deselectJob: store.actions.deselectJob,
    selectAll: store.actions.selectAll,
    deselectAll: store.actions.deselectAll,
    updateJob: store.actions.updateJob,
    clearError: store.actions.clearError,
    refresh,
  };
}
