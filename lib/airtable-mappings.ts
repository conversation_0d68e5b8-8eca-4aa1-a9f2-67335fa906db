import { getLanguageByCode, getLanguageByName } from './data/languages';

/**
 * Centralized Airtable value mappings
 * Converts our internal formats to Airtable's expected display formats
 */

// Language mapping: Convert language names/codes to Airtable format
export function mapLanguageToAirtableFormat(language: string): string {
  // Try to find by name first
  const byName = getLanguageByName(language);
  if (byName) {
    return `${byName.name} (${byName.code})`;
  }

  // Try to find by code
  const byCode = getLanguageByCode(language);
  if (byCode) {
    return `${byCode.name} (${byCode.code})`;
  }

  // Return original if not found
  return language;
}

// Currency mapping: Convert currency codes to Airtable format
export const CURRENCY_DISPLAY_NAMES: Record<string, string> = {
  USD: 'United States Dollar',
  EUR: 'Euro',
  GBP: 'British Pound Sterling',
  CAD: 'Canadian Dollar',
  AUD: 'Australian Dollar',
  JPY: 'Japanese Yen',
  CHF: 'Swiss Franc',
  CNY: 'Chinese Yuan',
  SEK: 'Swedish Krona',
  NZD: 'New Zealand Dollar',
  MXN: 'Mexican Peso',
  SGD: 'Singapore Dollar',
  HKD: 'Hong Kong Dollar',
  NOK: 'Norwegian Krone',
  KRW: 'South Korean Won',
  TRY: 'Turkish Lira',
  RUB: 'Russian Ruble',
  INR: 'Indian Rupee',
  BRL: 'Brazilian Real',
  ZAR: 'South African Rand',
  // Crypto
  BTC: 'Bitcoin',
  ETH: 'Ethereum',
  USDT: 'Tether',
  USDC: 'USD Coin',
  // Add more as needed
};

export function mapCurrencyToAirtableFormat(currency: string): string {
  const displayName = CURRENCY_DISPLAY_NAMES[currency];
  if (displayName) {
    return `${currency} (${displayName})`;
  }
  // Return with generic format if not in our mapping
  return currency;
}

// Career level mapping to available Airtable values
export function mapCareerLevelToAirtableFormat(levels: string[]): string[] {
  // Based on the actual available values in Airtable
  const airtableAvailableLevels = [
    'Internship',
    'Junior',
    'Mid Level',
    'Senior',
  ];

  return (
    levels
      // biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex mapping logic for various career level formats is necessary
      .map((level) => {
        // Direct match
        if (airtableAvailableLevels.includes(level)) {
          return level;
        }

        // Map variations
        switch (level.toLowerCase()) {
          case 'entry level':
          case 'entry-level':
          case 'associate':
            return 'Junior';
          case 'mid level':
          case 'mid-level':
          case 'intermediate':
            return 'Mid Level';
          case 'senior':
          case 'sr':
          case 'lead':
          case 'principal':
          case 'staff':
          case 'manager':
          case 'director':
          case 'vp':
          case 'c-level':
            return 'Senior';
          case 'intern':
          case 'internship':
            return 'Internship';
          default:
            // If we can't map it, try to find the closest match
            if (level.includes('intern')) {
              return 'Internship';
            }
            if (level.includes('junior') || level.includes('entry')) {
              return 'Junior';
            }
            if (level.includes('mid')) {
              return 'Mid Level';
            }
            if (level.includes('senior') || level.includes('lead')) {
              return 'Senior';
            }
            return 'Mid Level'; // Default fallback
        }
      })
      .filter((value, index, self) => self.indexOf(value) === index)
  ); // Remove duplicates
}

// Helper to convert arrays of languages
export function mapLanguagesToAirtableFormat(languages: string[]): string[] {
  return languages.map(mapLanguageToAirtableFormat);
}

// List of fields we send to Airtable (single source of truth)
export const AIRTABLE_FIELD_LIST = [
  // Basic job fields
  'title',
  'company',
  'type',
  'description',
  'status',

  // Location fields
  'workplace_type',
  'remote_region',
  'timezone_requirements',
  'workplace_city',
  'workplace_country',

  // Salary fields
  'salary_min',
  'salary_max',
  'salary_currency',
  'salary_unit',

  // Career and application fields
  'career_level',
  'visa_sponsorship',
  'languages',
  'skills',
  'qualifications',
  'education_requirements',
  'experience_requirements',
  'responsibilities',

  // Application fields
  'apply_url',
  'apply_method',
  'application_requirements',

  // Date fields
  'posted_date',
  'valid_through',

  // Source fields (snake_case - consistent with database!)
  'job_source_name',
  'job_identifier',
  'sourced_at',
  'source_url',

  // Metadata fields
  'benefits',
  'department',
  'travel_required',
  'featured',
  'industry',
  'occupational_category',
] as const;

export type AirtableFieldName = (typeof AIRTABLE_FIELD_LIST)[number];
