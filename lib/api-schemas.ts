/**
 * Shared Zod Schema Library for API Validation
 *
 * This file consolidates all API validation schemas to ensure consistency
 * across routes and enable OpenAPI generation from a single source of truth.
 */

import { z } from 'zod';
import { APPLY_METHODS } from './apply-methods';
import { CAREER_LEVELS } from './career-levels';
import { INPUT_LIMITS, SCHEMA_LIMITS } from './constants';
import { countries } from './data/countries';
import { CURRENCY_CODES } from './data/currencies';
import { LANGUAGE_CODES } from './data/languages';
import { JOB_STATUSES, VISA_SPONSORSHIP_OPTIONS } from './job-status';
import { JOB_TYPES } from './job-types';
import { MONITOR_STATUSES } from './monitor-schema';
import { SALARY_UNITS } from './salary-units';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from './workplace';

// =============================================================================
// COMMON SCHEMAS
// =============================================================================

export const TimestampSchema = z.string().datetime();
export const UUIDSchema = z.string().uuid();
export const URLSchema = z.string().url();
export const EmailSchema = z.string().email();

// =============================================================================
// HEALTH CHECK SCHEMAS
// =============================================================================

export const HealthCheckSchema = z.object({
  service: z.string(),
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  version: z.string().optional(),
  features: z.array(z.string()),
  environment: z.record(z.string(), z.boolean()).optional(),
  timestamp: TimestampSchema.optional(),
});

export const ServiceHealthResponseSchema = z.object({
  service: z.string(),
  status: z.string(),
  features: z.array(z.string()),
  environment: z.record(z.string(), z.boolean()).optional(),
});

export const SystemHealthResponseSchema = z.object({
  status: z.enum(['healthy', 'degraded', 'unhealthy']),
  services: z.array(HealthCheckSchema),
  timestamp: TimestampSchema,
  uptime: z.number(),
});

// =============================================================================
// AIRTABLE SCHEMAS
// =============================================================================

export const AirtableBaseIdSchema = z
  .string()
  .regex(
    /^app[A-Za-z0-9]{14}$/,
    'Base ID must be "app" + 14 alphanumeric characters'
  );

export const AirtablePATSchema = z
  .string()
  .regex(
    /^pat[A-Za-z0-9.]{40,}$/,
    'PAT must start with "pat" and be at least 40 characters'
  );

export const AirtableTableNameSchema = z.string().min(1).max(100);

export const AirtableConfigSchema = z.object({
  baseId: AirtableBaseIdSchema,
  tableName: AirtableTableNameSchema,
  pat: AirtablePATSchema,
});

export const AirtableFieldSchema = z.object({
  id: z.string(),
  name: z.string(),
  type: z.string(),
  options: z
    .object({
      choices: z
        .array(
          z.object({
            id: z.string(),
            name: z.string(),
            color: z.string().optional(),
          })
        )
        .optional(),
    })
    .passthrough()
    .optional(),
});

export const AirtableTableSchema = z.object({
  id: z.string(),
  name: z.string(),
  fields: z.array(AirtableFieldSchema),
});

// =============================================================================
// JOB SCHEMAS
// =============================================================================

export const JobExtractionSchema = z.object({
  // Core fields
  title: z.string(),
  company: z.string().nullable().optional(),
  type: z.enum(JOB_TYPES).nullable().optional(),
  description: z.string(),
  apply_url: z.string().nullable().optional(),
  apply_method: z.enum(APPLY_METHODS).nullable().optional(),
  posted_date: TimestampSchema.nullable().optional(),
  status: z.enum(JOB_STATUSES).default('active'),

  // Salary
  salary_min: z.number().int().positive().nullable().optional(),
  salary_max: z.number().int().positive().nullable().optional(),
  salary_currency: z.enum(CURRENCY_CODES).nullable().optional(),
  salary_unit: z.enum(SALARY_UNITS).nullable().optional(),

  // Location & Remote work
  workplace_type: z.enum(WORKPLACE_TYPES).nullable().optional(),
  remote_region: z.enum(REMOTE_REGIONS).nullable().optional(),
  timezone_requirements: z.string().nullable().optional(),
  workplace_city: z.string().nullable().optional(),
  workplace_country: z.enum(countries).nullable().optional(),

  // Additional details
  benefits: z.string().max(SCHEMA_LIMITS.benefits).nullable().optional(),
  application_requirements: z
    .string()
    .max(SCHEMA_LIMITS.application_requirements)
    .nullable()
    .optional(),
  valid_through: TimestampSchema.nullable().optional(),
  job_identifier: z.string().nullable().optional(),
  job_source_name: z.string().nullable().optional(),
  department: z.string().nullable().optional(),
  travel_required: z.boolean().nullable().optional(),

  // Career & Skills
  career_level: z.array(z.enum(CAREER_LEVELS)).nullable().optional(),
  visa_sponsorship: z.enum(VISA_SPONSORSHIP_OPTIONS).nullable().optional(),
  languages: z.array(z.enum(LANGUAGE_CODES)).nullable().optional(),
  skills: z.string().max(SCHEMA_LIMITS.skills).nullable().optional(),
  qualifications: z
    .string()
    .max(SCHEMA_LIMITS.qualifications)
    .nullable()
    .optional(),
  education_requirements: z
    .string()
    .max(SCHEMA_LIMITS.education_requirements)
    .nullable()
    .optional(),
  experience_requirements: z
    .string()
    .max(SCHEMA_LIMITS.experience_requirements)
    .nullable()
    .optional(),
  responsibilities: z
    .string()
    .max(SCHEMA_LIMITS.responsibilities)
    .nullable()
    .optional(),

  // SEO & Classification
  featured: z.boolean().nullable().optional(),
  industry: z.string().nullable().optional(),
  occupational_category: z.string().nullable().optional(),
});

export const JobSchema = z.object({
  sourcedAt: TimestampSchema,
  sourceUrl: URLSchema,
  ...JobExtractionSchema.shape,
});

// =============================================================================
// PIPELINE SCHEMAS
// =============================================================================

export const PipelineJobSchema = z.object({
  content: z.string().min(1, 'Job content is required'),
  sourceUrl: URLSchema,
  source: z.string().optional().default('pipeline'),
  metadata: z
    .object({
      company: z.string().optional(),
      title: z.string().optional(),
      postedDate: z.string().optional(),
      batchId: z.string().optional(),
    })
    .optional()
    .default({}),
});

export const PipelineBatchSchema = z.object({
  jobs: z.array(PipelineJobSchema).min(1, 'At least one job required'),
  batchId: z.string().optional(),
  priority: z.enum(['low', 'normal', 'high']).optional().default('normal'),
});

export const PipelineHealthResponseSchema = z.object({
  message: z.string(),
  version: z.string(),
  features: z.array(z.string()),
  usage: z.object({
    single: z.string(),
    batch: z.string(),
  }),
  timestamp: TimestampSchema,
});

// =============================================================================
// WEBHOOK SCHEMAS
// =============================================================================

export const ApifyWebhookSchema = z
  .object({
    eventType: z.string(),
    eventData: z.object({
      actorRunId: z.string(),
    }),
    resource: z
      .object({
        id: z.string(),
        status: z.string(),
        defaultDatasetId: z.string().optional(),
        datasetId: z.string().optional(),
        actorId: z.string().optional(),
        actId: z.string().optional(),
      })
      .passthrough(),
  })
  .passthrough();

export const QStashCallbackSchema = z.object({
  status: z.number().optional(),
  responseBody: z.string().optional(),
  createdAt: z.union([z.number(), z.string()]).optional(),
  sourceMessageId: z.string().optional(),
  dlqId: z.string().optional(),
});

// =============================================================================
// MONITORING SCHEMAS
// =============================================================================

export const JobStatusClassifierSchema = z.object({
  status: z.enum(MONITOR_STATUSES),
  confidence: z.number().min(0).max(1),
});

// =============================================================================
// EXTRACT API SCHEMAS
// =============================================================================

export const ExtractRequestSchema = z.object({
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
  sourceUrl: URLSchema.optional(),
});

export const ExtractJobRequestSchema = z.object({
  jobId: UUIDSchema.optional(),
  sourcedAt: TimestampSchema.optional(),
  sourceUrl: URLSchema.optional(),
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
});

// =============================================================================
// ERROR SCHEMAS
// =============================================================================

export const ErrorResponseSchema = z.object({
  error: z.string(),
  message: z.string(),
  code: z.string().optional(),
  details: z.record(z.string(), z.unknown()).optional(),
  timestamp: TimestampSchema.optional(),
});

export const ValidationErrorSchema = z.object({
  error: z.literal('ValidationError'),
  message: z.string(),
  code: z.literal('INVALID_SCHEMA'),
  details: z.object({
    field: z.string(),
    issue: z.string(),
  }),
});

// =============================================================================
// TYPE EXPORTS
// =============================================================================

export type HealthCheck = z.infer<typeof HealthCheckSchema>;
export type ServiceHealthResponse = z.infer<typeof ServiceHealthResponseSchema>;
export type SystemHealthResponse = z.infer<typeof SystemHealthResponseSchema>;
export type AirtableConfig = z.infer<typeof AirtableConfigSchema>;
export type AirtableField = z.infer<typeof AirtableFieldSchema>;
export type AirtableTable = z.infer<typeof AirtableTableSchema>;
export type Job = z.infer<typeof JobSchema>;
export type JobExtraction = z.infer<typeof JobExtractionSchema>;
export type PipelineJob = z.infer<typeof PipelineJobSchema>;
export type PipelineBatch = z.infer<typeof PipelineBatchSchema>;
export type PipelineHealthResponse = z.infer<
  typeof PipelineHealthResponseSchema
>;
export type ApifyWebhook = z.infer<typeof ApifyWebhookSchema>;
export type QStashCallback = z.infer<typeof QStashCallbackSchema>;
export type JobStatusClassifier = z.infer<typeof JobStatusClassifierSchema>;
export type ExtractRequest = z.infer<typeof ExtractRequestSchema>;
export type ExtractJobRequest = z.infer<typeof ExtractJobRequestSchema>;
export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;
export type ValidationError = z.infer<typeof ValidationErrorSchema>;
