/**
 * Central export file for all Zustand stores
 *
 * This file provides a single entry point for accessing all stores and their types
 * in the Bordfeed application. Each store manages a specific domain of the application.
 */

export type { BoardsState } from './boards.store';
// ==========================================
// BOARDS STORE - Job boards and PAT management
// ==========================================
export { useBoardsStore, useBoardsStoreHydrated } from './boards.store';
export type { HealthState } from './health.store';
// ==========================================
// HEALTH STORE - System health monitoring
// ==========================================
export { useHealthStore, useHealthStoreHydrated } from './health.store';
// ==========================================
// UTILITIES - SSR-safe hydration helpers
// ==========================================
export { createHydratedHook, isServer, useHydration } from './hydration';
export type { JobsState } from './jobs.store';
// ==========================================
// JOBS STORE - Jobs listing, filtering, pagination
// ==========================================
export { useJobsStore, useJobsStoreHydrated } from './jobs.store';
export type { SourcesState } from './sources.store';
// ==========================================
// SOURCES STORE - Job sources management
// ==========================================
export { useSourcesStore, useSourcesStoreHydrated } from './sources.store';
// ==========================================
// SHARED TYPES - Common interfaces
// ==========================================
export type {
  JobBoard,
  JobSource,
  LoadingState,
  PaginationState,
} from './types';
export type { UIState } from './ui.store';
// ==========================================
// UI STORE - Global UI state management
// ==========================================
export { useUIStore, useUIStoreHydrated } from './ui.store';
