/**
 * Jobs Store - Manages job listings with advanced filtering and pagination
 */

import type { SortingState } from '@tanstack/react-table';
import { useEffect, useState } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';
import type { DatabaseJob } from '@/lib/storage';
import type { JobFilters } from '@/lib/types/job-filters';
import type { LoadingState, PaginationState } from './types';

/**
 * Pagination Info
 */
interface PaginationInfo extends PaginationState {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

/**
 * SQL Query Info for debugging
 */
interface SqlInfo {
  query?: string;
  executionTime?: number;
  filterCount?: number;
  totalJobs?: number;
  filteredJobs?: number;
}

/**
 * Jobs State Interface
 */
export interface JobsState extends LoadingState {
  // Data
  jobs: DatabaseJob[];

  // Pagination
  pagination: PaginationInfo;

  // Filters and Sorting
  filters: JobFilters;
  sorting: SortingState;

  // Meta
  sqlInfo: SqlInfo;
  selectedJobIds: string[];

  // Actions
  actions: {
    // Fetch jobs with current filters/pagination
    fetch: () => Promise<void>;

    // Set page
    setPage: (page: number) => void;

    // Set page size
    setPageSize: (pageSize: number) => void;

    // Update filters
    setFilters: (filters: Partial<JobFilters>) => void;

    // Reset filters
    resetFilters: () => void;

    // Update sorting
    setSorting: (sorting: SortingState) => void;

    // Process single job
    processJob: (
      jobId: string
    ) => Promise<{ success: boolean; error?: string }>;

    // Batch operations
    processBatch: (
      jobIds: string[]
    ) => Promise<{ success: boolean; processed: number; failed: number }>;
    deleteBatch: (
      jobIds: string[]
    ) => Promise<{ success: boolean; deleted: number }>;
    exportBatch: (
      jobIds: string[]
    ) => Promise<{ success: boolean; data?: DatabaseJob[] }>;

    // Selection
    selectJob: (jobId: string) => void;
    deselectJob: (jobId: string) => void;
    selectAll: () => void;
    deselectAll: () => void;

    // Update single job
    updateJob: (
      jobId: string,
      updates: Partial<DatabaseJob>
    ) => Promise<{ success: boolean; error?: string }>;

    // Clear error
    clearError: () => void;

    // Reset store
    reset: () => void;
  };
}

/**
 * Default filter values
 */
const defaultFilters: JobFilters = {
  search: '',
  status: '',
  processingStatus: '',
  sourceType: '',

  jobTypes: [],
  workplaceTypes: [],
  careerLevels: [],
  salaryMin: undefined,
  salaryMax: undefined,
  salaryCurrencies: [],
  includeKeywords: [],
  excludeKeywords: [],
  countries: [],
  languages: [],
};

/**
 * Initial state
 */
const initialState: Omit<JobsState, 'actions'> = {
  jobs: [],
  pagination: {
    // PaginationState properties
    page: 1,
    pageSize: 20,
    total: 0,
    hasMore: false,
    cursor: null,
    // Additional PaginationInfo properties
    currentPage: 1,
    totalPages: 1,
    totalItems: 0,
    itemsPerPage: 20,
    hasNextPage: false,
    hasPreviousPage: false,
  },
  filters: defaultFilters,
  sorting: [{ id: 'created_at', desc: true }],
  sqlInfo: {},
  selectedJobIds: [],
  loading: false,
  error: null,
};

/**
 * Helper to add filter if it exists
 */
function addFilterParam(
  params: URLSearchParams,
  key: string,
  value: string | undefined
): void {
  if (value) {
    params.set(key, value);
  }
}

/**
 * Helper to add array filter if it has items
 */
function addArrayParam(
  params: URLSearchParams,
  key: string,
  values: string[] | undefined
): void {
  if (values?.length) {
    params.set(key, values.join(','));
  }
}

/**
 * Build API URL with filters, sorting, and pagination
 */
function buildApiUrl(
  page: number,
  pageSize: number,
  filters: JobFilters,
  sorting: SortingState
): string {
  const params = new URLSearchParams();

  // Pagination
  params.set('page', String(page));
  params.set('limit', String(pageSize));

  // Sorting
  if (sorting.length > 0) {
    params.set('sortField', sorting[0].id);
    params.set('sortDirection', sorting[0].desc ? 'desc' : 'asc');
  }

  // Basic filters
  addFilterParam(params, 'search', filters.search);
  addFilterParam(params, 'status', filters.status);
  addFilterParam(params, 'processingStatus', filters.processingStatus);
  addFilterParam(params, 'sourceType', filters.sourceType);

  // Array filters
  addArrayParam(params, 'jobTypes', filters.jobTypes);
  addArrayParam(params, 'workplaceTypes', filters.workplaceTypes);
  addArrayParam(params, 'careerLevels', filters.careerLevels);
  addArrayParam(params, 'countries', filters.countries);
  addArrayParam(params, 'languages', filters.languages);
  addArrayParam(params, 'salaryCurrencies', filters.salaryCurrencies);
  addArrayParam(params, 'includeKeywords', filters.includeKeywords);
  addArrayParam(params, 'excludeKeywords', filters.excludeKeywords);

  // Salary range
  if (filters.salaryMin !== undefined) {
    params.set('salaryMin', String(filters.salaryMin));
  }
  if (filters.salaryMax !== undefined) {
    params.set('salaryMax', String(filters.salaryMax));
  }

  return `/api/jobs?${params.toString()}`;
}

/**
 * Jobs Store with Immer for easier state updates
 */
export const useJobsStore = create<JobsState>()(
  devtools(
    immer((set, get) => ({
      ...initialState,

      actions: {
        // Fetch jobs
        fetch: async () => {
          const state = get();
          const { currentPage, itemsPerPage } = state.pagination;

          set((draft) => {
            draft.loading = true;
            draft.error = null;
          });

          try {
            const url = buildApiUrl(
              currentPage,
              itemsPerPage,
              state.filters,
              state.sorting
            );
            const response = await fetch(url);
            const data = await response.json();

            if (!response.ok) {
              throw new Error(data.error || 'Failed to fetch jobs');
            }

            set((draft) => {
              draft.jobs = data.jobs || [];
              draft.pagination = data.pagination || {
                ...draft.pagination,
                totalItems: 0,
                totalPages: 1,
                hasNextPage: false,
                hasPreviousPage: false,
              };
              draft.sqlInfo = data.sqlInfo || {};
              draft.loading = false;
            });
          } catch (error) {
            set((draft) => {
              draft.loading = false;
              draft.error =
                error instanceof Error ? error.message : 'Unknown error';
              draft.jobs = [];
            });
          }
        },

        // Set page
        setPage: (page) => {
          set((draft) => {
            draft.pagination.currentPage = page;
          });
          // Automatically fetch with new page
          get().actions.fetch();
        },

        // Set page size
        setPageSize: (pageSize) => {
          set((draft) => {
            draft.pagination.itemsPerPage = pageSize;
            draft.pagination.currentPage = 1; // Reset to first page
          });
          // Automatically fetch with new page size
          get().actions.fetch();
        },

        // Update filters
        setFilters: (filters) => {
          set((draft) => {
            draft.filters = { ...draft.filters, ...filters };
            draft.pagination.currentPage = 1; // Reset to first page
          });
          // Note: Don't auto-fetch here, let the component handle it with debounce
        },

        // Reset filters
        resetFilters: () => {
          set((draft) => {
            draft.filters = defaultFilters;
            draft.pagination.currentPage = 1;
          });
          get().actions.fetch();
        },

        // Update sorting
        setSorting: (sorting) => {
          set((draft) => {
            draft.sorting = sorting;
            draft.pagination.currentPage = 1; // Reset to first page
          });
          get().actions.fetch();
        },

        // Process single job
        processJob: async (jobId) => {
          const job = get().jobs.find((j) => j.id === jobId);
          if (!job) {
            return { success: false, error: 'Job not found' };
          }

          // Optimistic update
          set((draft) => {
            const jobIndex = draft.jobs.findIndex((j) => j.id === jobId);
            if (jobIndex !== -1) {
              draft.jobs[jobIndex].processing_status = 'pending';
            }
          });

          try {
            const response = await fetch(`/api/jobs/${jobId}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ processing_status: 'processing' }),
            });

            const data = await response.json();

            if (!response.ok) {
              throw new Error(data.error || 'Failed to process job');
            }

            // Update with server response
            set((draft) => {
              const jobIndex = draft.jobs.findIndex((j) => j.id === jobId);
              if (jobIndex !== -1) {
                draft.jobs[jobIndex] = data.job;
              }
            });

            return { success: true };
          } catch (error) {
            // Revert on error
            set((draft) => {
              const jobIndex = draft.jobs.findIndex((j) => j.id === jobId);
              if (jobIndex !== -1) {
                draft.jobs[jobIndex].processing_status = job.processing_status;
              }
            });

            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        },

        // Process batch
        processBatch: async (jobIds) => {
          if (jobIds.length === 0) {
            return { success: false, processed: 0, failed: 0 };
          }

          try {
            const response = await fetch('/api/workflows/status', {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ batchSize: jobIds.length }),
            });

            const data = await response.json();

            if (!response.ok) {
              throw new Error(data.error || 'Failed to process batch');
            }

            // Refresh jobs list
            await get().actions.fetch();

            return {
              success: true,
              processed: data.stats?.processed || 0,
              failed: data.stats?.failed || 0,
            };
          } catch (_error) {
            return {
              success: false,
              processed: 0,
              failed: jobIds.length,
            };
          }
        },

        // Delete batch
        deleteBatch: async (jobIds) => {
          if (jobIds.length === 0) {
            return { success: false, deleted: 0 };
          }

          // Optimistic update
          const originalJobs = get().jobs;
          set((draft) => {
            draft.jobs = draft.jobs.filter((j) => !jobIds.includes(j.id));
          });

          try {
            const response = await fetch('/api/jobs', {
              method: 'DELETE',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify({ jobIds }),
            });

            const data = await response.json();

            if (!response.ok) {
              throw new Error(data.error || 'Failed to delete jobs');
            }

            // Refresh to get accurate pagination
            await get().actions.fetch();

            return {
              success: true,
              deleted: data.deleted || jobIds.length,
            };
          } catch (_error) {
            // Revert on error
            set((draft) => {
              draft.jobs = originalJobs;
            });

            return { success: false, deleted: 0 };
          }
        },

        // Export batch
        exportBatch: async (jobIds) => {
          if (jobIds.length === 0) {
            return { success: false };
          }

          const selectedJobs = get().jobs.filter((j) => jobIds.includes(j.id));

          // Return selected jobs data for export
          return await Promise.resolve({
            success: true,
            data: selectedJobs,
          });
        },

        // Selection actions
        selectJob: (jobId) => {
          set((draft) => {
            if (!draft.selectedJobIds.includes(jobId)) {
              draft.selectedJobIds.push(jobId);
            }
          });
        },

        deselectJob: (jobId) => {
          set((draft) => {
            draft.selectedJobIds = draft.selectedJobIds.filter(
              (id) => id !== jobId
            );
          });
        },

        selectAll: () => {
          set((draft) => {
            draft.selectedJobIds = draft.jobs.map((j) => j.id);
          });
        },

        deselectAll: () => {
          set((draft) => {
            draft.selectedJobIds = [];
          });
        },

        // Update single job
        updateJob: async (jobId, updates) => {
          const job = get().jobs.find((j) => j.id === jobId);
          if (!job) {
            return { success: false, error: 'Job not found' };
          }

          // Optimistic update
          set((draft) => {
            const jobIndex = draft.jobs.findIndex((j) => j.id === jobId);
            if (jobIndex !== -1) {
              draft.jobs[jobIndex] = { ...draft.jobs[jobIndex], ...updates };
            }
          });

          try {
            const response = await fetch(`/api/jobs/${jobId}`, {
              method: 'PUT',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(updates),
            });

            const data = await response.json();

            if (!response.ok) {
              throw new Error(data.error || 'Failed to update job');
            }

            // Update with server response
            set((draft) => {
              const jobIndex = draft.jobs.findIndex((j) => j.id === jobId);
              if (jobIndex !== -1) {
                draft.jobs[jobIndex] = data.job;
              }
            });

            return { success: true };
          } catch (error) {
            // Revert on error
            set((draft) => {
              const jobIndex = draft.jobs.findIndex((j) => j.id === jobId);
              if (jobIndex !== -1) {
                draft.jobs[jobIndex] = job;
              }
            });

            return {
              success: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            };
          }
        },

        // Clear error
        clearError: () => {
          set((draft) => {
            draft.error = null;
          });
        },

        // Reset store
        reset: () => {
          set(initialState);
        },
      },
    })),
    { name: 'JobsStore' }
  )
);

/**
 * Hydrated hook for SSR-safe usage
 */
export const useJobsStoreHydrated = () => {
  const store = useJobsStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    return {
      ...initialState,
      actions: {
        fetch: async () => {
          // No-op during hydration
        },
        setPage: () => {
          // No-op during hydration
        },
        setPageSize: () => {
          // No-op during hydration
        },
        setFilters: () => {
          // No-op during hydration
        },
        resetFilters: () => {
          // No-op during hydration
        },
        setSorting: () => {
          // No-op during hydration
        },
        processJob: async () => ({ success: false, error: 'Not hydrated' }),
        processBatch: async () => ({ success: false, processed: 0, failed: 0 }),
        deleteBatch: async () => ({ success: false, deleted: 0 }),
        exportBatch: async () => ({ success: false }),
        selectJob: () => {
          // No-op during hydration
        },
        deselectJob: () => {
          // No-op during hydration
        },
        selectAll: () => {
          // No-op during hydration
        },
        deselectAll: () => {
          // No-op during hydration
        },
        updateJob: async () => ({ success: false, error: 'Not hydrated' }),
        clearError: () => {
          // No-op during hydration
        },
        reset: () => {
          // No-op during hydration
        },
      },
    } as JobsState;
  }

  return store;
};
