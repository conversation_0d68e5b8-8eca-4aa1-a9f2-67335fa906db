/**
 * Shared types and interfaces for Zustand stores
 */

export type { JobStatus } from '@/lib/job-status';
// Re-export types that will be used across stores
export type { JobData, JobMetadata } from '@/lib/types';
export type { JobFilters } from '@/lib/types/job-filters';

/**
 * Common store state interfaces
 */
export interface LoadingState {
  loading: boolean;
  error: string | null;
}

export interface PaginationState {
  page: number;
  pageSize: number;
  total: number;
  hasMore: boolean;
  cursor?: string | null;
}

/**
 * JobSource interface for Sources store
 */
export interface JobSource {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  actor_id: string;
  stats: {
    last_fetch_at: string | null;
    last_run_status: string | null;
    last_error: string | null;
  };
  schedule: {
    id: string | null;
    name: string | null;
    title: string | null;
    cronExpression: string | null;
    timezone: string | null;
    isEnabled: boolean;
    nextRunAt: string | null;
    lastRunAt: string | null;
    apifyUrl: string | null;
  };
  config: {
    apify_actor_url: string;
  };
  // Computed properties
  status?: 'active' | 'inactive' | 'error' | 'paused' | 'running';
  total_jobs?: number;
  success_rate?: number;
}

/**
 * JobBoard interface for Boards store
 */
export interface JobBoard {
  id: string;
  name: string;
  description?: string;
  airtable: {
    baseId: string;
    tableName: string;
  };
  patStatus?: 'configured' | 'missing' | 'invalid';
  lastSync?: string | null;
  created_at?: string;
  updated_at?: string;
}

/**
 * Health metrics interfaces
 */
export interface SystemHealth {
  status: 'healthy' | 'degraded' | 'down';
  database: boolean;
  apify: boolean;
  airtable: boolean;
  supabase: boolean;
  timestamp: string;
}

export interface HealthMetrics {
  systemHealth?: SystemHealth;
  sourcesHealth?: {
    success: boolean;
    sources?: JobSource[];
  };
  jobStats?: {
    totalJobs: number;
    processingStats: {
      pending: number;
      processing: number;
      completed: number;
      failed: number;
    };
  };
  deduplicationData?: {
    success: boolean;
    stats?: {
      totalJobs: number;
      duplicates: number;
      unique: number;
    };
  };
  lastUpdated: string;
}

/**
 * UI State interfaces
 */
export interface ModalState {
  isOpen: boolean;
  type: 'create' | 'edit' | 'delete' | 'confirm' | null;
  data?: unknown;
}

export interface ToastState {
  id: string;
  type: 'success' | 'error' | 'info' | 'warning';
  message: string;
  duration?: number;
}

/**
 * Real-time subscription types
 */
export interface RealtimeSubscription {
  channel: string;
  unsubscribe: () => void;
}

/**
 * Store action result types
 */
export interface ActionResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: string;
}

/**
 * Common store actions interface
 */
export interface StoreActions<_T> {
  fetch: () => Promise<void>;
  reset: () => void;
  setError: (error: string | null) => void;
}
