/**
 * Custom middleware for Zustand stores
 */

import type { StateCreator, StoreMutatorIdentifier } from 'zustand';

type Logger = <
  T,
  M<PERSON> extends [StoreMutatorIdentifier, unknown][] = [],
  M<PERSON> extends [StoreMutatorIdentifier, unknown][] = [],
>(
  f: StateCreator<T, Mps, Mcs>,
  name?: string
) => StateCreator<T, Mps, Mcs>;

type LoggerImpl = <T>(
  f: StateCreator<T, [], []>,
  name?: string
) => StateCreator<T, [], []>;

/**
 * Logger middleware for development
 * Logs all state changes with action names and timing
 *
 * @example
 * ```ts
 * const useStore = create<StoreState>()(
 *   logger(
 *     (set) => ({
 *       // state and actions
 *     }),
 *     'StoreName'
 *   )
 * );
 * ```
 */
const loggerImpl: LoggerImpl = (f, name) => (set, get, store) => {
  const loggedSet: typeof set = (...args) => {
    const startTime = Date.now();
    set(...(args as Parameters<typeof set>));
    const _duration = Date.now() - startTime;

    // Development logging can be enabled by modifying this condition
    if (process.env.NODE_ENV === 'development' && name) {
      // Logging would be implemented here when needed
    }
  };

  return f(loggedSet, get, store);
};

export const logger = loggerImpl as unknown as Logger;

/**
 * Performance monitoring middleware
 * Tracks slow state updates and warns when threshold is exceeded
 */
type PerformanceMonitor = <
  T,
  Mps extends [StoreMutatorIdentifier, unknown][] = [],
  Mcs extends [StoreMutatorIdentifier, unknown][] = [],
>(
  f: StateCreator<T, Mps, Mcs>,
  options?: PerformanceOptions
) => StateCreator<T, Mps, Mcs>;

interface PerformanceOptions {
  threshold?: number; // ms
  logSlowUpdates?: boolean;
  trackMetrics?: boolean;
}

// Helper function to log slow updates
function logSlowUpdate(duration: number, threshold: number, storeName: string) {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const warning = `⚠️ Slow store update in ${storeName}: ${duration.toFixed(2)}ms (threshold: ${threshold}ms)`;

  // Store warning in global for debugging
  if (typeof window !== 'undefined') {
    (
      window as Window & { __ZUSTAND_WARNINGS__?: string[] }
    ).__ZUSTAND_WARNINGS__ = [
      ...((window as Window & { __ZUSTAND_WARNINGS__?: string[] })
        .__ZUSTAND_WARNINGS__ || []),
      warning,
    ];
  }
}

// Helper function to track metrics
function trackPerformanceMetrics(
  duration: number,
  threshold: number,
  storeName: string
) {
  if (process.env.NODE_ENV !== 'development') {
    return;
  }

  const perfData = {
    storeName,
    duration: Math.round(duration),
    threshold,
    timestamp: Date.now(),
  };

  const existingLogs = JSON.parse(
    localStorage.getItem('bordfeed_perf_logs') || '[]'
  );
  existingLogs.push(perfData);

  // Keep only last 100 entries
  if (existingLogs.length > 100) {
    existingLogs.splice(0, existingLogs.length - 100);
  }

  localStorage.setItem('bordfeed_perf_logs', JSON.stringify(existingLogs));
}

const performanceMonitorImpl =
  <T>(f: StateCreator<T, [], []>, options: PerformanceOptions = {}) =>
  (
    set: Parameters<StateCreator<T, [], []>>[0],
    get: Parameters<StateCreator<T, [], []>>[1],
    store: Parameters<StateCreator<T, [], []>>[2]
  ) => {
    const {
      threshold = 100,
      logSlowUpdates = true,
      trackMetrics = true,
    } = options;

    const timedSet: typeof set = (...args) => {
      const startTime = performance.now();
      set(...(args as Parameters<typeof set>));
      const duration = performance.now() - startTime;

      if (duration > threshold) {
        const storeName = 'UnnamedStore'; // Store name not available at runtime

        if (logSlowUpdates) {
          logSlowUpdate(duration, threshold, storeName);
        }

        if (trackMetrics) {
          trackPerformanceMetrics(duration, threshold, storeName);
        }
      }
    };

    return f(timedSet, get, store);
  };

export const performanceMonitor =
  performanceMonitorImpl as unknown as PerformanceMonitor;

/**
 * Persistence middleware
 * Automatically saves and restores state from localStorage
 */
type Persist = <
  T,
  Mps extends [StoreMutatorIdentifier, unknown][] = [],
  Mcs extends [StoreMutatorIdentifier, unknown][] = [],
>(
  f: StateCreator<T, Mps, Mcs>,
  options: PersistOptions<T>
) => StateCreator<T, Mps, Mcs>;

interface PersistOptions<T> {
  name: string;
  storage?: Storage;
  serialize?: (state: T) => string;
  deserialize?: (str: string) => T;
  partialize?: (state: T) => Partial<T>;
  whitelist?: (keyof T)[];
  blacklist?: (keyof T)[];
}

const persistImpl =
  <T>(f: StateCreator<T, [], []>, options: PersistOptions<T>) =>
  (
    set: Parameters<StateCreator<T, [], []>>[0],
    get: Parameters<StateCreator<T, [], []>>[1],
    store: Parameters<StateCreator<T, [], []>>[2]
  ) => {
    const {
      name,
      storage = localStorage,
      serialize = JSON.stringify,
      deserialize = JSON.parse,
      partialize,
    } = options;

    // Load initial state from storage
    let initialState: Partial<T> | undefined;
    try {
      const storedState = storage.getItem(name);
      if (storedState) {
        initialState = deserialize(storedState);
      }
    } catch {
      // Ignore storage errors
    }

    const persistedSet: typeof set = (...args) => {
      set(...(args as Parameters<typeof set>));

      // Save to storage after state update
      try {
        const state = get();
        const stateToStore = partialize ? partialize(state) : state;
        storage.setItem(name, serialize(stateToStore as T));
      } catch {
        // Ignore storage errors
      }
    };

    const result = f(persistedSet, get, store);

    // Apply initial state if available
    if (initialState) {
      set((state) => ({ ...state, ...initialState }) as T);
    }

    return result;
  };

export const persist = persistImpl as unknown as Persist;

/**
 * Combine multiple middlewares
 * Applies middlewares in right-to-left order (like function composition)
 */
export function combineMiddlewares<T>(
  ...middlewares: Array<
    (config: StateCreator<T, [], []>) => StateCreator<T, [], []>
  >
) {
  return (config: StateCreator<T, [], []>) =>
    middlewares.reduceRight((acc, middleware) => middleware(acc), config);
}

/**
 * Error boundary middleware
 * Catches errors in state updates and provides recovery
 *
 * @example
 * ```ts
 * const useStore = create<StoreState>()(
 *   errorBoundary(
 *     (set) => ({
 *       // state and actions
 *     }),
 *     {
 *       onError: (error) => console.error('Store error:', error),
 *       fallbackState: {}
 *     }
 *   )
 * );
 * ```
 */
interface ErrorBoundaryOptions<T> {
  onError?: (error: Error, action: unknown) => void;
  fallbackState?: Partial<T>;
  resetOnError?: boolean;
}

type ErrorBoundary = <
  T,
  Mps extends [StoreMutatorIdentifier, unknown][] = [],
  Mcs extends [StoreMutatorIdentifier, unknown][] = [],
>(
  f: StateCreator<T, Mps, Mcs>,
  options: ErrorBoundaryOptions<T>
) => StateCreator<T, Mps, Mcs>;

const errorBoundaryImpl =
  <T>(f: StateCreator<T, [], []>, options: ErrorBoundaryOptions<T>) =>
  (
    set: Parameters<StateCreator<T, [], []>>[0],
    get: Parameters<StateCreator<T, [], []>>[1],
    store: Parameters<StateCreator<T, [], []>>[2]
  ) => {
    const { onError, fallbackState, resetOnError = false } = options;

    const safeSet: typeof set = (...args) => {
      try {
        set(...(args as Parameters<typeof set>));
      } catch (err) {
        const error = err instanceof Error ? err : new Error(String(err));

        onError?.(error, args[0]);

        if (resetOnError && fallbackState) {
          set((state) => ({ ...state, ...fallbackState }) as T);
        }
      }
    };

    return f(safeSet, get, store);
  };

export const errorBoundary = errorBoundaryImpl as unknown as ErrorBoundary;

/**
 * Development-only middleware that provides enhanced debugging
 * Only active in development mode
 */
export const devtools = <T>(f: StateCreator<T, [], []>, name?: string) => {
  if (process.env.NODE_ENV !== 'development') {
    return f;
  }

  return logger(f, name);
};
