/**
 * Sources Store - Manages job source data and operations
 */

import { useEffect, useState } from 'react';
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';
import { createClient } from '@/lib/supabase';
import type { JobSource, LoadingState } from './types';

// Simple API response type
interface APIResponse {
  success: boolean;
  message?: string;
  error?: string;
}

/**
 * Sources State Interface
 */
export interface SourcesState extends LoadingState {
  // Data
  sources: JobSource[];
  lastFetch: Date | null;

  // Actions
  actions: {
    // Fetch all sources
    fetch: () => Promise<void>;

    // Update a single source
    update: (id: string, updates: Partial<JobSource>) => void;

    // Test source connection
    testConnection: (id: string) => Promise<APIResponse>;

    // Run a source
    runSource: (id: string) => Promise<APIResponse>;

    // Subscribe to real-time updates
    subscribeToRealtime: () => () => void;

    // Clear error
    clearError: () => void;

    // Reset store
    reset: () => void;
  };
}

/**
 * Transform stats object
 */
function transformStats(
  stats: Record<string, unknown> | undefined
): JobSource['stats'] {
  return {
    last_fetch_at: (stats?.last_fetch_at as string | null) || null,
    last_run_status: (stats?.last_run_status as string | null) || null,
    last_error: (stats?.last_error as string | null) || null,
  };
}

/**
 * Transform Apify source data to match our JobSource interface
 */
function transformApifySource(source: Record<string, unknown>): JobSource {
  const stats = source.stats as Record<string, unknown> | undefined;
  const isEnabled = Boolean(source.enabled);
  const transformedStats = transformStats(stats);

  return {
    id: String(source.id),
    name: String(source.name),
    description: String(source.description || ''),
    enabled: isEnabled,
    actor_id: String(source.actor_id || ''),
    stats: transformedStats,
    status: isEnabled ? 'active' : 'inactive',
    total_jobs: 0, // Computed property - set default value
    success_rate: 0, // Computed property - set default value
    schedule: source.schedule as JobSource['schedule'],
    config: source.config as JobSource['config'],
  };
}

/**
 * Initial state
 */
const initialState: Omit<SourcesState, 'actions'> = {
  sources: [],
  loading: false,
  error: null,
  lastFetch: null,
};

/**
 * Sources Store
 */
export const useSourcesStore = create<SourcesState>()(
  devtools(
    (set, get) => ({
      ...initialState,

      actions: {
        // Fetch all sources
        fetch: async () => {
          set({ loading: true, error: null });

          try {
            const response = await fetch('/api/sources');
            const data = await response.json();

            if (!(response.ok && data.success)) {
              throw new Error(data.error || 'Failed to fetch sources');
            }

            const sources = data.sources.map(transformApifySource);

            set({
              sources,
              loading: false,
              lastFetch: new Date(),
            });
          } catch (error) {
            set({
              loading: false,
              error: error instanceof Error ? error.message : 'Unknown error',
            });
          }
        },

        // Update a single source
        update: (id, updates) => {
          set((state) => ({
            sources: state.sources.map((source) =>
              source.id === id ? { ...source, ...updates } : source
            ),
          }));
        },

        // Test source connection
        testConnection: async (id) => {
          const source = get().sources.find((s) => s.id === id);
          if (!source) {
            return {
              success: false,
              error: 'Source not found',
            };
          }

          // Optimistic update
          get().actions.update(id, { status: 'running' });

          try {
            const healthEndpoint = `/api/sources/${id}/health`;
            const response = await fetch(healthEndpoint);
            const data = await response.json();

            const status = data.success ? 'active' : 'error';
            get().actions.update(id, {
              status,
              stats: {
                ...source.stats,
                last_run_status: status,
                last_error: data.error || null,
                last_fetch_at: new Date().toISOString(),
              },
            });

            return data;
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';

            get().actions.update(id, {
              status: 'error',
              stats: {
                ...source.stats,
                last_run_status: 'error',
                last_error: errorMessage,
              },
            });

            return {
              success: false,
              error: errorMessage,
            };
          }
        },

        // Run a source
        runSource: async (id) => {
          const source = get().sources.find((s) => s.id === id);
          if (!source) {
            return {
              success: false,
              error: 'Source not found',
            };
          }

          // Optimistic update
          get().actions.update(id, { status: 'running' });

          try {
            const runEndpoint = `/api/sources/${id}/run`;
            const response = await fetch(runEndpoint, {
              method: 'POST',
            });
            const data = await response.json();

            if (data.success) {
              get().actions.update(id, {
                status: 'active',
                stats: {
                  ...source.stats,
                  last_run_status: 'success',
                  last_fetch_at: new Date().toISOString(),
                },
              });
            } else {
              get().actions.update(id, {
                status: 'error',
                stats: {
                  ...source.stats,
                  last_run_status: 'error',
                  last_error: data.error || 'Failed to run source',
                },
              });
            }

            return data;
          } catch (error) {
            const errorMessage =
              error instanceof Error ? error.message : 'Unknown error';

            get().actions.update(id, {
              status: 'error',
              stats: {
                ...source.stats,
                last_run_status: 'error',
                last_error: errorMessage,
              },
            });

            return {
              success: false,
              error: errorMessage,
            };
          }
        },

        // Subscribe to real-time updates
        subscribeToRealtime: () => {
          const supabase = createClient();

          const subscription = supabase
            .channel('job_sources_changes')
            .on(
              'postgres_changes',
              {
                event: '*',
                schema: 'public',
                table: 'job_sources',
              },
              (payload) => {
                if (payload.eventType === 'UPDATE' && payload.new) {
                  get().actions.update(payload.new.id, payload.new);
                } else if (payload.eventType === 'INSERT' && payload.new) {
                  set((state) => ({
                    sources: [
                      ...state.sources,
                      transformApifySource(payload.new),
                    ],
                  }));
                } else if (payload.eventType === 'DELETE' && payload.old) {
                  set((state) => ({
                    sources: state.sources.filter(
                      (s) => s.id !== payload.old.id
                    ),
                  }));
                }
              }
            )
            .on(
              'postgres_changes',
              {
                event: '*',
                schema: 'public',
                table: 'job_source_stats',
              },
              (payload) => {
                const newData = payload.new as Record<string, unknown> | null;
                if (
                  newData?.source_id &&
                  typeof newData.source_id === 'string'
                ) {
                  const source = get().sources.find(
                    (s) => s.id === newData.source_id
                  );
                  if (source) {
                    get().actions.update(newData.source_id, {
                      stats: {
                        ...source.stats,
                        last_fetch_at:
                          (newData.last_fetch_at as string | null) ||
                          source.stats.last_fetch_at,
                        last_run_status:
                          (newData.last_run_status as string | null) ||
                          source.stats.last_run_status,
                        last_error:
                          (newData.last_error as string | null) ||
                          source.stats.last_error,
                      },
                    });
                  }
                }
              }
            )
            .subscribe();

          // Return cleanup function
          return () => {
            subscription.unsubscribe();
          };
        },

        // Clear error
        clearError: () => {
          set({ error: null });
        },

        // Reset store
        reset: () => {
          set(initialState);
        },
      },
    }),
    { name: 'SourcesStore' }
  )
);

/**
 * Hydrated hook for SSR-safe usage
 * Returns default values during SSR and actual store values after hydration
 */
export const useSourcesStoreHydrated = () => {
  const store = useSourcesStore();
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    return {
      sources: [],
      loading: false,
      error: null,
      lastFetch: null,
      actions: {
        fetch: async () => {
          // No-op during hydration
        },
        update: () => {
          // No-op during hydration
        },
        testConnection: async () => ({ success: false, error: 'Not hydrated' }),
        runSource: async () => ({ success: false, error: 'Not hydrated' }),
        subscribeToRealtime: () => () => {
          // No-op during hydration
        },
        clearError: () => {
          // No-op during hydration
        },
        reset: () => {
          // No-op during hydration
        },
      },
    } as SourcesState;
  }

  return store;
};
