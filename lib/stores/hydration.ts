/**
 * Hydration utilities for SSR-safe Zustand stores in Next.js
 */

'use client';

import { useEffect, useState } from 'react';

/**
 * Hook to check if the app has been hydrated on the client
 * This prevents hydration mismatches when using Zustand stores with Next.js SSR
 *
 * @example
 * ```tsx
 * const isHydrated = useHydration();
 * const value = useStore(state => state.value);
 *
 * if (!isHydrated) {
 *   return <div>Loading...</div>;
 * }
 *
 * return <div>{value}</div>;
 * ```
 */
export function useHydration() {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated;
}

/**
 * Higher-order hook that combines a Zustand store hook with hydration check
 * Returns a default value during SSR and the actual store value after hydration
 *
 * @example
 * ```tsx
 * const useSourcesStoreHydrated = createHydratedHook(
 *   useSourcesStore,
 *   { sources: [], loading: true }
 * );
 * ```
 */
export function createHydratedHook<T, S>(
  useStore: (selector: (state: T) => S) => S,
  defaultValue: S
) {
  return function useHydratedStore(selector: (state: T) => S): S {
    const [isHydrated, setIsHydrated] = useState(false);
    const storeValue = useStore(selector);

    useEffect(() => {
      setIsHydrated(true);
    }, []);

    return isHydrated ? storeValue : defaultValue;
  };
}

/**
 * Component wrapper that delays rendering until hydration is complete
 * Useful for wrapping components that heavily depend on client-side state
 *
 * @example
 * ```tsx
 * <HydrationBoundary fallback={<Skeleton />}>
 *   <ComponentThatUsesStore />
 * </HydrationBoundary>
 * ```
 */
export function HydrationBoundary({
  children,
  fallback = null,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) {
  const isHydrated = useHydration();

  if (!isHydrated) {
    return fallback;
  }

  return children;
}

/**
 * Hook that provides hydration-safe access to multiple store values
 * Useful when you need to select multiple values from a store
 *
 * @example
 * ```tsx
 * const { sources, loading } = useHydratedValues(
 *   useSourcesStore,
 *   (state) => ({ sources: state.sources, loading: state.loading }),
 *   { sources: [], loading: true }
 * );
 * ```
 */
export function useHydratedValues<T, S extends Record<string, unknown>>(
  useStoreHook: (selectorFn: (state: T) => S) => S,
  selector: (state: T) => S,
  defaultValues: S
): S {
  const [isHydrated, setIsHydrated] = useState(false);
  const values = useStoreHook(selector);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  return isHydrated ? values : defaultValues;
}

/**
 * Utility to check if code is running on the server
 */
export const isServer = typeof window === 'undefined';

/**
 * Utility to safely access window object
 */
export const safeWindow = isServer ? undefined : window;

/**
 * Hook that only runs a callback after hydration
 * Useful for side effects that depend on client-side state
 *
 * @example
 * ```tsx
 * useAfterHydration(() => {
 *   // This will only run on the client after hydration
 *   console.log('App is hydrated!');
 * });
 * ```
 */
export function useAfterHydration(callback: () => undefined | (() => void)) {
  const isHydrated = useHydration();

  useEffect(() => {
    if (isHydrated) {
      return callback();
    }
  }, [isHydrated, callback]);
}
