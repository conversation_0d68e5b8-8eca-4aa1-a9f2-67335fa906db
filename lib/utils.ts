import { type ClassValue, clsx } from 'clsx';
import { twMerge } from 'tailwind-merge';

// Shadcn/ui utility function for combining classes
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}

export {
  type CostBreakdown,
  calculateCost,
  calculateCostBreakdown,
  type TokenUsage,
} from './cost-calculator';
// Re-export from split modules for backward compatibility
export { logger } from './logger';
export { generateMetadata } from './metadata';
export { estimateReadingTime } from './text-utils';

/**
 * Get formatted memory usage in MB
 * @returns Object with heapUsed and heapTotal in MB as strings
 */
export function getMemoryUsage(): { heapUsed: string; heapTotal: string } {
  const usage = process.memoryUsage();
  return {
    heapUsed: `${usage.heapUsed / 1024 / 1024}MB`,
    heapTotal: `${usage.heapTotal / 1024 / 1024}MB`,
  };
}
