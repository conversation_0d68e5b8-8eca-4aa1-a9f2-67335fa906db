import { z } from 'zod';
import { INPUT_LIMITS } from './constants';

// Schema for extract API request
export const ExtractRequestSchema = z.object({
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
  sourceUrl: z.string().url().optional(),
});

// Airtable validation utilities
const AIRTABLE_BASE_ID_REGEX = /^app[A-Za-z0-9]{14}$/;
const AIRTABLE_PAT_REGEX = /^pat[A-Za-z0-9.]{40,}$/;

/**
 * Validates Airtable base ID format
 * Valid format: app + 14 alphanumeric characters (e.g., apprhCjWTxfG3JX5p)
 */
export function validateAirtableBaseId(baseId: string): boolean {
  return AIRTABLE_BASE_ID_REGEX.test(baseId);
}

/**
 * Validates Airtable PAT token format
 * Valid format: pat + 40+ characters
 */
export function validateAirtablePat(pat: string): boolean {
  return AIRTABLE_PAT_REGEX.test(pat);
}

/**
 * Validates Airtable table name
 * Must be non-empty and reasonable length
 */
export function validateAirtableTableName(tableName: string): boolean {
  return tableName.length > 0 && tableName.length <= 100;
}

/**
 * Validates PAT and returns detailed error if invalid
 * Combines validation + error message formatting for DRY principle
 */
export function validatePatWithError(pat: string): {
  valid: boolean;
  error?: string;
} {
  if (!pat?.trim()) {
    return { valid: false, error: 'PAT token is required' };
  }

  if (!validateAirtablePat(pat)) {
    return {
      valid: false,
      error:
        'Invalid PAT format. PATs should start with "pat" and be at least 20 characters',
    };
  }

  return { valid: true };
}

/**
 * Validates Airtable configuration and returns detailed errors
 */
export function validateAirtableConfigWithErrors(config: {
  baseId?: string;
  tableName?: string;
  pat?: string;
}): { valid: boolean; errors: string[] } {
  const errors: string[] = [];

  if (!config.baseId) {
    errors.push('Base ID is required');
  } else if (!validateAirtableBaseId(config.baseId)) {
    errors.push(
      'Base ID format is invalid (must be "app" + 14 alphanumeric characters)'
    );
  }

  if (!config.tableName) {
    errors.push('Table name is required');
  } else if (!validateAirtableTableName(config.tableName)) {
    errors.push('Table name is invalid (must be 1-100 characters)');
  }

  const patValidation = validatePatWithError(config.pat || '');
  if (!patValidation.valid && patValidation.error) {
    errors.push(patValidation.error);
  }

  return { valid: errors.length === 0, errors };
}

// Extract job request schema - updated to support updating existing jobs
export const ExtractJobRequestSchema = z.object({
  jobId: z.string().uuid().optional(), // If provided, update existing job
  sourcedAt: z.string().datetime().optional(),
  sourceUrl: z.string().url().optional(),
  content: z
    .string()
    .min(1, 'Content is required')
    .max(
      INPUT_LIMITS.CONTENT_MAX_CHARS,
      `Content must be less than ${INPUT_LIMITS.CONTENT_MAX_CHARS} characters`
    ),
});

export type ExtractJobRequest = z.infer<typeof ExtractJobRequestSchema>;
