import type { JobData } from './types';

/**
 * Field mapping configurations for different systems
 */

// Mapping from JobData (camelCase) to database fields (snake_case)
export const JOB_DATA_TO_DB_FIELD_MAP = {
  sourcedAt: 'sourced_at',
  sourceUrl: 'source_url',
  // Other fields remain the same
} as const;

/**
 * Generic field mapper that can handle different naming conventions
 */
export function mapFields<T extends Record<string, unknown>>(
  source: T,
  fieldMap: Record<string, string> = {}
): Record<string, unknown> {
  const result: Record<string, unknown> = {};

  for (const [key, value] of Object.entries(source)) {
    // Skip null/undefined values unless explicitly handling them
    if (value === undefined) {
      continue;
    }

    // Use mapped field name or original
    const targetKey = fieldMap[key] || key;
    result[targetKey] = value;
  }

  return result;
}

/**
 * Map JobData fields to database snake_case format
 */
// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex field mapping with validation and transformation logic is necessary
export function mapJobDataToDbFields(
  jobData: Partial<JobData>
): Record<string, unknown> {
  // Apply field name transformations
  const mapped = mapFields(jobData, JOB_DATA_TO_DB_FIELD_MAP);

  // Ensure all expected fields are included (even if null)
  return {
    sourced_at: mapped.sourced_at ?? null,
    source_url: mapped.source_url ?? null,
    title: mapped.title ?? null,
    company: mapped.company ?? null,
    type: mapped.type ?? null,
    description: mapped.description ?? null,
    apply_url: mapped.apply_url ?? null,
    apply_method: mapped.apply_method ?? null,
    posted_date: mapped.posted_date ?? null,
    status: mapped.status ?? null,
    salary_min: mapped.salary_min ?? null,
    salary_max: mapped.salary_max ?? null,
    salary_currency: mapped.salary_currency ?? null,
    salary_unit: mapped.salary_unit ?? null,
    workplace_type: mapped.workplace_type ?? null,
    remote_region: mapped.remote_region ?? null,
    timezone_requirements: mapped.timezone_requirements ?? null,
    workplace_city: mapped.workplace_city ?? null,
    workplace_country: mapped.workplace_country ?? null,
    benefits: mapped.benefits ?? null,
    application_requirements: mapped.application_requirements ?? null,
    valid_through: mapped.valid_through ?? null,
    job_identifier: mapped.job_identifier ?? null,
    job_source_name: mapped.job_source_name ?? null,
    department: mapped.department ?? null,
    travel_required: mapped.travel_required ?? null,
    career_level: mapped.career_level ?? null,
    visa_sponsorship: mapped.visa_sponsorship ?? null,
    languages: mapped.languages ?? null,
    skills: mapped.skills ?? null,
    qualifications: mapped.qualifications ?? null,
    education_requirements: mapped.education_requirements ?? null,
    experience_requirements: mapped.experience_requirements ?? null,
    responsibilities: mapped.responsibilities ?? null,
    featured: mapped.featured ?? null,
    industry: mapped.industry ?? null,
    occupational_category: mapped.occupational_category ?? null,
  };
}

/**
 * Filter object to only include specified fields
 */
export function filterFields<T extends Record<string, unknown>>(
  source: T,
  allowedFields: readonly string[]
): Partial<T> {
  const result: Partial<T> = {};

  for (const field of allowedFields) {
    if (field in source) {
      result[field as keyof T] = source[field as keyof T];
    }
  }

  return result;
}

/**
 * Check if all required fields are present
 */
export function hasRequiredFields(
  data: Record<string, unknown>,
  requiredFields: readonly string[]
): { valid: boolean; missing: string[] } {
  const missing = requiredFields.filter(
    (field) => !(field in data) || data[field] === undefined
  );

  return {
    valid: missing.length === 0,
    missing,
  };
}
