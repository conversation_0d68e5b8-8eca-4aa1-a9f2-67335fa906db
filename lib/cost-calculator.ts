import { AI_CONFIG, DEBUG_MODE } from './constants';
import { logger } from './logger';

export interface TokenUsage {
  inputTokens?: number;
  outputTokens?: number;
  totalTokens?: number;
}

export interface CostBreakdown {
  input: number;
  output: number;
  total: number;
}

/**
 * Calculate cost breakdown from token usage
 */
export function calculateCost(
  inputTokens = 0,
  outputTokens = 0,
  model: string = AI_CONFIG.MODEL
): {
  inputTokens: number;
  outputTokens: number;
  inputCostPerMillion: number;
  outputCostPerMillion: number;
  totalCost: number;
} {
  const modelKey = model as keyof typeof AI_CONFIG.PRICING;
  const pricing = AI_CONFIG.PRICING[modelKey];

  if (!pricing) {
    throw new Error(`Unknown model: ${model}`);
  }

  const inputCostPerMillion = pricing.input;
  const outputCostPerMillion = pricing.output;

  const inputCost = (inputTokens / 1_000_000) * inputCostPerMillion;
  const outputCost = (outputTokens / 1_000_000) * outputCostPerMillion;
  const totalCost = inputCost + outputCost;

  if (DEBUG_MODE) {
    logger.log(`Cost calculation for model ${model}:`, {
      inputTokens,
      outputTokens,
      inputCostPerMillion,
      outputCostPerMillion,
      totalCost,
    });
  }

  return {
    inputTokens,
    outputTokens,
    inputCostPerMillion,
    outputCostPerMillion,
    totalCost,
  };
}

/**
 * Calculate cost breakdown for frontend display
 */
export function calculateCostBreakdown(
  inputTokens = 0,
  outputTokens = 0,
  model: string = AI_CONFIG.MODEL
): CostBreakdown {
  const modelKey = model as keyof typeof AI_CONFIG.PRICING;
  const pricing = AI_CONFIG.PRICING[modelKey];

  if (!pricing) {
    // Unknown model (e.g., 'heuristic') – treat as zero-cost
    return { input: 0, output: 0, total: 0 } satisfies CostBreakdown;
  }

  const input = (inputTokens / 1_000_000) * pricing.input;
  const output = (outputTokens / 1_000_000) * pricing.output;
  const total = input + output;

  return { input, output, total };
}
