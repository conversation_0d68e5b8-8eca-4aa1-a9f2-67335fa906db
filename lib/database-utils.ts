import { createClient } from './supabase';
import { logger } from './utils';

/**
 * Inserts jobs to Supabase database with conflict handling
 * Used by webhook endpoints for bulk job insertion
 *
 * Extracted from duplicated code in:
 * - app/api/jobdata-webhook/route.ts
 * - app/api/workable-webhook/route.ts
 * - app/api/wwr-webhook/route.ts
 */
export async function insertJobsToDatabase(
  jobsToInsert: Record<string, unknown>[]
): Promise<number> {
  if (jobsToInsert.length === 0) {
    return 0;
  }

  const supabase = createClient();
  const { data, error } = await supabase
    .from('jobs')
    .upsert(jobsToInsert, {
      onConflict: 'external_id',
      ignoreDuplicates: true,
    })
    .select('id');

  if (error) {
    logger.error('Failed to insert jobs', { error });
    throw error;
  }

  return data?.length || 0;
}

/**
 * Records a job board posting in the database
 * Used by job posting schedulers
 *
 * Consolidates similar patterns from:
 * - lib/simple-job-scheduler.ts
 * - lib/job-posting-scheduler.ts
 */
export async function recordJobBoardPosting(
  jobId: string,
  boardId: string,
  status: 'posted' | 'failed',
  airtableRecordId?: string,
  errorMessage?: string
): Promise<void> {
  const supabase = createClient();

  await supabase.from('job_board_postings').insert({
    job_id: jobId,
    board_id: boardId,
    posted_at: new Date().toISOString(),
    status,
    airtable_record_id: airtableRecordId,
    error_message: errorMessage,
  });
}
