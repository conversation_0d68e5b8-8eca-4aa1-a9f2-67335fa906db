import { SLACK_CONFIG } from './constants';
import { getNotificationSettings } from './notification-settings';

export type AlertLevel = 'critical' | 'warning' | 'info';

export type PipelineStep =
  | 'SOURCED'
  | 'DEDUPED'
  | 'QUEUED'
  | 'PROCESSED'
  | 'STORED'
  | 'MONITORED'
  | 'SYNCED';

export interface PipelineContext {
  step: PipelineStep;
  source?: string;
  jobCount?: number;
  batchId?: string;
  duration?: number;
  success?: boolean;
  error?: string;
  stats?: Record<string, unknown>;
  metadata?: Record<string, unknown>;
}

export interface Notifier {
  critical(message: string, details?: unknown): Promise<void> | void;
  alert(message: string, details?: unknown): Promise<void> | void;
  info(message: string, details?: unknown): Promise<void> | void;
  pipeline(context: PipelineContext): Promise<void> | void;
}

class NoopNotifier implements Notifier {
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  critical(_message: string, _details?: unknown): void {}
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  alert(_message: string, _details?: unknown): void {}
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  info(_message: string, _details?: unknown): void {}
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  pipeline(_context: PipelineContext): void {}
}

class SlackNotifier implements Notifier {
  private webhookUrl: string;

  constructor(webhookUrl: string) {
    this.webhookUrl = webhookUrl;
  }

  async critical(message: string, details?: unknown): Promise<void> {
    const settings = await getNotificationSettings();
    if (!settings.enableCritical) {
      return;
    }
    const fullMessage = details
      ? `${message}\n\nDetails: ${JSON.stringify(details, null, 2)}`
      : message;
    await this.send(fullMessage, 'critical');
  }

  async alert(message: string, details?: unknown): Promise<void> {
    const settings = await getNotificationSettings();
    if (!settings.enableAlert) {
      return;
    }
    const fullMessage = details
      ? `${message}\n\nDetails: ${JSON.stringify(details, null, 2)}`
      : message;
    await this.send(fullMessage, 'warning');
  }

  async info(message: string, details?: unknown): Promise<void> {
    const settings = await getNotificationSettings();
    if (!settings.enableInfo) {
      return;
    }
    const fullMessage = details
      ? `${message}\n\nDetails: ${JSON.stringify(details, null, 2)}`
      : message;
    await this.send(fullMessage, 'info');
  }

  async pipeline(context: PipelineContext): Promise<void> {
    const settings = await getNotificationSettings();
    if (!settings.enablePipeline) {
      return;
    }

    if (
      settings.pipelineOnlyFailures &&
      context.success !== undefined &&
      context.success
    ) {
      return;
    }

    if (
      Array.isArray(settings.pipelineSteps) &&
      settings.pipelineSteps.length > 0 &&
      !settings.pipelineSteps.includes(context.step)
    ) {
      return;
    }

    const stepEmojis: Record<string, string> = {
      SOURCED: '📥',
      DEDUPED: '🔍',
      QUEUED: '📤',
      PROCESSED: '🧠',
      STORED: '💾',
      MONITORED: '👁️',
      SYNCED: '🔄',
    };

    const emoji = stepEmojis[context.step] || '⚙️';
    const timestamp = new Date().toISOString();

    let message = `${emoji} Pipeline Step: ${context.step}`;
    if (context.source) {
      message += ` | Source: ${context.source}`;
    }
    if (context.jobCount !== undefined) {
      message += ` | Jobs: ${context.jobCount}`;
    }
    if (context.duration !== undefined) {
      message += ` | Duration: ${context.duration}ms`;
    }
    if (context.success !== undefined) {
      message += ` | ${context.success ? '✅ Success' : '❌ Failed'}`;
    }

    const details: Record<string, unknown> = { timestamp, step: context.step };
    if (context.batchId) {
      details.batchId = context.batchId;
    }
    if (context.error) {
      details.error = context.error;
    }
    if (context.stats) {
      details.stats = context.stats;
    }
    if (context.metadata) {
      details.metadata = context.metadata;
    }

    const slackMessage = `${message}\n\n**Context:**\n${JSON.stringify(details, null, 2)}`;
    await this.send(slackMessage, 'info');
  }

  private async send(message: string, level: AlertLevel): Promise<void> {
    const emoji = {
      critical: '🚨',
      warning: '⚠️',
      info: 'ℹ️',
    }[level];

    const color = {
      critical: '#FF0000',
      warning: '#FFA500',
      info: '#0099CC',
    }[level];

    const payload = {
      text: `${emoji} Bordfeed Alert`,
      attachments: [
        {
          color,
          fields: [
            {
              title: `${level.toUpperCase()} Alert`,
              value: message,
              short: false,
            },
            {
              title: 'Timestamp',
              value: new Date().toISOString(),
              short: true,
            },
            {
              title: 'Environment',
              value: process.env.NODE_ENV || 'unknown',
              short: true,
            },
          ],
        },
      ],
    } as const;

    const response = await fetch(this.webhookUrl, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(payload),
    });

    if (!response.ok) {
      throw new Error(
        `Slack webhook failed: ${response.status} ${response.statusText}`
      );
    }
  }
}

export function createNotifier(): Notifier {
  if (!SLACK_CONFIG.webhookUrl) {
    return new NoopNotifier();
  }
  return new SlackNotifier(SLACK_CONFIG.webhookUrl);
}
