export interface SourceConfig {
  id: string;
  testEndpoint: string | null;
  configUrl?: (actorId: string) => string;
  scheduleUrl?: (apifyUrl: string | null) => string | null;
  runsUrl?: (actorId: string) => string;
}

// Centralized source configuration
export const SOURCE_CONFIGS: Record<string, SourceConfig> = {
  'jobdata-api': {
    id: 'jobdata-api',
    testEndpoint: '/api/sources/jobdata-api/health',
    configUrl: (actorId) => `https://console.apify.com/actors/${actorId}`,
    scheduleUrl: (apifyUrl) => apifyUrl,
    runsUrl: (actorId) => `https://console.apify.com/actors/${actorId}/runs`,
  },
  'wwr-rss': {
    id: 'wwr-rss',
    testEndpoint: '/api/sources/wwr-rss/health',
    configUrl: (actorId) => `https://console.apify.com/actors/${actorId}`,
    scheduleUrl: (apifyUrl) => apifyUrl,
    runsUrl: (actorId) => `https://console.apify.com/actors/${actorId}/runs`,
  },
  workable: {
    id: 'workable',
    testEndpoint: '/api/sources/workable/health',
    configUrl: (actorId) => `https://console.apify.com/actors/${actorId}`,
    scheduleUrl: (apifyUrl) => apifyUrl,
    runsUrl: (actorId) => `https://console.apify.com/actors/${actorId}/runs`,
  },
};

// Helper functions
export const getTestUrl = (sourceId: string): string | null => {
  return SOURCE_CONFIGS[sourceId]?.testEndpoint || null;
};

export const getConfigUrl = (
  sourceId: string,
  actorId: string,
  customUrl?: string
): string => {
  if (customUrl) {
    return customUrl;
  }
  const config = SOURCE_CONFIGS[sourceId];
  return (
    config?.configUrl?.(actorId) ||
    `https://console.apify.com/actors/${actorId}`
  );
};

export const getScheduleUrl = (
  sourceId: string,
  apifyUrl: string | null
): string | null => {
  const config = SOURCE_CONFIGS[sourceId];
  return config?.scheduleUrl?.(apifyUrl) || null;
};

export const getRunsUrl = (sourceId: string, actorId: string): string => {
  const config = SOURCE_CONFIGS[sourceId];
  return (
    config?.runsUrl?.(actorId) ||
    `https://console.apify.com/actors/${actorId}/runs`
  );
};
