// Apply method constants
export const APPLY_METHODS = [
  'link',
  'email',
  'phone',
  'form',
  'platform',
  'other',
] as const;

export type ApplyMethod = (typeof APPLY_METHODS)[number];

export const APPLY_METHOD_DESCRIPTIONS: Record<ApplyMethod, string> = {
  link: 'Direct website or ATS link',
  email: 'Email application',
  phone: 'Phone call application',
  form: 'Online form or application portal',
  platform: 'Third-party platform (LinkedIn, etc.)',
  other: 'Custom instructions or other method',
} as const;
