/**
 * Workflow Utilities
 *
 * Shared utilities for Upstash Workflow operations
 */

import type {
  DatabaseJob,
  JobLockResult,
  WorkflowConfig,
  WorkflowError,
  WorkflowRun,
} from '@/types/workflow';
import { DEFAULT_WORKFLOW_CONFIG, JobLockError } from '@/types/workflow';
import { createClient } from './supabase';
import { logger } from './utils';

// =============================================================================
// JOB LOCKING UTILITIES
// =============================================================================

/**
 * Atomically lock jobs for processing to prevent race conditions
 */
export async function lockJobsForProcessing(
  workflowRunId: string,
  batchSize: number = DEFAULT_WORKFLOW_CONFIG.batchSize,
  source?: string
): Promise<JobLockResult> {
  const supabase = createClient();

  try {
    logger.info('🔒 Attempting to lock jobs for processing', {
      workflowRunId,
      batchSize,
      source,
    });

    // Step 1: SELECT the specific jobs we want to lock (with proper LIMIT)
    let selectQuery = supabase
      .from('jobs')
      .select('id, source_name, created_at')
      .eq('processing_status', 'pending')
      .is('workflow_run_id', null)
      .order('created_at', { ascending: true })
      .limit(batchSize);

    // Add source filter if specified
    if (source) {
      selectQuery = selectQuery.eq('source_name', source);
    }

    const { data: jobsToLock, error: selectError } = await selectQuery;

    if (selectError) {
      throw new JobLockError(
        `Failed to select jobs for locking: ${selectError.message}`,
        workflowRunId,
        selectError
      );
    }

    if (!jobsToLock || jobsToLock.length === 0) {
      logger.info('🔒 No jobs available for locking', { workflowRunId });
      return {
        lockedJobs: [],
        lockCount: 0,
        workflowRunId,
      };
    }

    // Step 2: UPDATE only the specific jobs by their IDs
    const jobIds = jobsToLock.map((job) => job.id);
    const { data: jobs, error } = await supabase
      .from('jobs')
      .update({
        processing_status: 'processing',
        workflow_run_id: workflowRunId,
        locked_at: new Date().toISOString(),
        locked_by: 'workflow',
      })
      .in('id', jobIds)
      .eq('processing_status', 'pending') // Double-check still pending
      .is('workflow_run_id', null) // Double-check not already locked
      .select('*');

    if (error) {
      throw new JobLockError(
        `Failed to lock jobs: ${error.message}`,
        workflowRunId,
        error
      );
    }

    const lockedJobs = (jobs || []) as DatabaseJob[];
    const lockCount = lockedJobs.length;

    logger.info(
      `🔒 Successfully locked ${lockCount}/${jobsToLock.length} jobs`,
      {
        workflowRunId,
        requestedIds: jobIds,
        lockedIds: lockedJobs.map((j) => j.id),
      }
    );

    return {
      lockedJobs,
      lockCount,
      workflowRunId,
    };
  } catch (error) {
    logger.error('❌ Failed to lock jobs', { error, workflowRunId });

    if (error instanceof JobLockError) {
      throw error;
    }

    throw new JobLockError(
      `Unexpected error during job locking: ${error instanceof Error ? error.message : 'Unknown error'}`,
      workflowRunId,
      error instanceof Error ? error : undefined
    );
  }
}

/**
 * Unlock jobs and clear workflow tracking fields
 */
export async function unlockJobs(jobIds: string[]): Promise<void> {
  if (jobIds.length === 0) return;

  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('jobs')
      .update({
        workflow_run_id: null,
        locked_at: null,
        locked_by: null,
      })
      .in('id', jobIds);

    if (error) {
      throw error;
    }

    logger.info(`🔓 Unlocked ${jobIds.length} jobs`, { jobIds });
  } catch (error) {
    logger.error('❌ Failed to unlock jobs', { error, jobIds });
    throw error;
  }
}

// =============================================================================
// WORKFLOW RUN TRACKING
// =============================================================================

/**
 * Create a new workflow run record
 */
export async function createWorkflowRun(
  workflowRunId: string,
  workflowType: string,
  jobCount: number
): Promise<void> {
  const supabase = createClient();

  try {
    const { error } = await supabase.from('workflow_runs').insert({
      id: workflowRunId,
      workflow_type: workflowType,
      status: 'running',
      job_count: jobCount,
    });

    if (error) {
      throw error;
    }

    logger.info('📝 Created workflow run record', {
      workflowRunId,
      workflowType,
      jobCount,
    });
  } catch (error) {
    logger.error('❌ Failed to create workflow run', { error, workflowRunId });
    throw error;
  }
}

/**
 * Update workflow run status
 */
export async function updateWorkflowRun(
  workflowRunId: string,
  status: 'completed' | 'failed' | 'partial_failure',
  errorMessage?: string
): Promise<void> {
  const supabase = createClient();

  try {
    const { error } = await supabase
      .from('workflow_runs')
      .update({
        status,
        completed_at: new Date().toISOString(),
        error_message: errorMessage || null,
      })
      .eq('id', workflowRunId);

    if (error) {
      throw error;
    }

    logger.info('📝 Updated workflow run status', {
      workflowRunId,
      status,
      errorMessage,
    });
  } catch (error) {
    logger.error('❌ Failed to update workflow run', { error, workflowRunId });
    throw error;
  }
}

/**
 * Get workflow run by ID
 */
export async function getWorkflowRun(
  workflowRunId: string
): Promise<WorkflowRun | null> {
  const supabase = createClient();

  try {
    const { data: run, error } = await supabase
      .from('workflow_runs')
      .select('*')
      .eq('id', workflowRunId)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        // Not found
        return null;
      }
      throw error;
    }

    return run as WorkflowRun;
  } catch (error) {
    logger.error('❌ Failed to get workflow run', { error, workflowRunId });
    return null;
  }
}

// =============================================================================
// CLEANUP UTILITIES
// =============================================================================

/**
 * Clean up stuck jobs that have been locked for too long
 */
export async function cleanupStuckJobs(timeoutMinutes = 30): Promise<number> {
  const supabase = createClient();

  try {
    const cutoffTime = new Date(
      Date.now() - timeoutMinutes * 60 * 1000
    ).toISOString();

    const { data: stuckJobs, error } = await supabase
      .from('jobs')
      .update({
        processing_status: 'pending',
        workflow_run_id: null,
        locked_at: null,
        locked_by: null,
      })
      .eq('processing_status', 'processing')
      .lt('locked_at', cutoffTime)
      .select('id');

    if (error) {
      throw error;
    }

    const cleanedCount = stuckJobs?.length || 0;

    if (cleanedCount > 0) {
      logger.warn(`🧹 Cleaned up ${cleanedCount} stuck jobs`, {
        cutoffTime,
        timeoutMinutes,
        jobIds: stuckJobs?.map((j) => j.id),
      });
    }

    return cleanedCount;
  } catch (error) {
    logger.error('❌ Failed to cleanup stuck jobs', { error, timeoutMinutes });
    throw error;
  }
}

// =============================================================================
// ERROR HANDLING UTILITIES
// =============================================================================

/**
 * Handle workflow errors with proper logging and cleanup
 */
export async function handleWorkflowError(
  error: Error,
  workflowRunId: string,
  step: string,
  jobIds?: string[]
): Promise<void> {
  logger.error(`❌ Workflow error in step: ${step}`, {
    error: error.message,
    workflowRunId,
    step,
    jobIds,
  });

  // Unlock jobs if provided
  if (jobIds && jobIds.length > 0) {
    try {
      await unlockJobs(jobIds);
    } catch (unlockError) {
      logger.error('❌ Failed to unlock jobs during error handling', {
        unlockError,
        jobIds,
      });
    }
  }

  // Update workflow run status
  try {
    await updateWorkflowRun(workflowRunId, 'failed', error.message);
  } catch (updateError) {
    logger.error('❌ Failed to update workflow run during error handling', {
      updateError,
      workflowRunId,
    });
  }
}

/**
 * Validate workflow configuration
 */
export function validateWorkflowConfig(
  config: Partial<WorkflowConfig>
): WorkflowConfig {
  return {
    maxConcurrency: Math.max(
      1,
      Math.min(
        config.maxConcurrency || DEFAULT_WORKFLOW_CONFIG.maxConcurrency,
        20
      )
    ),
    timeoutMs: Math.max(
      30_000,
      Math.min(config.timeoutMs || DEFAULT_WORKFLOW_CONFIG.timeoutMs, 600_000)
    ),
    retryAttempts: Math.max(
      0,
      Math.min(config.retryAttempts || DEFAULT_WORKFLOW_CONFIG.retryAttempts, 5)
    ),
    retryDelayMs: Math.max(
      100,
      Math.min(
        config.retryDelayMs || DEFAULT_WORKFLOW_CONFIG.retryDelayMs,
        10_000
      )
    ),
    batchSize: Math.max(
      1,
      Math.min(config.batchSize || DEFAULT_WORKFLOW_CONFIG.batchSize, 50)
    ),
  };
}
