/**
 * Shared types for job filtering across the application
 */

export interface JobFilters {
  search?: string;
  status?: string;
  processingStatus?: string;
  sourceType?: string;
  jobTypes?: string[];
  workplaceTypes?: string[];
  careerLevels?: string[];
  salaryMin?: number;
  salaryMax?: number;
  salaryCurrencies?: string[];
  includeKeywords?: string[];
  excludeKeywords?: string[];
  countries?: string[];
  languages?: string[];
}

/**
 * URL state structure for job filters (read values)
 * Values returned by useQueryStates with defaults are never null
 */
export interface JobFiltersUrlState {
  q: string; // search query
  status: string; // job status
  processing: string; // processing status
  source: string; // source type
  types: string; // job types (comma-separated)
  workplace: string; // workplace types (comma-separated)
  levels: string; // career levels (comma-separated)
  currencies: string; // salary currencies (comma-separated)
  countries: string; // countries (comma-separated)
  languages: string; // languages (comma-separated)
  include: string; // include keywords (comma-separated)
  exclude: string; // exclude keywords (comma-separated)
  salaryMin: number | null; // minimum salary (no default)
  salaryMax: number | null; // maximum salary (no default)
}

/**
 * URL state update structure for job filters (write values)
 * Partial updates where null removes the parameter from URL
 */
export interface JobFiltersUrlUpdate {
  q?: string | null; // search query
  status?: string | null; // job status
  processing?: string | null; // processing status
  source?: string | null; // source type
  types?: string | null; // job types (comma-separated)
  workplace?: string | null; // workplace types (comma-separated)
  levels?: string | null; // career levels (comma-separated)
  currencies?: string | null; // salary currencies (comma-separated)
  countries?: string | null; // countries (comma-separated)
  languages?: string | null; // languages (comma-separated)
  include?: string | null; // include keywords (comma-separated)
  exclude?: string | null; // exclude keywords (comma-separated)
  salaryMin?: number | null; // minimum salary
  salaryMax?: number | null; // maximum salary
}

/**
 * Filter option for select components
 */
export interface FilterOption {
  value: string;
  label: string;
}

/**
 * Array filter configuration
 */
export interface ArrayFilterConfig {
  urlKey: keyof JobFiltersUrlState;
  filterKey: keyof JobFilters;
  options: FilterOption[];
  label: string;
}

/**
 * Select filter configuration
 */
export interface SelectFilterConfig {
  urlKey: keyof JobFiltersUrlState;
  filterKey: keyof JobFilters;
  options: FilterOption[];
  label: string;
  allLabel: string;
}
