/**
 * Date formatting utilities
 */

/**
 * Convert ISO datetime string to YYYY-MM-DD format
 * Used for Airtable date fields and display purposes
 */
export function formatDateForAirtable(
  isoDate: string | null | undefined
): string | null {
  if (!isoDate) {
    return null;
  }

  try {
    const date = new Date(isoDate);
    if (Number.isNaN(date.getTime())) {
      return null;
    }
    return date.toISOString().split('T')[0];
  } catch {
    return null;
  }
}

/**
 * Convert date to ISO string format
 */
export function toISOString(
  date: Date | string | null | undefined
): string | null {
  if (!date) {
    return null;
  }

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (Number.isNaN(dateObj.getTime())) {
      return null;
    }
    return dateObj.toISOString();
  } catch {
    return null;
  }
}

/**
 * Format date for display (e.g., "Jan 15, 2024")
 */
export function formatDateForDisplay(
  date: string | Date | null | undefined
): string {
  if (!date) {
    return 'N/A';
  }

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (Number.isNaN(dateObj.getTime())) {
      return 'Invalid date';
    }

    return dateObj.toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
    });
  } catch {
    return 'Invalid date';
  }
}

/**
 * Check if a date string represents a date in the past
 */
export function isDateInPast(date: string | Date | null | undefined): boolean {
  if (!date) {
    return false;
  }

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (Number.isNaN(dateObj.getTime())) {
      return false;
    }
    return dateObj < new Date();
  } catch {
    return false;
  }
}

/**
 * Calculate days until a date
 */
export function daysUntil(
  date: string | Date | null | undefined
): number | null {
  if (!date) {
    return null;
  }

  try {
    const dateObj = typeof date === 'string' ? new Date(date) : date;
    if (Number.isNaN(dateObj.getTime())) {
      return null;
    }

    const now = new Date();
    const diffMs = dateObj.getTime() - now.getTime();
    return Math.ceil(diffMs / (1000 * 60 * 60 * 24));
  } catch {
    return null;
  }
}
