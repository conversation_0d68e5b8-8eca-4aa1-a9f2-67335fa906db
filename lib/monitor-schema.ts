import { z } from 'zod';

// Monitoring status options for classifier output
export const MONITOR_STATUSES = [
  'active',
  'closed',
  'filled',
  'unknown',
] as const;
export type MonitorStatus = (typeof MONITOR_STATUSES)[number];

/**
 * Zod schema for AI status classification response.
 * - status: one of MONITOR_STATUSES
 * - confidence: number 0..1 (higher = more confident)
 */
export const JobStatusClassifierSchema = z.object({
  status: z.enum(MONITOR_STATUSES),
  confidence: z.number().min(0).max(1),
});
