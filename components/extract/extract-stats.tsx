'use client';

import { useEffect, useState } from 'react';
import { Badge } from '@/components/ui/badge';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Skeleton } from '@/components/ui/skeleton';

interface JobStats {
  total: number;
  pending: number;
  completed: number;
  monitoring: {
    active: number;
    closed: number;
    filled: number;
    unknown: number;
  };
}

export function ExtractStats() {
  const [stats, setStats] = useState<JobStats | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchStats = async () => {
      try {
        const response = await fetch('/api/job-stats');
        if (response.ok) {
          const data = await response.json();
          setStats(data.stats);
        }
      } catch (_error) {
        // Silently ignore errors - stats are not critical
      } finally {
        setLoading(false);
      }
    };

    fetchStats();
  }, []);

  if (loading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {['extracted', 'stored', 'processed', 'success-rate'].map(
          (statType) => (
            <Card key={statType}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-4" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-7 w-16" />
                <Skeleton className="mt-1 h-3 w-24" />
              </CardContent>
            </Card>
          )
        )}
      </div>
    );
  }

  if (!stats) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Job Statistics</CardTitle>
          <CardDescription>Unable to load statistics</CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Total Jobs</CardTitle>
          <div className="h-4 w-4 text-muted-foreground">📊</div>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">
            {stats.total.toLocaleString()}
          </div>
          <p className="text-muted-foreground text-xs">All jobs in database</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">
            Pending Processing
          </CardTitle>
          <div className="h-4 w-4 text-muted-foreground">⏳</div>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">
            {stats.pending.toLocaleString()}
          </div>
          <p className="text-muted-foreground text-xs">
            Awaiting AI processing
          </p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">Completed</CardTitle>
          <div className="h-4 w-4 text-muted-foreground">✅</div>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">
            {stats.completed.toLocaleString()}
          </div>
          <p className="text-muted-foreground text-xs">Fully processed jobs</p>
        </CardContent>
      </Card>

      <Card>
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="font-medium text-sm">
            Monitoring Status
          </CardTitle>
          <div className="h-4 w-4 text-muted-foreground">📈</div>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-1">
            <Badge className="text-xs" variant="secondary">
              Active: {stats.monitoring.active}
            </Badge>
            <Badge className="text-xs" variant="outline">
              Closed: {stats.monitoring.closed}
            </Badge>
            <Badge className="text-xs" variant="outline">
              Filled: {stats.monitoring.filled}
            </Badge>
            <Badge className="text-xs" variant="outline">
              Unknown: {stats.monitoring.unknown}
            </Badge>
          </div>
          <p className="mt-2 text-muted-foreground text-xs">
            Job status breakdown
          </p>
        </CardContent>
      </Card>
    </div>
  );
}
