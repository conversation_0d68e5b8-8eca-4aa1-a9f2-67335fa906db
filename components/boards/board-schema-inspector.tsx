'use client';

import { AlertCircle, Database, Loader2, RefreshCw } from 'lucide-react';
import { useCallback, useEffect, useState } from 'react';
import { Alert, AlertDescription } from '../ui/alert';
import { Button } from '../ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';
import { FieldMappingCard } from './field-mapping-card';
import { type BoardSchemaData, SchemaCard } from './schema-card';

interface JobBoard {
  id: string;
  name: string;
  airtable: {
    baseId: string;
    tableName: string;
  };
  patStatus?: 'configured' | 'missing' | 'invalid';
}

interface BoardSchemaInspectorProps {
  boards: JobBoard[];
  className?: string;
}

export function BoardSchemaInspector({
  boards,
  className,
}: BoardSchemaInspectorProps) {
  const [selectedBoardId, setSelectedBoardId] = useState<string>('');
  const [schemaData, setSchemaData] = useState<BoardSchemaData | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Filter boards that have Airtable configuration
  const configuredBoards = boards.filter(
    (board) =>
      board.airtable.baseId &&
      board.airtable.tableName &&
      board.patStatus === 'configured'
  );

  const selectedBoard = configuredBoards.find(
    (board) => board.id === selectedBoardId
  );

  const loadSchema = useCallback(async (boardId: string) => {
    if (!boardId) {
      return;
    }

    setLoading(true);
    setError(null);
    setSchemaData(null);

    try {
      const response = await fetch(`/api/board-schema/${boardId}`);
      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || `HTTP ${response.status}`);
      }

      setSchemaData(data);
    } catch (err) {
      const errorMessage =
        err instanceof Error ? err.message : 'Failed to load schema';
      setError(errorMessage);
    } finally {
      setLoading(false);
    }
  }, []);

  // Auto-load schema when board is selected
  useEffect(() => {
    if (selectedBoardId) {
      loadSchema(selectedBoardId);
    }
  }, [selectedBoardId, loadSchema]);

  // Auto-select first board if only one is available
  useEffect(() => {
    if (configuredBoards.length === 1 && !selectedBoardId) {
      setSelectedBoardId(configuredBoards[0].id);
    }
  }, [configuredBoards, selectedBoardId]);

  const handleRefresh = () => {
    if (selectedBoardId) {
      loadSchema(selectedBoardId);
    }
  };

  if (configuredBoards.length === 0) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-medium text-sm">
            <Database className="h-5 w-5" />
            Airtable Schema Inspector
          </CardTitle>
          <CardDescription className="text-xs">
            Inspect Airtable table schemas and validate field mappings for your
            job boards
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription className="text-xs">
              No job boards with complete Airtable configuration found. Create a
              job board with valid Airtable credentials to use the schema
              inspector.
            </AlertDescription>
          </Alert>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className={className}>
      {/* Board Selection */}
      <Card className="mb-6">
        <CardHeader>
          <CardTitle className="flex items-center gap-2 font-medium text-sm">
            <Database className="h-5 w-5" />
            Airtable Schema Inspector
          </CardTitle>
          <CardDescription className="text-xs">
            Inspect Airtable table schemas and validate field mappings for your
            job boards
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center gap-2">
            <Select onValueChange={setSelectedBoardId} value={selectedBoardId}>
              <SelectTrigger className="flex-1">
                <SelectValue placeholder="Select a job board to inspect" />
              </SelectTrigger>
              <SelectContent>
                {configuredBoards.map((board) => (
                  <SelectItem key={board.id} value={board.id}>
                    <div className="flex w-full items-center justify-between">
                      <span>{board.name}</span>
                      <span className="ml-2 text-muted-foreground text-xs">
                        {board.airtable.tableName}
                      </span>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {selectedBoardId && (
              <Button
                disabled={loading}
                onClick={handleRefresh}
                size="sm"
                variant="outline"
              >
                {loading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
              </Button>
            )}
          </div>

          {selectedBoard && (
            <div className="text-muted-foreground text-xs">
              <div>
                Base ID: <code>{selectedBoard.airtable.baseId}</code>
              </div>
              <div>
                Table: <code>{selectedBoard.airtable.tableName}</code>
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <div className="flex items-center gap-2 text-muted-foreground">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Loading Airtable schema...</span>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Error State */}
      {error && !loading && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-xs">
            <strong>Failed to load schema:</strong> {error}
          </AlertDescription>
        </Alert>
      )}

      {/* Schema Display */}
      {schemaData && !loading && !error && (
        <div className="grid gap-6 md:grid-cols-2">
          <SchemaCard boardName={selectedBoard?.name} schemaData={schemaData} />
          <FieldMappingCard
            boardName={selectedBoard?.name}
            className="md:col-span-2"
            schemaData={schemaData}
          />
        </div>
      )}
    </div>
  );
}
