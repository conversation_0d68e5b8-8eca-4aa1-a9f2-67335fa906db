'use client';

import { ThemeToggle as FDThemeToggle } from 'fumadocs-ui/components/layout/theme-toggle';
import { cn } from '@/lib/utils';

export function SidebarThemeToggle({
  className,
  mode = 'light-dark',
}: {
  className?: string;
  mode?: 'light-dark' | 'light-dark-system';
}) {
  return (
    <div className={cn('flex items-center justify-end', className)}>
      <FDThemeToggle className="ms-1.5 p-0" mode={mode} />
    </div>
  );
}

export default SidebarThemeToggle;
