'use client';

import { <PERSON><PERSON><PERSON><PERSON>gle, Check<PERSON><PERSON>cle, Settings, XCircle } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export function EnvironmentHealthCard() {
  // Check environment variables (client-side safe checks)
  const envChecks = [
    {
      name: 'Node Environment',
      value: process.env.NODE_ENV || 'unknown',
      status: process.env.NODE_ENV ? 'healthy' : 'warning',
      critical: false,
    },
    {
      name: 'Next.js Version',
      value: process.env.NEXT_PUBLIC_VERCEL_ENV || 'local',
      status: 'healthy',
      critical: false,
    },
  ];

  // Note: We can't check server-side env vars from client components
  // This is intentional for security
  const serverEnvs = [
    'Database Connection',
    'Redis Connection',
    'QStash Configuration',
    'Airtable Integration',
    'OpenAI API',
    'Slack Webhooks',
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
      case 'error':
        return <XCircle className="h-3 w-3 text-red-500" />;
      default:
        return <Settings className="h-3 w-3 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const healthyCount = envChecks.filter(
    (check) => check.status === 'healthy'
  ).length;
  const totalChecks = envChecks.length + serverEnvs.length;

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Settings className="h-4 w-4" />
          Environment
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">{healthyCount}</span>
            <Badge variant="secondary">{totalChecks} total</Badge>
          </div>
          <p className="text-muted-foreground text-xs">Configured Services</p>
        </div>

        <div className="space-y-3">
          <div className="space-y-2">
            {envChecks.map((check) => (
              <div
                className="flex items-center justify-between text-xs"
                key={check.name}
              >
                <div className="flex items-center gap-2">
                  {getStatusIcon(check.status)}
                  <span>{check.name}</span>
                </div>
                <Badge
                  className={getStatusColor(check.status)}
                  variant="secondary"
                >
                  {check.value}
                </Badge>
              </div>
            ))}
          </div>

          <div className="border-t pt-2">
            <div className="mb-2 font-medium text-xs">Server Configuration</div>
            <div className="space-y-1">
              {serverEnvs.map((env) => (
                <div
                  className="flex items-center justify-between text-xs"
                  key={env}
                >
                  <div className="flex items-center gap-2">
                    <Settings className="h-3 w-3 text-gray-500" />
                    <span className="text-muted-foreground">{env}</span>
                  </div>
                  <Badge className="text-xs" variant="outline">
                    Server-side
                  </Badge>
                </div>
              ))}
            </div>
            <div className="mt-2 text-muted-foreground text-xs">
              Server environment variables are validated at runtime for
              security.
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
