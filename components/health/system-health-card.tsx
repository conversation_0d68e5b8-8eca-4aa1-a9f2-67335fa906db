'use client';

import {
  AlertCircle,
  Bot,
  CheckCircle,
  Database,
  MessageSquare,
  Server,
  Zap,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

interface SystemHealthData {
  status: 'healthy' | 'degraded' | 'unhealthy';
  timestamp: string;
  version: string;
  environment: string;
  checks: {
    database: {
      status: string;
      latency: number;
      error?: string;
    };
    workflow: {
      status: string;
      latency: number;
      error?: string;
    };
    apify: {
      status: string;
      latency: number;
      error?: string;
    };
    slack: {
      status: string;
      latency: number;
      error?: string;
    };
    uptime: number;
    responseTime?: number;
  };
}

interface SystemHealthCardProps {
  data?: SystemHealthData;
}

export function SystemHealthCard({ data }: SystemHealthCardProps) {
  if (!data?.checks) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="font-medium text-sm">System Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'degraded':
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'unhealthy':
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Server className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'degraded':
        return 'bg-yellow-100 text-yellow-800';
      case 'unhealthy':
      case 'error':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatUptime = (seconds: number) => {
    const days = Math.floor(seconds / 86_400);
    const hours = Math.floor((seconds % 86_400) / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);

    if (days > 0) {
      return `${days}d ${hours}h ${minutes}m`;
    }
    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Server className="h-4 w-4" />
          System Health
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex items-center gap-2">
          {getStatusIcon(data.status)}
          <Badge className={getStatusColor(data.status)}>
            {data.status.toUpperCase()}
          </Badge>
        </div>

        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Database className="h-4 w-4 text-blue-500" />
              <span className="text-sm">Database</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(data.checks.database.status)}
              <span className="text-muted-foreground text-sm">
                {data.checks.database.latency}ms
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Zap className="h-4 w-4 text-purple-500" />
              <span className="text-sm">Workflow</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(data.checks.workflow.status)}
              <span className="text-muted-foreground text-sm">
                {data.checks.workflow.latency}ms
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Bot className="h-4 w-4 text-green-500" />
              <span className="text-sm">Apify</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(data.checks.apify.status)}
              <span className="text-muted-foreground text-sm">
                {data.checks.apify.latency}ms
              </span>
            </div>
          </div>

          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4 text-indigo-500" />
              <span className="text-sm">Slack</span>
            </div>
            <div className="flex items-center gap-2">
              {getStatusIcon(data.checks.slack.status)}
              <span className="text-muted-foreground text-sm">
                {data.checks.slack.latency}ms
              </span>
            </div>
          </div>

          <div className="border-t pt-2">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Uptime</span>
              <span>{formatUptime(data.checks.uptime)}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Version</span>
              <span>{data.version}</span>
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Environment</span>
              <span className="capitalize">{data.environment}</span>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
