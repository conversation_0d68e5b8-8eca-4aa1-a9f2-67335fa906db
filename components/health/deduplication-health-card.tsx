'use client';

import { Shield, TrendingDown } from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface DeduplicationData {
  success: boolean;
  data?: {
    total_duplicates: number;
    duplicates_today: number;
    duplicate_rate: number;
    total_jobs: number;
    unique_pairs_with_duplicates: number;
    most_common_duplicates: Array<{
      title: string;
      count: number;
    }>;
  };
}

interface DeduplicationHealthCardProps {
  data?: DeduplicationData;
}

function LoadingCard() {
  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Shield className="h-4 w-4" />
          Deduplication
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">--</span>
            <Badge variant="secondary">Loading</Badge>
          </div>
          <div className="text-muted-foreground text-xs">
            Loading deduplication metrics...
          </div>
        </div>
      </CardContent>
    </Card>
  );
}

export function DeduplicationHealthCard({
  data,
}: DeduplicationHealthCardProps) {
  if (!data?.success) {
    return <LoadingCard />;
  }

  const dedupData = data.data;
  const duplicateRate = dedupData?.duplicate_rate || 0;
  const totalDuplicates = dedupData?.total_duplicates || 0;
  const totalJobs = dedupData?.total_jobs || 0;
  const duplicatesToday = dedupData?.duplicates_today || 0;
  const mostCommonDuplicates = dedupData?.most_common_duplicates || [];

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Shield className="h-4 w-4" />
          Deduplication
          {duplicateRate > 5 && (
            <Badge className="ml-auto" variant="destructive">
              High Rate
            </Badge>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Key Metrics */}
        <div className="space-y-2">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">
              {duplicateRate.toFixed(1)}%
            </span>
            <TrendingDown className="h-4 w-4 text-green-500" />
          </div>
          <p className="text-muted-foreground text-xs">Duplicate Rate</p>
        </div>

        {/* Performance Stats */}
        <div className="grid grid-cols-2 gap-3 text-xs">
          <div>
            <div className="font-medium">{totalJobs.toLocaleString()}</div>
            <div className="text-muted-foreground">Total Jobs</div>
          </div>
          <div>
            <div className="font-medium">
              {totalDuplicates.toLocaleString()}
            </div>
            <div className="text-muted-foreground">Duplicates</div>
          </div>
          <div>
            <div className="font-medium">
              {duplicatesToday.toLocaleString()}
            </div>
            <div className="text-muted-foreground">Today</div>
          </div>
          <div>
            <div className="font-medium">
              {dedupData?.unique_pairs_with_duplicates || 0}
            </div>
            <div className="text-muted-foreground">Unique Pairs</div>
          </div>
        </div>

        {/* Most Common Duplicates */}
        {mostCommonDuplicates.length > 0 && (
          <div className="space-y-2">
            <div className="font-medium text-xs">Most Common Duplicates</div>
            <div className="space-y-1">
              {mostCommonDuplicates.slice(0, 3).map((duplicate) => (
                <div
                  className="flex items-center justify-between text-xs"
                  key={`${duplicate.title}-${duplicate.count}`}
                >
                  <span className="truncate">{duplicate.title}</span>
                  <span className="font-medium text-orange-600">
                    {duplicate.count}
                  </span>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Progress Bar */}
        <div className="space-y-1">
          <div className="flex items-center justify-between text-xs">
            <span>Duplicate Rate</span>
            <span className="font-medium">{duplicateRate.toFixed(1)}%</span>
          </div>
          <Progress className="h-2" value={Math.min(100, duplicateRate)} />
          <div className="flex justify-between text-muted-foreground text-xs">
            <span>Target: &lt;2%</span>
            <span>{duplicateRate > 2 ? 'Above target' : 'Within target'}</span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
