'use client';

import { RefreshCw } from 'lucide-react';
import { useCallback } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { useHealthStoreHydrated } from '@/lib/stores';
import { ConnectedSourcesHealthCard } from './connected-sources-health-card';
import { DeduplicationHealthCard } from './deduplication-health-card';
import { EnvironmentHealthCard } from './environment-health-card';
import { JobStatsCard } from './job-stats-card';
import { LogsHealthCard } from './logs-health-card';
import { MonitoringHealthCard } from './monitoring-health-card';
import { SourcesHealthCard } from './sources-health-card';
import { SystemHealthCard } from './system-health-card';

export function HealthDashboard() {
  // Use the health store with hydration protection
  const { data, loading, error, autoRefresh, actions } =
    useHealthStoreHydrated();

  const handleRefresh = useCallback(async () => {
    await actions.refresh();
  }, [actions]);

  const toggleAutoRefresh = useCallback(() => {
    if (autoRefresh) {
      actions.disableAutoRefresh();
    } else {
      actions.enableAutoRefresh();
    }
  }, [autoRefresh, actions]);

  if (loading && !data.systemHealth) {
    const loadingCards = [
      'system',
      'sources',
      'jobs',
      'deduplication',
      'monitoring',
      'environment',
      'logs',
      'connected-sources',
    ];

    return (
      <div className="space-y-6">
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {loadingCards.map((cardType) => (
            <Card key={cardType}>
              <CardHeader>
                <div className="h-4 w-32 animate-pulse rounded bg-muted" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <div className="h-8 w-16 animate-pulse rounded bg-muted" />
                  <div className="h-4 w-24 animate-pulse rounded bg-muted" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-end gap-2">
        <Button
          onClick={toggleAutoRefresh}
          size="sm"
          variant={autoRefresh ? 'default' : 'outline'}
        >
          {autoRefresh ? 'Auto-refresh On' : 'Auto-refresh Off'}
        </Button>
        <Button
          disabled={loading}
          onClick={handleRefresh}
          size="sm"
          variant="outline"
        >
          <RefreshCw
            className={`mr-2 h-4 w-4 ${loading ? 'animate-spin' : ''}`}
          />
          Refresh
        </Button>
      </div>
      {error && <p className="text-destructive text-sm">Error: {error}</p>}

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        <SystemHealthCard data={data.systemHealth} />
        <SourcesHealthCard data={data.sourcesHealth} />
        <JobStatsCard data={data.jobStats} />
        <DeduplicationHealthCard data={data.deduplicationData} />
        <MonitoringHealthCard data={data.monitoringData} />
        <EnvironmentHealthCard />
        <LogsHealthCard data={data.logsData?.data as Record<string, number>} />
        <ConnectedSourcesHealthCard data={data.connectedSourcesHealth} />
      </div>

      {data.lastUpdated ? (
        <div className="text-muted-foreground text-xs">
          Last updated:{' '}
          {new Date(data.lastUpdated)
            .toISOString()
            .replace('T', ' ')
            .substring(0, 19)}{' '}
          UTC
        </div>
      ) : null}
    </div>
  );
}
