'use client';

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Clock,
  Minus,
  Radio,
  XCircle,
} from 'lucide-react';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Progress } from '@/components/ui/progress';

interface SourceData {
  id: string;
  name: string;
  enabled: boolean;
  stats: {
    success_rate: number;
    error_count_24h: number;
    last_success: string;
    last_error: string;
  };
}

interface SourcesHealthData {
  success: boolean;
  data?: {
    sources: SourceData[];
  };
}

interface SourcesHealthCardProps {
  data?: SourcesHealthData;
}

export function SourcesHealthCard({ data }: SourcesHealthCardProps) {
  if (!data?.success) {
    return (
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="font-medium text-sm">Sources Health</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="font-bold text-2xl">Loading...</div>
        </CardContent>
      </Card>
    );
  }

  const sources = data.data?.sources || [];
  const healthySources = sources.filter(
    (source) => getSourceStatus(source) === 'healthy'
  ).length;

  const getSourceStatus = (source: SourceData) => {
    if (source.stats.error_count_24h >= 5 || source.stats.success_rate < 0.5) {
      return 'critical';
    }
    if (source.stats.error_count_24h > 0) {
      return 'warning';
    }
    if (source.stats.success_rate > 0.8) {
      return 'healthy';
    }
    return 'unknown';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy':
        return <CheckCircle className="h-3 w-3 text-green-500" />;
      case 'warning':
        return <AlertTriangle className="h-3 w-3 text-yellow-500" />;
      case 'critical':
        return <XCircle className="h-3 w-3 text-red-500" />;
      case 'disabled':
        return <Minus className="h-3 w-3 text-gray-500" />;
      default:
        return <Clock className="h-3 w-3 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'bg-green-100 text-green-800';
      case 'warning':
        return 'bg-yellow-100 text-yellow-800';
      case 'critical':
        return 'bg-red-100 text-red-800';
      case 'disabled':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader className="pb-2">
        <CardTitle className="flex items-center gap-2 font-medium text-sm">
          <Radio className="h-4 w-4" />
          Sources Health
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <span className="font-bold text-2xl">{healthySources}</span>
            <Badge variant="secondary">{sources.length} total</Badge>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Health Rate</span>
              <span className="font-medium">
                {sources.length > 0
                  ? ((healthySources / sources.length) * 100).toFixed(1)
                  : 0}
                %
              </span>
            </div>
            <Progress
              className="h-2"
              value={
                sources.length > 0 ? (healthySources / sources.length) * 100 : 0
              }
            />
          </div>

          <div className="space-y-2">
            {sources.slice(0, 3).map((source) => {
              const status = getSourceStatus(source);
              return (
                <div
                  className="flex items-center justify-between text-xs"
                  key={source.id}
                >
                  <div className="flex items-center gap-2">
                    {getStatusIcon(status)}
                    <span className="text-muted-foreground">{source.name}</span>
                  </div>
                  <Badge
                    className={`text-xs ${getStatusColor(status)}`}
                    variant="outline"
                  >
                    {status}
                  </Badge>
                </div>
              );
            })}
          </div>

          {sources.length > 3 && (
            <div className="border-t pt-2">
              <div className="text-muted-foreground text-xs">
                +{sources.length - 3} more sources
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
