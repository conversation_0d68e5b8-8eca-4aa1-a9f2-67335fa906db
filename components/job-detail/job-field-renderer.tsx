import type React from 'react';
import { Badge } from '@/components/ui/badge';
import { JobExtractionSchema } from '@/lib/job-schema';
import type { DatabaseJob } from '@/lib/storage';
import { formatValue } from '@/lib/utils/job-status-utils';

// Fields that are processed and enriched by AI (derived automatically for 100% accuracy)
const AI_PROCESSED_FIELDS = new Set(
  // Using Object.keys on the zod schema shape ensures the list stays up to date
  // with any future changes to JobExtractionSchema without manual edits.
  Object.keys(JobExtractionSchema.shape)
);

interface JobFieldRendererProps {
  fieldKey: string;
  fieldValue: unknown;
  job: DatabaseJob;
  isEditing: boolean;
  editForm: Partial<DatabaseJob>;
  onInputChange: (
    field: keyof DatabaseJob,
    value: string | boolean | null
  ) => void;
}

export const JobFieldRenderer: React.FC<JobFieldRendererProps> = ({
  fieldKey,
  fieldValue,
  job,
  isEditing,
  editForm,
  onInputChange,
}) => {
  // Check if a field was enriched by AI
  const isAIEnriched = (key: string): boolean => {
    return (
      AI_PROCESSED_FIELDS.has(key) && job?.processing_status === 'completed'
    );
  };

  const renderFieldInput = () => {
    const isEditMode =
      isEditing &&
      fieldKey !== 'id' &&
      fieldKey !== 'created_at' &&
      fieldKey !== 'updated_at';

    if (!isEditMode) {
      return (
        <div className="whitespace-pre-wrap break-words rounded bg-muted p-2 text-muted-foreground text-xs">
          {formatValue(fieldValue)}
        </div>
      );
    }

    const editValue = editForm[fieldKey as keyof DatabaseJob];

    if (typeof fieldValue === 'boolean') {
      return (
        <select
          className="w-full rounded border bg-background px-2 py-1 text-xs"
          onChange={(e) =>
            onInputChange(
              fieldKey as keyof DatabaseJob,
              e.target.value === 'true'
            )
          }
          value={String(editValue)}
        >
          <option value="true">Yes</option>
          <option value="false">No</option>
        </select>
      );
    }

    if (
      fieldKey === 'description' ||
      fieldKey === 'responsibilities' ||
      fieldKey === 'benefits'
    ) {
      return (
        <textarea
          className="w-full rounded border bg-background px-2 py-1 text-xs"
          onChange={(e) =>
            onInputChange(fieldKey as keyof DatabaseJob, e.target.value)
          }
          rows={4}
          value={String(editValue || '')}
        />
      );
    }

    return (
      <input
        className="w-full rounded border bg-background px-2 py-1 text-xs"
        onChange={(e) =>
          onInputChange(fieldKey as keyof DatabaseJob, e.target.value)
        }
        type="text"
        value={String(editValue || '')}
      />
    );
  };

  return (
    <div>
      <div className="mb-1 flex items-center gap-2">
        <span className="block font-medium text-foreground text-xs">
          {fieldKey}
        </span>
        {isAIEnriched(fieldKey) && (
          <Badge className="px-1 py-0 text-[10px]" variant="outline">
            AI
          </Badge>
        )}
      </div>
      {renderFieldInput()}
    </div>
  );
};
