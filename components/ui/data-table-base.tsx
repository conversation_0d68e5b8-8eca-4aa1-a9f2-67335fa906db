import type { Table } from '@tanstack/react-table';
import { flexRender } from '@tanstack/react-table';
import { RefreshCw } from 'lucide-react';
import type { ReactNode } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  TableBody,
  TableCell,
  Table as TableComponent,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { DataTablePagination } from '../jobs/data-table-pagination';
import { DataTableViewOptions } from '../jobs/data-table-view-options';

export interface DataTableBaseProps<TData> {
  table: Table<TData>;
  loading?: boolean;
  filterPlaceholder?: string;
  filterColumn?: string;
  noDataMessage?: string;
  noDataIcon?: ReactNode;
  onRefresh?: () => void;
  refreshLabel?: string;
  customActions?: ReactNode;
  showPagination?: boolean;
  showSelection?: boolean;
  showViewOptions?: boolean;
  emptyStateButton?: ReactNode;
}

export function DataTableBase<TData>({
  table,
  loading = false,
  filterPlaceholder = 'Filter...',
  filterColumn = 'name',
  noDataMessage = 'No data found.',
  noDataIcon,
  onRefresh,
  refreshLabel = 'Refresh',
  customActions,
  showPagination = true,
  showSelection = true,
  showViewOptions = true,
  emptyStateButton,
}: DataTableBaseProps<TData>) {
  if (loading || !table) {
    return (
      <div className="flex items-center justify-center py-10">
        <div className="text-muted-foreground">Loading...</div>
      </div>
    );
  }

  const hasNoData = !table.getRowModel().rows?.length;

  return (
    <div className="flex flex-col gap-4">
      {/* Header with search and actions */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-2">
          <Input
            className="max-w-sm"
            onChange={(event) =>
              table.getColumn(filterColumn)?.setFilterValue(event.target.value)
            }
            placeholder={filterPlaceholder}
            value={
              (table.getColumn(filterColumn)?.getFilterValue() as string) ?? ''
            }
          />
        </div>
        <div className="flex items-center space-x-2">
          {showViewOptions && <DataTableViewOptions table={table} />}
          {onRefresh && (
            <Button onClick={onRefresh} size="sm" variant="outline">
              <RefreshCw className="mr-2 h-4 w-4" />
              {refreshLabel}
            </Button>
          )}
          {customActions}
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border">
        <div className="relative overflow-auto">
          <TableComponent>
            <TableHeader>
              {table.getHeaderGroups().map((headerGroup) => (
                <TableRow key={headerGroup.id}>
                  {headerGroup.headers.map((header) => {
                    const meta = header.column.columnDef.meta as
                      | { className?: string }
                      | undefined;
                    return (
                      <TableHead
                        className={`whitespace-nowrap ${meta?.className || ''}`}
                        key={header.id}
                      >
                        {header.isPlaceholder
                          ? null
                          : flexRender(
                              header.column.columnDef.header,
                              header.getContext()
                            )}
                      </TableHead>
                    );
                  })}
                </TableRow>
              ))}
            </TableHeader>
            <TableBody>
              {hasNoData ? (
                <TableRow>
                  <TableCell
                    className="h-24 text-center"
                    colSpan={table.getAllColumns().length}
                  >
                    <div className="flex flex-col items-center justify-center space-y-2">
                      {noDataIcon && (
                        <div className="text-2xl">{noDataIcon}</div>
                      )}
                      <div className="font-medium text-lg text-muted-foreground">
                        {noDataMessage}
                      </div>
                      {emptyStateButton}
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                table.getRowModel().rows.map((row) => (
                  <TableRow
                    className="cursor-pointer hover:bg-muted/50"
                    data-state={row.getIsSelected() && 'selected'}
                    key={row.id}
                  >
                    {row.getVisibleCells().map((cell) => {
                      const meta = cell.column.columnDef.meta as
                        | { className?: string }
                        | undefined;
                      return (
                        <TableCell
                          className={`whitespace-nowrap ${
                            meta?.className || ''
                          }`}
                          key={cell.id}
                        >
                          {flexRender(
                            cell.column.columnDef.cell,
                            cell.getContext()
                          )}
                        </TableCell>
                      );
                    })}
                  </TableRow>
                ))
              )}
            </TableBody>
          </TableComponent>
        </div>
      </div>

      {/* Pagination */}
      {showPagination && (
        <DataTablePagination showSelection={showSelection} table={table} />
      )}
    </div>
  );
}
