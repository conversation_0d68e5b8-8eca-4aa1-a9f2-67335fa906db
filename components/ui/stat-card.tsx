'use client';

import type React from 'react';
import { Button } from './button';
import { <PERSON>, CardContent, CardHeader, CardTitle } from './card';

interface StatCardProps {
  /** Title of the statistic */
  title: string;
  /** The statistic value to display */
  value: string | number;
  /** Color variant for the value text */
  variant?: 'blue' | 'orange' | 'green' | 'red' | 'purple' | 'gray';
  /** Whether the value is loading */
  loading?: boolean;
  /** Optional button to render instead of value */
  button?: {
    label: string;
    onClick: () => void;
    disabled?: boolean;
    loading?: boolean;
  };
  /** Additional CSS classes */
  className?: string;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  value,
  variant = 'blue',
  loading = false,
  button,
  className = '',
}) => {
  const variantClasses = {
    blue: 'text-blue-600',
    orange: 'text-orange-600',
    green: 'text-green-600',
    red: 'text-red-600',
    purple: 'text-purple-600',
    gray: 'text-gray-600',
  };

  const handleKeyDown = (event: React.KeyboardEvent<HTMLButtonElement>) => {
    if (event.key === 'Enter' || event.key === ' ') {
      event.preventDefault();
      button?.onClick();
    }
  };

  return (
    <Card className={className}>
      <CardHeader className="pb-2">
        <CardTitle className="font-medium text-muted-foreground text-sm">
          {title}
        </CardTitle>
      </CardHeader>
      <CardContent>
        {button ? (
          <Button
            className="w-full text-sm"
            disabled={button.disabled}
            onClick={button.onClick}
            onKeyDown={handleKeyDown}
            type="button"
            variant="outline"
          >
            {button.loading ? '...' : button.label}
          </Button>
        ) : (
          <p className={`font-bold text-2xl ${variantClasses[variant]}`}>
            {loading ? '...' : value}
          </p>
        )}
      </CardContent>
    </Card>
  );
};

export default StatCard;
