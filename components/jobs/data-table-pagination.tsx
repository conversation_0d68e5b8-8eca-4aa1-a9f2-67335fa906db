import type { Table } from '@tanstack/react-table';
import {
  Chevron<PERSON>eft,
  ChevronRight,
  ChevronsLeft,
  ChevronsRight,
} from 'lucide-react';

import { Button } from '@/components/ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface DataTablePaginationProps<TData> {
  table: Table<TData>;
  showSelection?: boolean;
  // Server-side pagination props
  isServerSide?: boolean;
  currentPage?: number;
  totalPages?: number;
  pageSize?: number;
  onPageChange?: (page: number) => void;
  onPageSizeChange?: (pageSize: number) => void;
  canPreviousPage?: boolean;
  canNextPage?: boolean;
}

export function DataTablePagination<TData>({
  table,
  showSelection = true,
  isServerSide = false,
  currentPage,
  totalPages,
  pageSize,
  onPageChange,
  onPageSizeChange,
  canPreviousPage,
  canNextPage,
}: DataTablePaginationProps<TData>) {
  // Use server-side values if provided, otherwise fall back to table state
  const effectivePageSize = isServerSide
    ? pageSize
    : table.getState().pagination.pageSize;
  const effectiveCurrentPage = isServerSide
    ? currentPage
    : table.getState().pagination.pageIndex + 1;
  const effectiveTotalPages = isServerSide ? totalPages : table.getPageCount();
  const effectiveCanPreviousPage = isServerSide
    ? canPreviousPage
    : table.getCanPreviousPage();
  const effectiveCanNextPage = isServerSide
    ? canNextPage
    : table.getCanNextPage();

  const handlePageSizeChange = (value: string) => {
    const newPageSize = Number(value);
    if (isServerSide && onPageSizeChange) {
      onPageSizeChange(newPageSize);
    } else {
      table.setPageSize(newPageSize);
    }
  };

  const handleFirstPage = () => {
    if (isServerSide && onPageChange) {
      onPageChange(1);
    } else {
      table.setPageIndex(0);
    }
  };

  const handlePreviousPage = () => {
    if (isServerSide && onPageChange && effectiveCurrentPage) {
      onPageChange(effectiveCurrentPage - 1);
    } else {
      table.previousPage();
    }
  };

  const handleNextPage = () => {
    if (isServerSide && onPageChange && effectiveCurrentPage) {
      onPageChange(effectiveCurrentPage + 1);
    } else {
      table.nextPage();
    }
  };

  const handleLastPage = () => {
    if (isServerSide && onPageChange && effectiveTotalPages) {
      onPageChange(effectiveTotalPages);
    } else {
      table.setPageIndex(table.getPageCount() - 1);
    }
  };

  return (
    <div className="flex items-center justify-between px-2">
      {showSelection ? (
        <div className="flex-1 text-muted-foreground text-sm">
          {table.getFilteredSelectedRowModel().rows.length} of{' '}
          {table.getFilteredRowModel().rows.length} row(s) selected.
        </div>
      ) : (
        <div className="flex-1" />
      )}
      <div className="flex items-center space-x-6 lg:space-x-8">
        <div className="flex items-center space-x-2">
          <p className="font-medium text-sm">Rows per page</p>
          <Select
            onValueChange={handlePageSizeChange}
            value={`${effectivePageSize}`}
          >
            <SelectTrigger className="h-8 w-[70px]">
              <SelectValue placeholder={effectivePageSize} />
            </SelectTrigger>
            <SelectContent side="top">
              {[10, 20, 50, 100, 200, 500].map((pageSizeOption) => (
                <SelectItem key={pageSizeOption} value={`${pageSizeOption}`}>
                  {pageSizeOption}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="flex w-[100px] items-center justify-center font-medium text-sm">
          Page {effectiveCurrentPage} of {effectiveTotalPages}
        </div>
        <div className="flex items-center space-x-2">
          <Button
            className="hidden h-8 w-8 lg:flex"
            disabled={!effectiveCanPreviousPage}
            onClick={handleFirstPage}
            size="icon"
            variant="outline"
          >
            <span className="sr-only">Go to first page</span>
            <ChevronsLeft className="h-4 w-4" />
          </Button>
          <Button
            className="h-8 w-8"
            disabled={!effectiveCanPreviousPage}
            onClick={handlePreviousPage}
            size="icon"
            variant="outline"
          >
            <span className="sr-only">Go to previous page</span>
            <ChevronLeft className="h-4 w-4" />
          </Button>
          <Button
            className="h-8 w-8"
            disabled={!effectiveCanNextPage}
            onClick={handleNextPage}
            size="icon"
            variant="outline"
          >
            <span className="sr-only">Go to next page</span>
            <ChevronRight className="h-4 w-4" />
          </Button>
          <Button
            className="hidden h-8 w-8 lg:flex"
            disabled={!effectiveCanNextPage}
            onClick={handleLastPage}
            size="icon"
            variant="outline"
          >
            <span className="sr-only">Go to last page</span>
            <ChevronsRight className="h-4 w-4" />
          </Button>
        </div>
      </div>
    </div>
  );
}
