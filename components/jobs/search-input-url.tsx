'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { useCallback, useEffect, useState } from 'react';
import { useDebouncedCallback } from 'use-debounce';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface SearchInputUrlProps {
  disabled?: boolean;
  placeholder?: string;
  id?: string;
}

/**
 * Search input component with debounced URL updates
 * Follows Pattern 2 from nuqs-todo.md: Controlled Components with Debouncing
 *
 * Key features:
 * - Local state for immediate UI updates
 * - Debounced URL updates (500ms)
 * - Syncs URL changes to local state (browser navigation)
 * - No infinite loops
 */
export function SearchInputUrl({
  disabled = false,
  placeholder = 'Search title, company, description...',
  id = 'search',
}: SearchInputUrlProps) {
  // URL state for search query
  const [search, setSearch] = useQueryState('q', parseAsString.withDefault(''));

  // Local state for immediate UI updates
  const [localSearch, setLocalSearch] = useState(search);

  // Sync URL changes to local state (browser navigation)
  useEffect(() => {
    setLocalSearch(search);
  }, [search]);

  // Debounced update to URL (500ms delay)
  const debouncedSetSearch = useDebouncedCallback((value: string) => {
    setSearch(value || null);
  }, 500);

  // Handle input changes
  const handleChange = useCallback(
    (newValue: string) => {
      setLocalSearch(newValue); // Immediate UI update
      debouncedSetSearch(newValue); // Debounced URL update
    },
    [debouncedSetSearch]
  );

  return (
    <div>
      <Label htmlFor={id}>Search</Label>
      <Input
        disabled={disabled}
        id={id}
        onChange={(e) => handleChange(e.target.value)}
        placeholder={placeholder}
        value={localSearch}
      />
    </div>
  );
}
