'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { WORKPLACE_TYPES } from '@/lib/workplace';

interface WorkplaceTypesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Workplace Types filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state stores comma-separated string
 * - Converts between array and string automatically
 * - No infinite loops
 */
export function WorkplaceTypesFilterUrl({
  disabled = false,
}: WorkplaceTypesFilterUrlProps) {
  // URL state for workplace types (comma-separated string)
  const [workplaceTypesString, setWorkplaceTypesString] = useQueryState(
    'workplace',
    parseAsString.withDefault('')
  );

  // Convert string to array for UI
  const selectedWorkplaceTypes = workplaceTypesString
    ? workplaceTypesString
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  // Handle checkbox changes
  const handleWorkplaceTypeToggle = (
    workplaceType: string,
    checked: boolean
  ) => {
    let newWorkplaceTypes: string[];

    if (checked) {
      // Add workplace type if not already selected
      newWorkplaceTypes = selectedWorkplaceTypes.includes(workplaceType)
        ? selectedWorkplaceTypes
        : [...selectedWorkplaceTypes, workplaceType];
    } else {
      // Remove workplace type
      newWorkplaceTypes = selectedWorkplaceTypes.filter(
        (type) => type !== workplaceType
      );
    }

    // Convert back to comma-separated string for URL
    const newWorkplaceTypesString =
      newWorkplaceTypes.length > 0 ? newWorkplaceTypes.join(',') : null; // null removes the parameter from URL

    setWorkplaceTypesString(newWorkplaceTypesString);
  };

  return (
    <div>
      <Label className="font-medium text-sm">Workplace Types</Label>
      <div className="mt-2 space-y-2">
        {WORKPLACE_TYPES.map((workplaceType) => (
          <div className="flex items-center space-x-2" key={workplaceType}>
            <Checkbox
              checked={selectedWorkplaceTypes.includes(workplaceType)}
              disabled={disabled}
              id={`workplace-type-${workplaceType}`}
              onCheckedChange={(checked) =>
                handleWorkplaceTypeToggle(workplaceType, checked as boolean)
              }
            />
            <Label
              className="cursor-pointer font-normal text-sm"
              htmlFor={`workplace-type-${workplaceType}`}
            >
              {workplaceType}
            </Label>
          </div>
        ))}
      </div>
      {selectedWorkplaceTypes.length > 0 && (
        <div className="mt-2 text-muted-foreground text-xs">
          Selected: {selectedWorkplaceTypes.join(', ')}
        </div>
      )}
    </div>
  );
}
