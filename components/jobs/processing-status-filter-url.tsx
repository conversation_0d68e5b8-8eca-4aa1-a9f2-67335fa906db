'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface ProcessingStatusFilterUrlProps {
  disabled?: boolean;
}

// Processing status options (matching the legacy implementation)
const processingStatusOptions = [
  { value: 'all', label: 'All Processing Statuses' },
  { value: 'pending', label: 'Pending' },
  { value: 'processing', label: 'Processing' },
  { value: 'completed', label: 'Completed' },
  { value: 'failed', label: 'Failed' },
  { value: 'skipped', label: 'Skipped' },
];

/**
 * Processing Status filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state is the only state (no local state)
 * - Direct updates to URL
 * - No infinite loops
 */
export function ProcessingStatusFilterUrl({
  disabled = false,
}: ProcessingStatusFilterUrlProps) {
  // URL state for processing status filter
  const [processingStatus, setProcessingStatus] = useQueryState(
    'processing',
    parseAsString.withDefault('')
  );

  const handleValueChange = (value: string) => {
    // Set to null to remove from URL when "all" is selected
    setProcessingStatus(value === 'all' ? null : value);
  };

  return (
    <div>
      <Label>Processing Status</Label>
      <Select
        disabled={disabled}
        onValueChange={handleValueChange}
        value={processingStatus || 'all'}
      >
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {processingStatusOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
