'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { countries } from '@/lib/data/countries';

interface CountriesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Countries filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state stores comma-separated string
 * - Searchable country list
 * - Converts between array and string automatically
 * - No infinite loops
 */
export function CountriesFilterUrl({
  disabled = false,
}: CountriesFilterUrlProps) {
  // URL state for countries (comma-separated string)
  const [countriesString, setCountriesString] = useQueryState(
    'countries',
    parseAsString.withDefault('')
  );

  // Local search state for filtering countries list
  const [searchTerm, setSearchTerm] = useState('');

  // Convert string to array for UI
  const selectedCountries = countriesString
    ? countriesString
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  // Filter countries based on search term
  const filteredCountries = countries.filter((country) =>
    country.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle checkbox changes
  const handleCountryToggle = (country: string, checked: boolean) => {
    let newCountries: string[];

    if (checked) {
      // Add country if not already selected
      newCountries = selectedCountries.includes(country)
        ? selectedCountries
        : [...selectedCountries, country];
    } else {
      // Remove country
      newCountries = selectedCountries.filter((c) => c !== country);
    }

    // Convert back to comma-separated string for URL
    const newCountriesString =
      newCountries.length > 0 ? newCountries.join(',') : null; // null removes the parameter from URL

    setCountriesString(newCountriesString);
  };

  // Clear all selected countries
  const clearAllCountries = () => {
    setCountriesString(null);
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <Label className="font-medium text-sm">Countries</Label>
        {selectedCountries.length > 0 && (
          <Button
            className="h-auto p-1 text-xs"
            disabled={disabled}
            onClick={clearAllCountries}
            size="sm"
            variant="ghost"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Search input */}
      <div className="mt-2">
        <Input
          className="text-sm"
          disabled={disabled}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search countries..."
          value={searchTerm}
        />
      </div>

      {/* Selected countries summary */}
      {selectedCountries.length > 0 && (
        <div className="mt-2 text-muted-foreground text-xs">
          Selected ({selectedCountries.length}):{' '}
          {selectedCountries.slice(0, 3).join(', ')}
          {selectedCountries.length > 3 &&
            ` and ${selectedCountries.length - 3} more`}
        </div>
      )}

      {/* Countries list */}
      <div className="mt-2 max-h-48 space-y-2 overflow-y-auto rounded border p-2">
        {filteredCountries.length === 0 ? (
          <div className="py-2 text-center text-muted-foreground text-sm">
            No countries found
          </div>
        ) : (
          filteredCountries.map((country) => (
            <div className="flex items-center space-x-2" key={country}>
              <Checkbox
                checked={selectedCountries.includes(country)}
                disabled={disabled}
                id={`country-${country}`}
                onCheckedChange={(checked) =>
                  handleCountryToggle(country, checked as boolean)
                }
              />
              <Label
                className="cursor-pointer font-normal text-sm"
                htmlFor={`country-${country}`}
              >
                {country}
              </Label>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
