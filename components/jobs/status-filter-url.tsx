'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

interface StatusFilterUrlProps {
  disabled?: boolean;
}

// Status options (matching the legacy implementation)
const statusOptions = [
  { value: 'all', label: 'All Statuses' },
  { value: 'active', label: 'Active' },
  { value: 'inactive', label: 'Inactive' },
  { value: 'pending', label: 'Pending' },
  { value: 'expired', label: 'Expired' },
];

/**
 * Status filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state is the only state (no local state)
 * - Direct updates to URL
 * - No infinite loops
 */
export function StatusFilterUrl({ disabled = false }: StatusFilterUrlProps) {
  // URL state for status filter
  const [status, setStatus] = useQueryState(
    'status',
    parseAsString.withDefault('')
  );

  const handleValueChange = (value: string) => {
    // Set to null to remove from URL when "all" is selected
    setStatus(value === 'all' ? null : value);
  };

  return (
    <div>
      <Label>Status</Label>
      <Select
        disabled={disabled}
        onValueChange={handleValueChange}
        value={status || 'all'}
      >
        <SelectTrigger>
          <SelectValue />
        </SelectTrigger>
        <SelectContent>
          {statusOptions.map((option) => (
            <SelectItem key={option.value} value={option.value}>
              {option.label}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
    </div>
  );
}
