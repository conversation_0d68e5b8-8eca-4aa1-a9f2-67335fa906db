'use client';

import { parseAsInteger, useQueryStates } from 'nuqs';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

interface SalaryRangeFilterUrlProps {
  disabled?: boolean;
}

/**
 * Salary Range filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state for both min and max salary using parseAsInteger
 * - Direct number input handling
 * - Clear functionality
 * - No infinite loops
 */
export function SalaryRangeFilterUrl({
  disabled = false,
}: SalaryRangeFilterUrlProps) {
  // URL state for salary range (both min and max)
  const [salaryRange, setSalaryRange] = useQueryStates({
    salaryMin: parseAsInteger,
    salaryMax: parseAsInteger,
  });

  // Handle min salary change
  const handleMinSalaryChange = (value: string) => {
    const numValue = value === '' ? null : Number.parseInt(value, 10);
    if (
      value === '' ||
      (numValue !== null && !Number.isNaN(numValue) && numValue >= 0)
    ) {
      setSalaryRange({ salaryMin: numValue });
    }
  };

  // Handle max salary change
  const handleMaxSalaryChange = (value: string) => {
    const numValue = value === '' ? null : Number.parseInt(value, 10);
    if (
      value === '' ||
      (numValue !== null && !Number.isNaN(numValue) && numValue >= 0)
    ) {
      setSalaryRange({ salaryMax: numValue });
    }
  };

  // Clear salary range
  const clearSalaryRange = () => {
    setSalaryRange({ salaryMin: null, salaryMax: null });
  };

  // Check if any salary filter is active
  const hasSalaryFilter =
    salaryRange.salaryMin !== null || salaryRange.salaryMax !== null;

  // Format number for display
  const formatSalary = (value: number | null): string => {
    if (value === null) {
      return '';
    }
    return value.toLocaleString();
  };

  // Parse display value back to number
  const parseSalaryInput = (value: string): string => {
    // Remove commas and non-numeric characters except for the number itself
    return value.replace(/[^\d]/g, '');
  };

  return (
    <div>
      <div className="flex items-center justify-between">
        <Label className="font-medium text-sm">Salary Range</Label>
        {hasSalaryFilter && (
          <Button
            className="h-auto p-1 text-xs"
            disabled={disabled}
            onClick={clearSalaryRange}
            size="sm"
            variant="ghost"
          >
            Clear
          </Button>
        )}
      </div>

      <div className="mt-2 space-y-3">
        {/* Minimum Salary */}
        <div>
          <Label className="text-muted-foreground text-xs" htmlFor="salary-min">
            Minimum Salary
          </Label>
          <div className="relative">
            <span className="-translate-y-1/2 absolute top-1/2 left-3 transform text-muted-foreground text-sm">
              $
            </span>
            <Input
              className="pl-7"
              disabled={disabled}
              id="salary-min"
              onChange={(e) => {
                const cleanValue = parseSalaryInput(e.target.value);
                handleMinSalaryChange(cleanValue);
              }}
              placeholder="e.g., 80,000"
              type="text"
              value={formatSalary(salaryRange.salaryMin)}
            />
          </div>
        </div>

        {/* Maximum Salary */}
        <div>
          <Label className="text-muted-foreground text-xs" htmlFor="salary-max">
            Maximum Salary
          </Label>
          <div className="relative">
            <span className="-translate-y-1/2 absolute top-1/2 left-3 transform text-muted-foreground text-sm">
              $
            </span>
            <Input
              className="pl-7"
              disabled={disabled}
              id="salary-max"
              onChange={(e) => {
                const cleanValue = parseSalaryInput(e.target.value);
                handleMaxSalaryChange(cleanValue);
              }}
              placeholder="e.g., 150,000"
              type="text"
              value={formatSalary(salaryRange.salaryMax)}
            />
          </div>
        </div>

        {/* Range Summary */}
        {hasSalaryFilter && (
          <div className="text-muted-foreground text-xs">
            Range:{' '}
            {salaryRange.salaryMin
              ? `$${formatSalary(salaryRange.salaryMin)}`
              : 'No min'}{' '}
            -{' '}
            {salaryRange.salaryMax
              ? `$${formatSalary(salaryRange.salaryMax)}`
              : 'No max'}
          </div>
        )}

        {/* Quick preset buttons */}
        <div className="flex flex-wrap gap-1">
          <Button
            className="h-7 text-xs"
            disabled={disabled}
            onClick={() =>
              setSalaryRange({ salaryMin: 50_000, salaryMax: 100_000 })
            }
            size="sm"
            variant="outline"
          >
            $50K-$100K
          </Button>
          <Button
            className="h-7 text-xs"
            disabled={disabled}
            onClick={() =>
              setSalaryRange({ salaryMin: 100_000, salaryMax: 150_000 })
            }
            size="sm"
            variant="outline"
          >
            $100K-$150K
          </Button>
          <Button
            className="h-7 text-xs"
            disabled={disabled}
            onClick={() =>
              setSalaryRange({ salaryMin: 150_000, salaryMax: null })
            }
            size="sm"
            variant="outline"
          >
            $150K+
          </Button>
        </div>
      </div>
    </div>
  );
}
