'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { JOB_TYPES } from '@/lib/job-types';

interface JobTypesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Job Types filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state stores comma-separated string
 * - Converts between array and string automatically
 * - No infinite loops
 */
export function JobTypesFilterUrl({
  disabled = false,
}: JobTypesFilterUrlProps) {
  // URL state for job types (comma-separated string)
  const [jobTypesString, setJobTypesString] = useQueryState(
    'types',
    parseAsString.withDefault('')
  );

  // Convert string to array for UI
  const selectedJobTypes = jobTypesString
    ? jobTypesString
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  // Handle checkbox changes
  const handleJobTypeToggle = (jobType: string, checked: boolean) => {
    let newJobTypes: string[];

    if (checked) {
      // Add job type if not already selected
      newJobTypes = selectedJobTypes.includes(jobType)
        ? selectedJobTypes
        : [...selectedJobTypes, jobType];
    } else {
      // Remove job type
      newJobTypes = selectedJobTypes.filter((type) => type !== jobType);
    }

    // Convert back to comma-separated string for URL
    const newJobTypesString =
      newJobTypes.length > 0 ? newJobTypes.join(',') : null; // null removes the parameter from URL

    setJobTypesString(newJobTypesString);
  };

  return (
    <div>
      <Label className="font-medium text-sm">Job Types</Label>
      <div className="mt-2 space-y-2">
        {JOB_TYPES.map((jobType) => (
          <div className="flex items-center space-x-2" key={jobType}>
            <Checkbox
              checked={selectedJobTypes.includes(jobType)}
              disabled={disabled}
              id={`job-type-${jobType}`}
              onCheckedChange={(checked) =>
                handleJobTypeToggle(jobType, checked as boolean)
              }
            />
            <Label
              className="cursor-pointer font-normal text-sm"
              htmlFor={`job-type-${jobType}`}
            >
              {jobType}
            </Label>
          </div>
        ))}
      </div>
      {selectedJobTypes.length > 0 && (
        <div className="mt-2 text-muted-foreground text-xs">
          Selected: {selectedJobTypes.join(', ')}
        </div>
      )}
    </div>
  );
}
