'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { CAREER_LEVELS } from '@/lib/career-levels';

interface CareerLevelsFilterUrlProps {
  disabled?: boolean;
}

/**
 * Career Levels filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state stores comma-separated string
 * - Converts between array and string automatically
 * - No infinite loops
 */
export function CareerLevelsFilterUrl({
  disabled = false,
}: CareerLevelsFilterUrlProps) {
  // URL state for career levels (comma-separated string)
  const [careerLevelsString, setCareerLevelsString] = useQueryState(
    'levels',
    parseAsString.withDefault('')
  );

  // Convert string to array for UI
  const selectedCareerLevels = careerLevelsString
    ? careerLevelsString
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  // Handle checkbox changes
  const handleCareerLevelToggle = (careerLevel: string, checked: boolean) => {
    let newCareerLevels: string[];

    if (checked) {
      // Add career level if not already selected
      newCareerLevels = selectedCareerLevels.includes(careerLevel)
        ? selectedCareerLevels
        : [...selectedCareerLevels, careerLevel];
    } else {
      // Remove career level
      newCareerLevels = selectedCareerLevels.filter(
        (level) => level !== careerLevel
      );
    }

    // Convert back to comma-separated string for URL
    const newCareerLevelsString =
      newCareerLevels.length > 0 ? newCareerLevels.join(',') : null; // null removes the parameter from URL

    setCareerLevelsString(newCareerLevelsString);
  };

  return (
    <div>
      <Label className="font-medium text-sm">Career Levels</Label>
      <div className="mt-2 max-h-48 space-y-2 overflow-y-auto">
        {CAREER_LEVELS.map((careerLevel) => (
          <div className="flex items-center space-x-2" key={careerLevel}>
            <Checkbox
              checked={selectedCareerLevels.includes(careerLevel)}
              disabled={disabled}
              id={`career-level-${careerLevel}`}
              onCheckedChange={(checked) =>
                handleCareerLevelToggle(careerLevel, checked as boolean)
              }
            />
            <Label
              className="cursor-pointer font-normal text-sm"
              htmlFor={`career-level-${careerLevel}`}
            >
              {careerLevel}
            </Label>
          </div>
        ))}
      </div>
      {selectedCareerLevels.length > 0 && (
        <div className="mt-2 text-muted-foreground text-xs">
          Selected: {selectedCareerLevels.join(', ')}
        </div>
      )}
    </div>
  );
}
