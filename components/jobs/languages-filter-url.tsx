'use client';

import { parseAsString, useQueryState } from 'nuqs';
import { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { LANGUAGES } from '@/lib/data/languages';

interface LanguagesFilterUrlProps {
  disabled?: boolean;
}

/**
 * Languages filter component with URL state
 * Follows Pattern 1 from nuqs-todo.md: Single Source of Truth
 *
 * Key features:
 * - URL state stores comma-separated string of language codes
 * - Searchable language list
 * - Converts between array and string automatically
 * - No infinite loops
 */
export function LanguagesFilterUrl({
  disabled = false,
}: LanguagesFilterUrlProps) {
  // URL state for languages (comma-separated string of language codes)
  const [languagesString, setLanguagesString] = useQueryState(
    'languages',
    parseAsString.withDefault('')
  );

  // Local search state for filtering languages list
  const [searchTerm, setSearchTerm] = useState('');

  // Convert string to array for UI
  const selectedLanguageCodes = languagesString
    ? languagesString
        .split(',')
        .map((s) => s.trim())
        .filter(Boolean)
    : [];

  // Filter languages based on search term
  const filteredLanguages = LANGUAGES.filter(
    (language) =>
      language.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      language.code.toLowerCase().includes(searchTerm.toLowerCase())
  );

  // Handle checkbox changes
  const handleLanguageToggle = (languageCode: string, checked: boolean) => {
    let newLanguageCodes: string[];

    if (checked) {
      // Add language code if not already selected
      newLanguageCodes = selectedLanguageCodes.includes(languageCode)
        ? selectedLanguageCodes
        : [...selectedLanguageCodes, languageCode];
    } else {
      // Remove language code
      newLanguageCodes = selectedLanguageCodes.filter(
        (code) => code !== languageCode
      );
    }

    // Convert back to comma-separated string for URL
    const newLanguagesString =
      newLanguageCodes.length > 0 ? newLanguageCodes.join(',') : null; // null removes the parameter from URL

    setLanguagesString(newLanguagesString);
  };

  // Clear all selected languages
  const clearAllLanguages = () => {
    setLanguagesString(null);
  };

  // Get display names for selected languages
  const selectedLanguageNames = selectedLanguageCodes.map((code) => {
    const language = LANGUAGES.find((lang) => lang.code === code);
    return language ? language.name : code;
  });

  return (
    <div>
      <div className="flex items-center justify-between">
        <Label className="font-medium text-sm">Languages</Label>
        {selectedLanguageCodes.length > 0 && (
          <Button
            className="h-auto p-1 text-xs"
            disabled={disabled}
            onClick={clearAllLanguages}
            size="sm"
            variant="ghost"
          >
            Clear All
          </Button>
        )}
      </div>

      {/* Search input */}
      <div className="mt-2">
        <Input
          className="text-sm"
          disabled={disabled}
          onChange={(e) => setSearchTerm(e.target.value)}
          placeholder="Search languages..."
          value={searchTerm}
        />
      </div>

      {/* Selected languages summary */}
      {selectedLanguageCodes.length > 0 && (
        <div className="mt-2 text-muted-foreground text-xs">
          Selected ({selectedLanguageCodes.length}):{' '}
          {selectedLanguageNames.slice(0, 3).join(', ')}
          {selectedLanguageNames.length > 3 &&
            ` and ${selectedLanguageNames.length - 3} more`}
        </div>
      )}

      {/* Languages list */}
      <div className="mt-2 max-h-48 space-y-2 overflow-y-auto rounded border p-2">
        {filteredLanguages.length === 0 ? (
          <div className="py-2 text-center text-muted-foreground text-sm">
            No languages found
          </div>
        ) : (
          filteredLanguages.map((language) => (
            <div className="flex items-center space-x-2" key={language.code}>
              <Checkbox
                checked={selectedLanguageCodes.includes(language.code)}
                disabled={disabled}
                id={`language-${language.code}`}
                onCheckedChange={(checked) =>
                  handleLanguageToggle(language.code, checked as boolean)
                }
              />
              <Label
                className="cursor-pointer font-normal text-sm"
                htmlFor={`language-${language.code}`}
              >
                {language.name} ({language.code})
              </Label>
            </div>
          ))
        )}
      </div>
    </div>
  );
}
