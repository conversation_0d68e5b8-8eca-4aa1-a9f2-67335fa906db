import {
  Bot,
  CheckCircle,
  Circle,
  Eye,
  Monitor,
  Radio,
  RefreshCw,
  Target,
} from 'lucide-react';
import type { TimelineActivity } from '@/types/timeline';

interface TimelineIconProps {
  activity: TimelineActivity;
}

export const TimelineIcon: React.FC<TimelineIconProps> = ({ activity }) => {
  const getIcon = () => {
    switch (activity.type) {
      case 'created':
        return <Circle className="size-4 text-muted-foreground" />;
      case 'fetched':
        return <Radio className="size-4 text-muted-foreground" />;
      case 'processed':
        return <Bot className="size-4 text-muted-foreground" />;
      case 'synced':
        return <RefreshCw className="size-4 text-muted-foreground" />;
      case 'posted':
        return <Target className="size-4 text-muted-foreground" />;
      case 'monitored':
        return <Monitor className="size-4 text-muted-foreground" />;
      case 'updated':
        return <CheckCircle className="size-4 text-muted-foreground" />;
      case 'viewed':
        return <Eye className="size-4 text-muted-foreground" />;
      default:
        return (
          <div className="size-1.5 rounded-full bg-muted ring-1 ring-border" />
        );
    }
  };

  return (
    <div className="relative flex size-6 flex-none items-center justify-center bg-card">
      {getIcon()}
    </div>
  );
};
