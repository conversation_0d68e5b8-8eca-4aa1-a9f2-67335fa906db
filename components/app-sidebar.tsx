"use client";

import {
  Bell,
  <PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  Briefcase,
  FileText,
  Radio,
  Workflow,
} from "lucide-react";
import type * as React from "react";
import { NavMain } from "@/components/nav-main";
import { NavProjects } from "@/components/nav-projects";
import { NavUser } from "@/components/nav-user";
import { SidebarThemeToggle } from "@/components/sidebar-theme-toggle";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
  SidebarRail,
} from "@/components/ui/sidebar";

// Bordfeed navigation data
const data = {
  user: {
    name: "<PERSON>",
    email: "<EMAIL>",
    avatar: "/avatar.png",
  },
  navMain: [
    {
      title: "Sources",
      url: "/dashboard/sources",
      icon: Radio,
      isActive: true,
      items: [],
    },
    {
      title: "Jobs",
      url: "/dashboard/jobs",
      icon: FileText,
      items: [],
    },
  ],
  management: [
    {
      name: "Workflows",
      url: "/dashboard/workflows",
      icon: Workflow,
    },
    {
      name: "Job Boards",
      url: "/dashboard/boards",
      icon: Briefcase,
    },
    {
      name: "Notifications",
      url: "/dashboard/notifications",
      icon: Bell,
    },
    {
      name: "Docs",
      url: "/docs",
      icon: BookOpen,
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar collapsible="icon" {...props}>
      <SidebarHeader>
        <SidebarMenu>
          <SidebarMenuItem>
            <SidebarMenuButton
              className="data-[state=open]:bg-sidebar-accent data-[state=open]:text-sidebar-accent-foreground"
              size="lg"
            >
              <div className="flex aspect-square size-8 items-center justify-center rounded-lg bg-sidebar-primary text-sidebar-primary-foreground">
                <Bot className="size-4" />
              </div>
              <div className="grid flex-1 text-left text-sm leading-tight">
                <span className="truncate font-medium">Bordfeed</span>
                <span className="truncate text-xs">
                  AI-Powered Job Board Automation
                </span>
              </div>
            </SidebarMenuButton>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.navMain} />
        <NavProjects projects={data.management} />
      </SidebarContent>
      <SidebarFooter>
        <SidebarThemeToggle />
        <NavUser user={data.user} />
      </SidebarFooter>
      <SidebarRail />
    </Sidebar>
  );
}
