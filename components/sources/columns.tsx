'use client';

import type { ColumnDef } from '@tanstack/react-table';
import {
  ArrowUpDown,
  ExternalLink,
  Loader2,
  Play,
  Settings,
  Zap,
} from 'lucide-react';
import { useState } from 'react';
import { toast } from 'sonner';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { getConfigUrl, getRunsUrl, getScheduleUrl } from '@/lib/sources-config';
import { StatusBadge } from '@/lib/utils/status-badge';
import { createActionColumn } from './action-button';
import { useRealTimeJobTracker } from './real-time-job-tracker';

export interface JobSource {
  id: string;
  name: string;
  description: string;
  enabled: boolean;
  actor_id: string;
  stats: {
    last_fetch_at: string | null;
    last_run_status: string | null;
    last_error: string | null;
  };
  schedule: {
    id: string | null;
    name: string | null;
    title: string | null;
    cronExpression: string | null;
    timezone: string | null;
    isEnabled: boolean;
    nextRunAt: string | null;
    lastRunAt: string | null;
    apifyUrl: string | null;
  };
  config: {
    apify_actor_url: string;
  };
  // Computed properties for table display
  status?: 'active' | 'inactive' | 'error' | 'paused' | 'running';
}

export interface SourcesTableProps {
  onTestConnection?: (sourceId: string) => void;
}

// Enhanced Run Now button component with better UX
function RunNowButton({ source }: { source: JobSource }) {
  const [isRunning, setIsRunning] = useState(false);
  const [isTrackingJobs, setIsTrackingJobs] = useState(false);

  // Real-time job tracking hook
  const { jobCount } = useRealTimeJobTracker({
    sourceId: source.id,
    sourceName: source.name,
    isActive: isTrackingJobs,
    onComplete: () => {
      // Stop tracking when ingestion completes
      setIsTrackingJobs(false);
      setIsRunning(false);
    },
  });

  // Helper function to determine button text
  const getButtonText = () => {
    if (isTrackingJobs) {
      return `${jobCount}`;
    }
    if (isRunning) {
      return 'Running...';
    }
    return 'Run';
  };

  const handleRunNow = async () => {
    if (isRunning) {
      return;
    }

    setIsRunning(true);

    // Show initial toast
    const runToastId = toast.loading(`Starting ${source.name} run...`, {
      description: 'Triggering actor run via Apify API',
    });

    try {
      const response = await fetch(`/api/sources/${source.id}/run`, {
        method: 'POST',
      });

      const result = await response.json();

      if (response.ok) {
        // Success toast
        toast.success(`${source.name} run started successfully!`, {
          id: runToastId,
          description: '🔄 Listening for real-time job ingestion...',
          action: result.apifyUrl
            ? {
                label: 'View Run',
                onClick: () => window.open(result.apifyUrl, '_blank'),
              }
            : undefined,
        });

        // Start real-time job tracking
        setIsTrackingJobs(true);

        // Note: Real-time tracking will handle completion and stop running state
      } else {
        // Error toast
        toast.error(`Failed to start ${source.name} run`, {
          id: runToastId,
          description: result.error || 'Unknown error occurred',
        });
        setIsRunning(false);
      }
    } catch (error) {
      // Network error toast
      toast.error(`Network error for ${source.name}`, {
        id: runToastId,
        description:
          error instanceof Error ? error.message : 'Connection failed',
      });
      setIsRunning(false);
    }
    // Note: setIsRunning(false) is handled by onComplete callback for successful runs
  };

  return (
    <Button
      className="h-6 px-2"
      disabled={isRunning || isTrackingJobs}
      onClick={handleRunNow}
      size="sm"
      variant="default"
    >
      {isRunning || isTrackingJobs ? (
        <Loader2 className="mr-1 h-3 w-3 animate-spin" />
      ) : (
        <Zap className="mr-1 h-3 w-3" />
      )}
      {getButtonText()}
    </Button>
  );
}

export function createSourcesColumns(
  onTestConnection?: (sourceId: string) => void
): ColumnDef<JobSource>[] {
  return [
    {
      accessorKey: 'name',
      header: ({ column }) => (
        <Button
          className="h-8 px-2"
          onClick={() => column.toggleSorting(column.getIsSorted() === 'asc')}
          variant="ghost"
        >
          <Zap className="mr-2 h-3 w-3" />
          Source Name
          <ArrowUpDown className="ml-2 h-4 w-4" />
        </Button>
      ),
      cell: ({ row }) => {
        const source = row.original;
        const name = row.getValue('name') as string;
        const configUrl = getConfigUrl(
          source.id,
          source.actor_id,
          source.config?.apify_actor_url
        );

        return (
          <div className="flex items-center space-x-2">
            <Zap className="mr-1 h-3 w-3" />
            <div>
              <a
                className="font-medium text-blue-600 hover:text-blue-800 hover:underline"
                href={configUrl}
                rel="noopener noreferrer"
                target="_blank"
              >
                {name}
              </a>
            </div>
          </div>
        );
      },
    },

    {
      accessorKey: 'stats.last_run_status',
      header: 'Last Run Status',
      cell: ({ row }) => {
        const source = row.original;
        const lastRunStatus = source.stats.last_run_status;
        const lastFetchAt = source.stats.last_fetch_at;

        return (
          <div className="flex flex-col space-y-1">
            <StatusBadge
              fallbackLabel="NOT CONFIGURED"
              status={lastRunStatus}
            />
            {lastFetchAt && (
              <div className="text-muted-foreground text-xs">
                {new Date(lastFetchAt).toLocaleDateString('en-US', {
                  month: 'short',
                  day: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit',
                })}
              </div>
            )}
          </div>
        );
      },
    },

    {
      accessorKey: 'stats.last_error',
      header: 'Error Status',
      cell: ({ row }) => {
        const lastError = row.original.stats.last_error;
        if (!lastError) {
          return (
            <Badge className="text-green-600" variant="outline">
              None
            </Badge>
          );
        }
        return (
          <div className="max-w-[200px]">
            <Badge className="text-xs" variant="destructive">
              Error
            </Badge>
            <div
              className="mt-1 truncate text-muted-foreground text-xs"
              title={lastError}
            >
              {lastError}
            </div>
          </div>
        );
      },
    },
    {
      accessorKey: 'schedule.isEnabled',
      header: 'Schedule Status',
      cell: ({ row }) => {
        const schedule = row.original.schedule;
        if (!schedule.id) {
          return <Badge variant="outline">No Schedule</Badge>;
        }
        return (
          <div className="flex items-center space-x-2">
            <Badge variant={schedule.isEnabled ? 'default' : 'secondary'}>
              {schedule.isEnabled ? 'Enabled' : 'Disabled'}
            </Badge>
          </div>
        );
      },
    },
    {
      accessorKey: 'schedule.nextRunAt',
      header: 'Next Scheduled Run',
      cell: ({ row }) => {
        const schedule = row.original.schedule;
        if (!schedule.nextRunAt) {
          return <span className="text-muted-foreground">-</span>;
        }

        const nextRun = new Date(schedule.nextRunAt);
        const now = new Date();
        const diffInMinutes = Math.floor(
          (nextRun.getTime() - now.getTime()) / 60_000
        );

        let timeText: string;
        if (diffInMinutes <= 0) {
          timeText = 'Due now';
        } else if (diffInMinutes < 60) {
          timeText = `In ${diffInMinutes}m`;
        } else if (diffInMinutes < 1440) {
          timeText = `In ${Math.floor(diffInMinutes / 60)}h`;
        } else {
          timeText = `In ${Math.floor(diffInMinutes / 1440)}d`;
        }

        // Get timezone from schedule or default to UTC
        const timezone = schedule.timezone || 'UTC';

        // Format the date and time with timezone
        const dateTimeOptions: Intl.DateTimeFormatOptions = {
          year: 'numeric',
          month: '2-digit',
          day: '2-digit',
          hour: '2-digit',
          minute: '2-digit',
          second: '2-digit',
          timeZone: timezone,
          timeZoneName: 'short',
        };

        const formattedDateTime = nextRun.toLocaleString(
          'en-US',
          dateTimeOptions
        );

        return (
          <div className="min-w-[180px] text-sm">
            <div className="font-medium font-mono">{timeText}</div>
            <div className="mt-1 text-muted-foreground text-xs leading-tight">
              <div>{formattedDateTime}</div>
              {timezone !== 'UTC' && (
                <div className="text-slate-400">({timezone})</div>
              )}
            </div>
          </div>
        );
      },
    },

    {
      id: 'test',
      header: 'Connection Test',
      cell: ({ row }) => {
        const source = row.original;
        return (
          <Button
            className="h-6 px-2"
            onClick={() => onTestConnection?.(source.id)}
            size="sm"
            variant="outline"
          >
            <Play className="mr-1 h-3 w-3" />
            Test
          </Button>
        );
      },
      enableSorting: false,
    },
    {
      id: 'run-now',
      header: 'Manual Trigger',
      cell: ({ row }) => {
        const source = row.original;
        return <RunNowButton source={source} />;
      },
      enableSorting: false,
    },
    // DRY action button columns using abstractions
    createActionColumn(
      'configure',
      'Configuration',
      Settings,
      'Config',
      (source) =>
        getConfigUrl(source.id, source.actor_id, source.config?.apify_actor_url)
    ),
    createActionColumn(
      'schedule',
      'Schedule Management',
      ExternalLink,
      'Manage',
      (source) => getScheduleUrl(source.id, source.schedule.apifyUrl)
    ),
    createActionColumn('runs', 'View Runs', ExternalLink, 'View', (source) =>
      getRunsUrl(source.id, source.actor_id)
    ),
  ];
}
