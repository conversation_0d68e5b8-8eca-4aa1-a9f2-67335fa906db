'use client';

import {
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from '@tanstack/react-table';
import { Settings } from 'lucide-react';
import { useCallback, useEffect, useMemo } from 'react';
import { toast } from 'sonner';
import { Button } from '@/components/ui/button';
import { DataTableBase } from '@/components/ui/data-table-base';
import { useDataTable } from '@/lib/hooks/use-data-table';
import { getTestUrl } from '@/lib/sources-config';
import { useSourcesStoreHydrated } from '@/lib/stores';
import { createSourcesColumns, type JobSource } from './columns';

// Helper function to log health data to database
const logHealthData = async (
  sourceId: string,
  // biome-ignore lint/suspicious/noExplicitAny: Health data structure varies by source type and test scenario
  healthData: any
): Promise<void> => {
  try {
    const response = await fetch(`/api/sources/${sourceId}`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(healthData),
    });

    // Don't log RLS policy violations
    if (!response.ok && response.status === 403) {
      return; // Skip RLS errors silently
    }
  } catch {
    // Silently ignore logging errors - health logging is not critical
  }
};

// Helper function to handle connection test error response
const handleTestErrorResponse = async (response: Response): Promise<string> => {
  let errorMessage = `Source returned status ${response.status}`;

  try {
    const errorData = await response.json();
    if (response.status === 429 && errorData.details) {
      errorMessage = errorData.details;
    } else if (response.status === 429) {
      errorMessage = 'Rate limit exceeded. Free tier allows 10 requests/hour.';
    } else if (errorData.error) {
      errorMessage = errorData.error;
    }
  } catch {
    // Use default message if JSON parsing fails
  }

  return errorMessage;
};

export function SourcesTable() {
  // Use the Zustand store with hydration protection
  const { sources, loading, error, actions } = useSourcesStoreHydrated();

  // Use the DRY data table hook
  const dataTableState = useDataTable({
    defaultSorting: [{ id: 'name', desc: false }],
    defaultColumnVisibility: {
      // Show most important columns by default
      name: true,
      type: true,
      status: true,
      success_rate: true,
      total_jobs: true,
      // Hide detailed fields by default (can be toggled via View button)
      endpoint: false,
      rate_limit: false,
      data_quality: false,
      avg_processing_time: false,
    },
  });

  // Fetch sources on mount
  useEffect(() => {
    actions.fetch();
  }, [actions]);

  // Set up real-time subscription
  useEffect(() => {
    const unsubscribe = actions.subscribeToRealtime();
    return unsubscribe;
  }, [actions]);

  // Helper function to execute connection test
  const executeConnectionTest = useCallback(
    async (sourceId: string, testUrl: string) => {
      const startTime = Date.now();
      const response = await fetch(testUrl);
      const responseTime = Date.now() - startTime;
      const status: JobSource['status'] = response.ok ? 'active' : 'error';

      // Log health check to database
      await logHealthData(sourceId, {
        status: response.ok ? 'success' : 'error',
        response_time_ms: responseTime,
        jobs_returned: 0, // Just a test connection
        error_message: response.ok ? undefined : `HTTP ${response.status}`,
        metadata: {
          test_url: testUrl,
          method: 'GET',
        },
      });

      return { response, status };
    },
    []
  );

  const handleTestConnection = useCallback(
    async (sourceId: string): Promise<void> => {
      const source = sources.find((s) => s.id === sourceId);
      if (!source) {
        return;
      }

      const loadingToast = toast.loading(
        `Testing connection to ${source.name}...`
      );

      actions.update(sourceId, { status: 'inactive' });

      try {
        const testUrl = getTestUrl(sourceId);

        if (!testUrl) {
          toast.dismiss(loadingToast);
          toast.info(`No test available for ${source.name}`);
          actions.update(sourceId, { status: 'active' });
          return;
        }

        const { response, status } = await executeConnectionTest(
          sourceId,
          testUrl
        );

        actions.update(sourceId, { status });

        toast.dismiss(loadingToast);

        if (response.ok) {
          toast.success('Connection test successful!', {
            description: `${source.name} is responding correctly`,
          });
        } else {
          const errorMessage = await handleTestErrorResponse(response);
          toast.error('Connection test failed', {
            description: errorMessage,
          });
        }
      } catch (err) {
        actions.update(sourceId, { status: 'error' });

        // Log error to database
        await logHealthData(sourceId, {
          status: 'error' as const,
          error_message: err instanceof Error ? err.message : 'Unknown error',
          metadata: {
            source_id: sourceId,
            error_type: 'connection_test',
          },
        });

        toast.dismiss(loadingToast);
        toast.error('Connection test failed', {
          description: `${source.name}: ${
            err instanceof Error ? err.message : 'Unknown error'
          }`,
        });
      }
    },
    [sources, executeConnectionTest, actions]
  );

  const sourcesColumns = useMemo(
    () => createSourcesColumns(handleTestConnection),
    [handleTestConnection]
  );

  const table = useReactTable({
    data: sources,
    columns: sourcesColumns,
    getCoreRowModel: getCoreRowModel(),
    getPaginationRowModel: getPaginationRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    ...dataTableState.getTableProps(),
  });

  const testSingleSource = useCallback(
    async (source: JobSource): Promise<JobSource> => {
      try {
        const testUrl = getTestUrl(source.id);
        if (!testUrl) {
          return { ...source, status: 'active' };
        }

        const response = await fetch(testUrl);
        const status: JobSource['status'] = response.ok ? 'active' : 'error';

        await logHealthData(source.id, {
          status: response.ok ? 'success' : 'error',
          response_time_ms: 100,
        });

        return { ...source, status };
      } catch {
        return { ...source, status: 'error' };
      }
    },
    []
  );

  const testAllConnections = useCallback(async (): Promise<void> => {
    const promise = (async () => {
      const updatedSources = await Promise.all(sources.map(testSingleSource));
      for (const source of updatedSources) {
        actions.update(source.id, { status: source.status });
      }
    })();

    await toast.promise(promise, {
      loading: 'Testing all connections...',
      success: 'All connection tests completed!',
      error: 'Some connection tests failed',
    });
  }, [sources, testSingleSource, actions]);

  const refreshSources = useCallback(async (): Promise<void> => {
    const promise = actions.fetch();

    await toast.promise(promise, {
      loading: 'Refreshing source statistics...',
      success: 'Sources refreshed successfully!',
      error: 'Failed to refresh sources',
    });
  }, [actions]);

  if (loading && sources.length === 0) {
    return <div>Loading sources...</div>;
  }

  if (error) {
    return <div>Error loading sources: {error}</div>;
  }

  return (
    <div className="flex flex-col gap-4">
      <div className="flex items-center justify-between">
        <Button onClick={refreshSources} size="sm">
          Refresh Sources
        </Button>
        <div className="flex gap-2">
          <Button onClick={testAllConnections} size="sm" variant="outline">
            Test All Connections
          </Button>
          <Button size="sm" variant="outline">
            <Settings className="mr-2 h-4 w-4" />
            Source Settings
          </Button>
        </div>
      </div>
      <DataTableBase
        filterPlaceholder="Filter sources..."
        loading={loading}
        table={table}
      />
    </div>
  );
}
