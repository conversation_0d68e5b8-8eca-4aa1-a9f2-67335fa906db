import type { LucideIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import type { JobSource } from './columns';

interface ActionButtonProps {
  source: JobSource;
  icon: LucideIcon;
  label: string;
  getUrl: (source: JobSource) => string | null;
  disabled?: boolean;
}

export function ActionButton({
  source,
  icon: Icon,
  label,
  getUrl,
  disabled,
}: ActionButtonProps) {
  const handleClick = () => {
    const url = getUrl(source);
    if (url) {
      window.open(url, '_blank');
    }
  };

  return (
    <Button
      className="h-6 px-2"
      disabled={disabled || !getUrl(source)}
      onClick={handleClick}
      size="sm"
      variant="outline"
    >
      <Icon className="mr-1 h-3 w-3" />
      {label}
    </Button>
  );
}

// Factory function to create column definitions for action buttons
export function createActionColumn(
  id: string,
  header: string,
  icon: LucideIcon,
  label: string,
  getUrl: (source: JobSource) => string | null
) {
  return {
    id,
    header,
    cell: ({ row }: { row: { original: JobSource } }) => (
      <ActionButton
        getUrl={getUrl}
        icon={icon}
        label={label}
        source={row.original}
      />
    ),
    enableSorting: false,
  };
}
