---
title: Reference
description: Technical reference materials and documentation for Bordfeed
---

This section contains technical reference materials, specifications, and detailed documentation for Bordfeed's features and capabilities.

## Documentation Reference

### Changelog

Complete version history with detailed release notes, bug fixes, and feature additions following semantic versioning principles.

View the [complete changelog](./changelog) for detailed release information.

### Job Data Fields

Comprehensive reference for all 35+ extracted job fields including data types, validation rules, and AI processing details.

### Filter Reference

Complete guide to all available job filtering options including:

- **Job Types** (9 types): Full-time, Part-time, Contract, Freelance, etc.
- **Workplace Types** (3 types): On-site, Remote, Hybrid
- **Remote Regions** (13 regions): Worldwide, Americas Only, Europe Only, etc.
- **Career Levels** (18 levels): From Internship to C-Level and Founder
- **Currencies** (68 currencies): All major fiat and cryptocurrencies
- **Countries** (248 countries): Complete worldwide coverage
- **Languages** (183 languages): ISO 639-1 language codes

### Constants & Enums

Reference documentation for all system constants, enumerations, and predefined values used throughout Bordfeed.

## Technical Specifications

### Database Schema

Complete PostgreSQL schema documentation including:

- Table structures and relationships
- Field definitions and constraints
- Indexes and performance optimizations
- Row Level Security (RLS) policies

### API Specifications

- **[OpenAPI Specification](/api/openapi.json)** - Complete API documentation
- **[Rate Limiting](../api/rate-limiting)** - API rate limiting policies and implementation
- **[Versioning Strategy](../api/versioning)** - API versioning approach and migration guides

### Integration Schemas

Data schemas and interfaces for external integrations:

- Airtable field mappings and validation
- Apify actor input/output formats
- Webhook payload structures
- QStash message formats

## Performance Reference

### AI Processing Costs

Detailed cost analysis for AI-powered job extraction:

- Token usage patterns by job type and complexity
- Cost optimization strategies
- Performance benchmarks and metrics

### System Limits

Reference for system limitations and constraints:

- API rate limits by endpoint type
- Database connection limits
- Processing queue capacities
- File size and payload limits

### Performance Benchmarks

System performance metrics and benchmarks:

- Response time targets by endpoint
- Processing throughput capabilities
- Concurrent user limits
- Resource utilization patterns

## Security Reference

### Authentication & Authorization

Security model documentation:

- QStash signature verification
- API key management
- Access control patterns
- Security headers and policies

### Data Protection

Data security and privacy measures:

- Encryption standards (AES-256-GCM)
- Data retention policies
- GDPR compliance measures
- Secure data transmission

## Troubleshooting Reference

### Error Codes

Complete reference of error codes and their meanings:

- HTTP status codes and their contexts
- Application-specific error codes
- Recovery strategies and solutions

### Common Issues

Frequently encountered issues and their solutions:

- Integration problems and fixes
- Performance issues and optimizations
- Configuration errors and corrections

### Debug Information

Debugging tools and techniques:

- Log analysis and interpretation
- Performance monitoring and profiling
- Error tracking and resolution

---

_Reference documentation is continuously updated to reflect the latest system capabilities and changes._
