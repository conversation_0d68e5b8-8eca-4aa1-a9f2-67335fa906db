---
title: API Examples
description: TypeScript examples for common Bordfeed API operations
---

# API Examples (TypeScript Only)

This page shows practical **TypeScript** examples for integrating with the Bordfeed API. Each snippet is ready to paste into a Next.js / Node.js project.

> **Prerequisites**  
> – Node 18+  
> – `fetch` available globally or `node-fetch` polyfill  
> – `UPSTASH_SIGNATURE` set in your environment variables

---

## Process a Single Job

```typescript
interface IngestResponse {
  jobId: string;
  status: "processing" | "completed" | "failed";
}

async function processJob(url: string): Promise<IngestResponse> {
  const res = await fetch("https://bordfeed.com/api/pipeline-ingest", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      "Upstash-Signature": process.env.UPSTASH_SIGNATURE as string,
    },
    body: JSON.stringify({ url, source: "custom" }),
  });

  if (!res.ok) throw new Error(`HTTP ${res.status}`);
  return res.json();
}

// Usage
const { jobId } = await processJob("https://example.com/job-posting");
console.log("Job queued →", jobId);
```

---

## Check System Health

```typescript
interface HealthResponse {
  status: "healthy" | "degraded" | "unhealthy";
  timestamp: string;
}

async function getHealth(): Promise<HealthResponse> {
  const res = await fetch("https://bordfeed.com/api/health");
  if (!res.ok) throw new Error("Unable to fetch health");
  return res.json();
}

const health = await getHealth();
console.log("System status:", health.status);
```

---

## Webhook Validation (Next.js)

```typescript
import { verifySignature } from "@upstash/qstash/nextjs";

export async function POST(request: Request) {
  // 1️⃣ Verify signature
  const isValid = await verifySignature(request);
  if (!isValid) return new Response("Unauthorized", { status: 401 });

  // 2️⃣ Parse body
  const { event, payload } = await request.json();

  // 3️⃣ Handle events
  switch (event) {
    case "job.processed":
      console.log("Job processed:", payload.jobId);
      break;
    case "job.failed":
      console.error("Job failed:", payload.jobId);
      break;
  }

  return new Response("OK");
}
```

---

## Batch Job Processing Helper

```typescript
async function processBatch(urls: string[]) {
  const responses = await Promise.all(
    urls.map((url) =>
      fetch("https://bordfeed.com/api/pipeline-ingest", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          "Upstash-Signature": process.env.UPSTASH_SIGNATURE as string,
        },
        body: JSON.stringify({ url, source: "custom" }),
      })
    )
  );

  const results = await Promise.all(responses.map((r) => r.json()));
  console.table(results);
}

await processBatch(["https://example.com/job1", "https://example.com/job2"]);
```

---

## Robust API Request Utility

```typescript
class BordfeedClient {
  constructor(
    private signature: string,
    private base = "https://bordfeed.com/api"
  ) {}

  private async request<T>(endpoint: string, init?: RequestInit): Promise<T> {
    const res = await fetch(`${this.base}${endpoint}`, {
      ...init,
      headers: {
        "Content-Type": "application/json",
        "Upstash-Signature": this.signature,
        ...init?.headers,
      },
    });

    if (!res.ok) {
      const error = await res.json().catch(() => ({}));
      throw new Error(error.message || `HTTP ${res.status}`);
    }
    return res.json();
  }

  // Example wrapper
  health() {
    return this.request<{ status: string }>("/health");
  }
}

// Usage
const bf = new BordfeedClient(process.env.UPSTASH_SIGNATURE!);
const sys = await bf.health();
console.log(sys.status);
```

---

Need more endpoints? Explore the **Interactive API Playground** for live request generation.
