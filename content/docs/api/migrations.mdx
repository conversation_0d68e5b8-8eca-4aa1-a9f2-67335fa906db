---
title: Migration Guides
description: Step-by-step guides for upgrading between Bordfeed API versions
---

# API Migration Guides

This page will track all breaking changes and provide hands-on instructions for migrating your integration to newer versions.

> **Current Stable**: **v1**  
> No breaking changes at this time.

---

## How We Version

Bordfeed follows **semantic versioning**:

| Version   | Meaning                           |
| --------- | --------------------------------- |
| **MAJOR** | Incompatible / breaking changes   |
| **MINOR** | Back-compatible feature additions |
| **PATCH** | Back-compatible bug fixes         |

For full details see [API Versioning](/docs/api/versioning).

---

## v0 → v1 (Initial Stable Release)

> _All users should already be on v1._  
> If you still use a pre-stable endpoint shape please contact support.

### Removed Endpoints

- `/api/job-stats` → replaced by `/api/jobs/{id}` statistics field

### Parameter Renames

| Old        | New                  |
| ---------- | -------------------- |
| `priority` | `processingPriority` |

### Migration Checklist

1. Update OpenAPI client codegen to **v1** spec
2. Search your codebase for removed endpoint paths
3. Update renamed parameters and response fields

---

## Future v2 Preview _(Draft)_

Planned changes (subject to feedback):

- Move authentication to permanent API keys (signatures remain supported)
- Replace `salary_min` / `salary_max` query params with `salaryRange`
- Unified pagination response object

Follow the [Changelog](/docs/reference/changelog) for real-time progress.
