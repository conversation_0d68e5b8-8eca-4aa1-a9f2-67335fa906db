---
title: Authentication
description: API authentication and security best practices
---

# Authentication

Bordfeed API uses QStash signature verification to secure webhook endpoints and API calls.

## QStash Signatures

QStash provides cryptographic signatures to verify that requests are coming from authorized sources.

### How It Works

1. **Request Signing**: QStash signs each request with a secret key
2. **Signature Header**: The signature is included in the `Upstash-Signature` header
3. **Verification**: Our API verifies the signature before processing the request

### Implementation

```typescript
// Example: Verifying QStash signature in Next.js
import { verifySignature } from "@upstash/qstash/nextjs";

export async function POST(request: Request) {
  // Verify the signature
  const isValid = await verifySignature(request);

  if (!isValid) {
    return new Response("Unauthorized", { status: 401 });
  }

  // Process the request
  const data = await request.json();
  // ... your logic here
}
```

## API Keys (Coming Soon)

We're working on implementing API key authentication for direct API access. This will allow you to:

- Generate multiple API keys per account
- Set expiration dates and scopes
- Monitor usage per key
- Revoke keys instantly

## Security Best Practices

### Keep Secrets Secure

- Never commit API keys or secrets to version control
- Use environment variables for sensitive data
- Rotate keys regularly

### Use HTTPS

Always use HTTPS when making API requests to ensure data encryption in transit.

### Validate Webhooks

Always verify webhook signatures to ensure requests are from legitimate sources:

```javascript
// Example webhook validation
async function handleWebhook(request) {
  const signature = request.headers.get("Upstash-Signature");

  if (!signature) {
    throw new Error("Missing signature");
  }

  const isValid = await verifySignature(signature, request.body);

  if (!isValid) {
    throw new Error("Invalid signature");
  }

  // Process webhook
}
```

### Rate Limiting

Respect rate limits to avoid service disruption:

- Monitor the `X-RateLimit-*` headers in responses
- Implement exponential backoff for retries
- Cache responses when appropriate

## Error Handling

Handle authentication errors gracefully:

```javascript
try {
  const response = await fetch("/api/endpoint", {
    headers: {
      "Upstash-Signature": signature,
      "Content-Type": "application/json",
    },
  });

  if (response.status === 401) {
    // Handle authentication error
    console.error("Authentication failed");
  }
} catch (error) {
  // Handle network or other errors
  console.error("Request failed:", error);
}
```

## Support

If you're having authentication issues:

1. Verify your signature is correctly formatted
2. Check that your QStash configuration is up to date
3. Ensure you're using the correct base URL
4. Contact support with your request ID for debugging
