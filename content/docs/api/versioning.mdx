---
title: API Versioning
description: Understanding API versions and migration strategies
---

# API Versioning

Bordfeed follows semantic versioning for API changes to ensure backward compatibility and smooth migrations.

## Current Version

**v1** - Stable (Current)

All API endpoints are currently on version 1. The base URL structure is:

```
https://bordfeed.com/api/{endpoint}
```

## Versioning Strategy

We follow these principles for API versioning:

### Backward Compatible Changes

These changes don't require a new version:

- Adding new endpoints
- Adding optional parameters
- Adding new fields to responses
- Adding new webhook events

### Breaking Changes

These changes require a new major version:

- Removing endpoints
- Changing required parameters
- Removing response fields
- Changing authentication methods
- Modifying data types

## Version Lifecycle

1. **Beta** - New features in testing
2. **Stable** - Production-ready
3. **Deprecated** - Scheduled for removal (6 months notice)
4. **Sunset** - No longer available

## Migration Guide

When we release a new API version:

1. **Announcement** - 6 months before deprecation
2. **Migration Period** - Both versions supported
3. **Deprecation** - Old version marked deprecated
4. **Sunset** - Old version removed

### Migration Best Practices

```javascript
// Use version headers for gradual migration
const response = await fetch("/api/jobs", {
  headers: {
    "API-Version": "v2", // Opt into new version
    "Content-Type": "application/json",
  },
});
```

## Changelog

View the [API Changelog](/docs/reference/changelog) for detailed version history.

## Deprecation Notices

Currently, there are no deprecated endpoints.

## Getting Help

- Check migration guides for version-specific changes
- Test in development environment first
- Contact support for migration assistance
