---
title: API Rate Limiting
description: Comprehensive rate limiting strategy to protect API from abuse and ensure fair usage
---

This document outlines the comprehensive rate limiting strategy for the Bordfeed API. Rate limiting protects the API from abuse, ensures fair usage, and maintains service quality for all users.

## Rate Limiting Categories

### Public Endpoints (No Authentication)

**Limits**: 100 requests per minute per IP

**Endpoints**:

- `GET /api/health`
- `GET /api/versions`
- `GET /api/monitoring/health`

**Rationale**: Allow reasonable health checking and monitoring while preventing abuse.

### Webhook Endpoints (QStash Authentication)

**Limits**: 1,000 requests per minute per source

**Endpoints**:

- `POST /api/webhook-callbacks`
- `POST /api/workable-webhook`
- `POST /api/wwr-webhook`
- `POST /api/jobdata-webhook`

**Rationale**: High throughput needed for webhook processing, but limited by source to prevent abuse.

### Data Processing Endpoints (High Resource Usage)

**Limits**: 50 requests per minute per user

**Endpoints**:

- `POST /api/extract`
- `POST /api/pipeline-ingest`
- `POST /api/jobs/process-batch`

**Rationale**: These endpoints consume significant resources (AI processing, database writes).

### Configuration Endpoints (Admin Operations)

**Limits**: 200 requests per minute per user

**Endpoints**:

- `GET /api/airtable-config`
- `POST /api/airtable-config`
- `GET /api/board-schema/[boardId]`
- `GET /api/job-boards`

**Rationale**: Moderate usage expected for configuration management.

### Data Access Endpoints (Read Operations)

**Limits**: 500 requests per minute per user

**Endpoints**:

- `GET /api/jobs`
- `GET /api/search`
- `GET /api/job-stats`

**Rationale**: Higher limits for read operations that don't modify data.

### Monitoring & Debug Endpoints (Internal Use)

**Limits**: 1,000 requests per minute per IP

**Endpoints**:

- `GET /api/monitoring`
- `GET /api/monitoring/raw`
- `GET /api/debug-*`
- `GET /api/verify-logs`

**Rationale**: High limits for internal monitoring and debugging needs.

## Rate Limiting Headers

All API responses include rate limiting headers:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
X-RateLimit-Window: 60
```

### Header Descriptions

- **X-RateLimit-Limit**: Maximum requests allowed in the current window
- **X-RateLimit-Remaining**: Requests remaining in the current window
- **X-RateLimit-Reset**: Unix timestamp when the current window resets
- **X-RateLimit-Window**: Window duration in seconds

## Implementation Details

### Storage Backend

Rate limiting uses in-memory storage with the following characteristics:

- **Performance**: Sub-millisecond lookup times
- **Memory Efficiency**: Automatic cleanup of expired entries
- **Scalability**: Suitable for single-instance deployments
- **Persistence**: Non-persistent (resets on restart)

### Algorithm

**Sliding Window Counter**: Provides smooth rate limiting without burst allowances.

```typescript
interface RateLimitEntry {
  count: number;
  resetTime: number;
  window: number;
}
```

### Key Generation

Rate limit keys are generated based on endpoint and identifier:

```
{method}:{path}:{identifier}
```

Examples:

- `GET:/api/health:***********` (IP-based)
- `POST:/api/extract:user_123` (User-based)
- `POST:/api/webhook:qstash_source` (Source-based)

## Error Responses

### 429 Too Many Requests

When rate limit is exceeded:

```json
{
  "error": "Rate Limit Exceeded",
  "message": "Too many requests. Limit: 100 per 60s",
  "code": "RATE_LIMIT_EXCEEDED",
  "details": {
    "limit": 100,
    "window": 60,
    "resetTime": **********
  },
  "timestamp": "2024-01-01T12:00:00.000Z"
}
```

### Retry-After Header

Rate limited responses include `Retry-After` header:

```http
HTTP/1.1 429 Too Many Requests
Retry-After: 45
```

## Configuration

### Environment Variables

```bash
# Rate limiting configuration
RATE_LIMIT_ENABLED=true
RATE_LIMIT_REDIS_URL=redis://localhost:6379  # Optional: Redis backend
RATE_LIMIT_DEFAULT_WINDOW=60                 # Default window in seconds
RATE_LIMIT_DEFAULT_LIMIT=100                 # Default request limit
```

### Per-Endpoint Configuration

Rate limits are configured in the middleware:

```typescript
export const rateLimitConfig = {
  "/api/health": { requests: 100, window: 60 },
  "/api/extract": { requests: 50, window: 60 },
  "/api/webhook-callbacks": { requests: 1000, window: 60 },
  // ... more endpoints
};
```

## Monitoring & Alerting

### Metrics Tracked

1. **Rate Limit Hits**: Number of requests that hit rate limits
2. **Rate Limit Violations**: Number of requests that exceeded limits
3. **Top Rate Limited IPs**: Most frequently rate limited IP addresses
4. **Rate Limit Efficiency**: Percentage of requests that are rate limited

### Alerts

Alerts are triggered when:

- **High Rate Limit Violations**: >10% of requests are rate limited
- **Suspicious Activity**: Single IP hits rate limits repeatedly
- **Endpoint Abuse**: Specific endpoint shows unusual traffic patterns

### Dashboard Metrics

Available in `/api/monitoring`:

```json
{
  "rateLimiting": {
    "totalRequests": 10000,
    "rateLimitedRequests": 150,
    "rateLimitHitRate": 0.015,
    "topLimitedEndpoints": [
      { "endpoint": "/api/extract", "violations": 45 },
      { "endpoint": "/api/health", "violations": 30 }
    ],
    "topLimitedIPs": [
      { "ip": "*************", "violations": 25 },
      { "ip": "*********", "violations": 20 }
    ]
  }
}
```

## Best Practices

### For API Consumers

1. **Respect Rate Limits**: Monitor response headers and adjust request frequency
2. **Implement Exponential Backoff**: When rate limited, wait before retrying
3. **Cache Responses**: Reduce API calls by caching appropriate responses
4. **Batch Operations**: Use batch endpoints when available

### For API Developers

1. **Set Appropriate Limits**: Balance protection with usability
2. **Document Limits Clearly**: Include limits in OpenAPI specification
3. **Monitor Usage Patterns**: Adjust limits based on actual usage
4. **Provide Clear Error Messages**: Help users understand and resolve rate limiting

## OpenAPI Specification

Rate limiting is documented in the OpenAPI spec using extensions:

```yaml
paths:
  /api/extract:
    post:
      x-rate-limit:
        requests: 50
        window: 60
        scope: user
      responses:
        "429":
          description: Rate limit exceeded
          headers:
            X-RateLimit-Limit:
              schema:
                type: integer
            X-RateLimit-Remaining:
              schema:
                type: integer
            X-RateLimit-Reset:
              schema:
                type: integer
            Retry-After:
              schema:
                type: integer
```

## Testing Rate Limits

### Manual Testing

```bash
# Test rate limiting with curl
for i in {1..105}; do
  curl -w "%{http_code}\n" -o /dev/null -s http://localhost:3000/api/health
done
```

### Automated Testing

```typescript
describe("Rate Limiting", () => {
  it("should rate limit after exceeding threshold", async () => {
    const requests = Array(101)
      .fill(null)
      .map(() => fetch("/api/health"));

    const responses = await Promise.all(requests);
    const rateLimited = responses.filter((r) => r.status === 429);

    expect(rateLimited.length).toBeGreaterThan(0);
  });
});
```

## Future Enhancements

### Planned Improvements

1. **Redis Backend**: For distributed rate limiting across multiple instances
2. **Dynamic Rate Limits**: Adjust limits based on system load
3. **User-Specific Limits**: Different limits for different user tiers
4. **Geographic Rate Limiting**: Different limits by region
5. **Burst Allowances**: Allow short bursts above normal limits

### Advanced Features

1. **Rate Limit Quotas**: Daily/monthly quotas in addition to per-minute limits
2. **Adaptive Rate Limiting**: AI-powered dynamic limit adjustment
3. **Rate Limit Bypass**: Whitelist for trusted sources
4. **Custom Rate Limit Policies**: Endpoint-specific custom logic
