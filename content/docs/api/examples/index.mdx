---
title: API Examples
description: Real-world examples of Bordfeed API usage with interactive demonstrations
---

import { InteractiveExamples } from "./interactive-examples";

This page showcases real-world examples of the Bordfeed API in action, including actual job data, API responses, and webhook payloads used in production.

## Interactive Examples

<InteractiveExamples />

## Quick Reference

### Authentication

All webhook endpoints require QStash signature verification:

```typescript
import { verifySignature } from "@upstash/qstash/nextjs";

export const POST = verifySignature(async (req: Request) => {
  // Your webhook handler
});
```

### Error Handling

Standard error response format:

```json
{
  "success": false,
  "error": "Validation failed",
  "details": {
    "field": "title",
    "message": "Title is required"
  },
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Rate Limiting

Check rate limit headers in responses:

```http
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: **********
```

## Testing

Use the [Mock Server](../../technical/mock-server) for development and testing:

```bash
# Test with mock server
curl http://localhost:3000/api/mock/api/health

# Add delay for testing loading states
curl http://localhost:3000/api/mock/api/jobs?delay=1000

# Force error response
curl http://localhost:3000/api/mock/api/health?error=true&status=503
```

---

_All examples use real production data with sensitive information sanitized for security._
