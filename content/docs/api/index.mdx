---
title: API Reference
description: Complete API documentation and interactive playground for Bordfeed
icon: Code
---

import { Card<PERSON>, Card } from "fumadocs-ui/components/card";
import { Download } from "lucide-react";

# API Reference

Welcome to the Bordfeed API documentation. Our RESTful API provides programmatic access to all platform functionality.

## Quick Start

<Cards>
  <Card
    title="Interactive Playground"
    description="Test API endpoints directly in your browser"
    href="/api/reference"
  />
  <Card
    title="Authentication"
    description="Learn about API authentication and security"
    href="/docs/api/authentication"
  />
  <Card
    title="Rate Limiting"
    description="Understand rate limits and best practices"
    href="/docs/api/rate-limiting"
  />
  <Card
    title="Examples"
    description="Code examples in multiple languages"
    href="/docs/api/examples"
  />
</Cards>

## Base URL

```
Production: https://bordfeed.com/api
Development: http://localhost:3000/api
```

## Authentication

Most endpoints require QStash signature verification. Include the `Upstash-Signature` header with your requests.

```bash
curl -X POST https://bordfeed.com/api/pipeline-ingest \
  -H "Upstash-Signature: YOUR_SIGNATURE" \
  -H "Content-Type: application/json" \
  -d '{"url": "https://example.com/job"}'
```

## Response Format

All responses are returned in JSON format with appropriate HTTP status codes:

- `200 OK` - Request successful
- `201 Created` - Resource created
- `400 Bad Request` - Invalid request parameters
- `401 Unauthorized` - Authentication required
- `404 Not Found` - Resource not found
- `429 Too Many Requests` - Rate limit exceeded
- `500 Internal Server Error` - Server error

## OpenAPI Specification

Download our OpenAPI specification for use with tools like Postman or Insomnia:

<a
  href="/api/openapi.json"
  download
  className="inline-flex items-center gap-2 px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90"
>
  <Download className="h-4 w-4" />
  Download OpenAPI Spec
</a>

## Need Help?

- Check our [API Examples](/docs/api/examples) for code samples
- Review [Rate Limiting](/docs/api/rate-limiting) guidelines
- Contact support for API-related questions
