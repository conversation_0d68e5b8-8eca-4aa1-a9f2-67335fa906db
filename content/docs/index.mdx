---
title: Bordfeed Documentation
description: AI-powered job board automation platform
---

import { <PERSON><PERSON>, Card } from "fumadocs-ui/components/card";

# Welcome to Bordfeed

Bordfeed is an AI-powered job board automation platform that streamlines job posting management across multiple platforms.

## Quick Links

<Cards>
  <Card
    title="Getting Started"
    description="Set up Bordfeed and start automating your job board"
    href="/docs/getting-started"
  />
  <Card
    title="API Reference"
    description="Interactive API documentation and playground"
    href="/docs/api"
  />
  <Card
    title="Data Sources"
    description="Configure and manage job data sources"
    href="/docs/sources"
  />
  <Card
    title="Development"
    description="Local setup and development guide"
    href="/docs/development"
  />
</Cards>

## Core Features

### 🤖 AI-Powered Extraction

Automatically extract and standardize job data from any source using GPT-4o-mini.

### 📊 Multi-Source Integration

Connect to Workable, WeWorkRemotely, JobDataAPI, and custom sources.

### 🔄 Real-Time Synchronization

Keep your job boards synchronized with automatic updates and deduplication.

### 📈 Monitoring & Analytics

Track job processing, monitor health, and analyze performance metrics.

## Architecture Overview

```mermaid
graph LR
    A[Data Sources] --> B[AI Extraction]
    B --> C[Standardization]
    C --> D[Deduplication]
    D --> E[Publishing]
    E --> F[Job Boards]
```

## Technology Stack

- **Next.js 15** - React framework with App Router
- **Supabase** - PostgreSQL database
- **OpenAI** - AI-powered extraction
- **QStash** - Job scheduling
- **Scalar** - API documentation
- **shadcn/ui** - Component library

## Support

- [GitHub Issues](https://github.com/your-repo/issues)
- [API Status](/api/health)
- [Changelog](/docs/reference/changelog)
