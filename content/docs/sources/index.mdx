---
title: Data Sources Overview
description: Comprehensive guide to job data sources in Bordfeed
---

Bordfeed integrates with multiple job data sources to provide comprehensive job coverage across different platforms and markets. All sources use **Apify actors** for reliable, scalable data extraction with built-in retry logic and monitoring.

## Available Sources

### JobDataAPI

Professional job aggregation API with structured data and advanced filtering.

- **Volume**: ~100 jobs per run
- **Speed**: ~6 seconds
- **Specialty**: Structured data, global coverage
- **[Learn more →](./jobdata-api)**

### Workable XML Feed

Enterprise-scale job feed processing a massive 458MB XML file from companies using Workable platform.

- **Volume**: 500-1000+ jobs per run (highest volume)
- **Speed**: 5-10 minutes (large file processing)
- **Specialty**: Enterprise jobs, diverse categories
- **[Learn more →](./workable)**

### WeWorkRemotely RSS

Direct RSS feed integration specializing in remote-only positions.

- **Volume**: 80-100 jobs per run
- **Speed**: ~5 seconds (fastest)
- **Specialty**: 100% remote jobs, high quality
- **[Learn more →](./weworkremotely-rss)**

## Architecture Overview

All sources follow a consistent architecture pattern:

```
Dashboard Trigger → Apify Actor → Webhook/Direct → Database → AI Processing
```

### Data Flow

1. **Manual Trigger**: Initiated from Sources dashboard
2. **Apify Processing**: Reliable data extraction with monitoring
3. **Webhook Integration**: Real-time data processing
4. **Database Storage**: Immediate availability with deduplication
5. **AI Enhancement**: Structured field extraction via GPT-4o-mini

## Performance Comparison

| Source             | Volume     | Speed      | Reliability | Specialty             |
| ------------------ | ---------- | ---------- | ----------- | --------------------- |
| **Workable**       | ⭐⭐⭐⭐⭐ | ⭐⭐       | ⭐⭐⭐⭐    | Enterprise, Diversity |
| **JobDataAPI**     | ⭐⭐⭐⭐   | ⭐⭐⭐⭐   | ⭐⭐⭐⭐    | Structured, Global    |
| **WeWorkRemotely** | ⭐⭐⭐     | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐  | Remote, Quality       |

## Configuration

All sources require these environment variables (stored in Vercel):

```bash
# Apify Platform
APIFY_TOKEN=apify_api_...

# Database
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-key

# Source-specific Actor IDs
JOBDATA_ACTOR_ID=your-jobdata-actor-id
WORKABLE_ACTOR_ID=your-workable-actor-id
WWR_ACTOR_ID=your-wwr-actor-id
```

## Monitoring & Health

### Real-time Dashboard

The Sources dashboard provides:

- **Live Statistics**: Job counts, success rates, response times
- **Health Status**: Active, inactive, error states
- **Run History**: Recent execution logs and performance

### Health Checks

Each source includes automated health monitoring:

- **Availability**: Source endpoint accessibility
- **Performance**: Response time tracking
- **Data Quality**: Job volume and field completeness
- **Error Tracking**: Failure detection and alerting

## Best Practices

### Scheduling

- **Workable**: 1-2 times daily (resource intensive)
- **JobDataAPI**: 2-3 times daily (rate limited)
- **WeWorkRemotely**: 2-3 times daily (optimal freshness)

### Resource Management

- Monitor Apify usage and costs
- Balance frequency with resource consumption
- Use off-peak hours for large sources (Workable)

### Data Quality

- Regular deduplication monitoring
- Field completeness validation
- Source-specific filtering optimization

## Adding New Sources

To add a new job data source:

1. **Create Apify Actor**: Implement data extraction logic
2. **Add Configuration**: Environment variables and constants
3. **Implement Webhook**: Process extracted data
4. **Add Dashboard Integration**: Trigger and monitoring
5. **Update Documentation**: Source-specific guide

For detailed implementation guidance, see the [Development Guide](/docs/development).

## Support

For source-related issues:

1. Check source-specific documentation
2. Monitor Apify actor logs
3. Verify webhook processing
4. Review database insertion logs
