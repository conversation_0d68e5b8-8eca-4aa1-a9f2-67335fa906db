---
title: JobDataAPI Source
description: Professional job aggregation API with structured data and advanced filtering
---

JobDataAPI is a high-volume job aggregation service that provides access to thousands of job postings from multiple sources. Our integration uses Apify actors to fetch jobs and process them through a webhook-based pipeline.

## Quick Overview

- **Volume**: ~100 jobs per run
- **Speed**: 3-7 seconds
- **Success Rate**: 99%+
- **Cost**: ~$0.001 per run
- **Specialty**: Structured data, global coverage

## Architecture

```
Dashboard Trigger → Apify Actor → Webhook → Database → Dashboard
     ↓                ↓            ↓         ↓          ↓
   Manual Run    Fetch 100 Jobs   Process   Store    Display
```

## Configuration

### Environment Variables

Environment variables are stored in **Vercel dashboard**. For local development:

```bash
# Pull secrets from Vercel to .env.local
bun run env:pull
```

Required variables in Vercel:

```bash
APIFY_TOKEN=apify_api_...        # Apify API token
JOBDATA_ACTOR_ID=GxymxREtbsUduUFEO  # Apify actor ID
SUPABASE_URL=...                 # Database connection
SUPABASE_SERVICE_ROLE_KEY=...    # Database access
```

### API Endpoints

- **Trigger**: `POST /api/sources/jobdata-api/run`
- **Webhook**: `POST /api/jobdata-webhook`
- **Health**: `GET /api/sources/jobdata-api/health`

## Data Flow

### 1. Manual Trigger

```typescript
// From /dashboard/sources
POST / api / sources / jobdata - api / run;
```

### 2. Apify Actor Processing

- **Actor**: `craftled/jobdataapi-actor`
- **Runtime**: ~6 seconds
- **Output**: 100 jobs per run
- **Format**: Structured JSON with job details

### 3. Webhook Processing

- **Endpoint**: `/api/jobdata-webhook`
- **Data Source**: Uses actor run ID to fetch dataset
- **Validation**: Field mapping and data validation
- **Storage**: Bulk insert to Supabase

### 4. Data Mapping

```typescript
{
  title: job.title || originalJob.title,
  company: originalJob.company?.name,
  description: "", // Placeholder - extracted via AI later
  apply_url: applicationUrl,
  source_type: "jobdata_api",
  source_name: "JobDataAPI",
  processing_status: "pending",
  raw_sourced_job_data: job // Complete original data
}
```

## Sample Data

### Input (from JobDataAPI)

```json
{
  "title": "Senior Software Engineer",
  "company": { "name": "TechCorp" },
  "application_url": "https://jobs.techcorp.com/apply/123",
  "description": "<p>We are looking for...</p>",
  "location": "San Francisco, CA",
  "salary": "$120,000 - $180,000"
}
```

### Output (to Database)

```json
{
  "id": "uuid-generated",
  "title": "Senior Software Engineer",
  "company": "TechCorp",
  "description": "",
  "apply_url": "https://jobs.techcorp.com/apply/123",
  "source_type": "jobdata_api",
  "source_name": "JobDataAPI",
  "processing_status": "pending",
  "created_at": "2025-07-22T00:22:14Z",
  "raw_sourced_job_data": {...}
}
```

## Troubleshooting

### Common Issues

#### "Failed to fetch dataset: 404"

**Cause**: Using dataset ID instead of actor run ID

**Solution**: Ensure webhook uses actor run ID approach:

```typescript
// ✅ CORRECT
const datasetUrl = `https://api.apify.com/v2/actor-runs/${actorRunId}/dataset/items`;

// ❌ WRONG
const datasetUrl = `https://api.apify.com/v2/datasets/${datasetId}/items`;
```

#### "null value in column 'description'"

**Cause**: Missing required fields in job validation

**Solution**: Ensure all required fields are mapped:

```typescript
return {
  title: job.title || "Untitled Job",
  description: "", // Required placeholder
  apply_url: applicationUrl, // Required for validation
  // ... other fields
};
```

#### "No jobs in dashboard after successful run"

**Cause**: Webhook not being called or failing silently

**Diagnosis**:

- Check Apify console "Triggered integrations" tab
- Verify webhook URL in actor configuration
- Check server logs for webhook errors

### Debugging Steps

1. **Check Actor Status**

```bash
curl -H "Authorization: Bearer $APIFY_TOKEN" \
  "https://api.apify.com/v2/acts/GxymxREtbsUduUFEO/runs?limit=1"
```

2. **Test Webhook Locally**

```bash
curl -X POST "http://localhost:3001/api/jobdata-webhook" \
  -H "Content-Type: application/json" \
  -d '{"eventType":"ACTOR.RUN.SUCCEEDED","eventData":{"actorRunId":"RECENT_RUN_ID"}}'
```

3. **Check Database**

```sql
SELECT COUNT(*) FROM jobs WHERE source_name = 'JobDataAPI';
SELECT * FROM jobs WHERE source_name = 'JobDataAPI' ORDER BY created_at DESC LIMIT 5;
```

## API Reference

### Trigger Run

```http
POST /api/sources/jobdata-api/run
Content-Type: application/json

Response:
{
  "success": true,
  "message": "JobDataAPI actor started successfully",
  "runId": "abc123..."
}
```

### Webhook Handler

```http
POST /api/jobdata-webhook
Content-Type: application/json

Body:
{
  "eventType": "ACTOR.RUN.SUCCEEDED",
  "eventData": {"actorRunId": "abc123..."},
  "resource": {"id": "abc123...", "status": "SUCCEEDED"}
}

Response:
{
  "success": true,
  "runId": "abc123...",
  "jobsReceived": 100,
  "jobsSaved": 100,
  "jobsSkipped": 0,
  "processingTime": "4487ms"
}
```

## Monitoring

### Health Checks

- **Actor Status**: Check last successful run timestamp
- **Webhook Status**: Monitor response times and error rates
- **Data Quality**: Validate job count and field completeness

### Alerts

- Run failures (> 5 minutes without completion)
- Webhook errors (4xx/5xx responses)
- Low job volume (< 90 jobs per run)
- Database insertion failures

## Related Files

- **Trigger**: `/app/api/sources/jobdata-api/run/route.ts`
- **Webhook**: `/app/api/jobdata-webhook/route.ts`
- **Validation**: `/lib/job-validation.ts`
- **Constants**: `/lib/constants.ts`
- **Types**: `/lib/types.ts`

## Support

For issues with JobDataAPI integration:

1. Check this documentation first
2. Review server logs and Apify console
3. Test webhook endpoint manually
4. Verify environment variable configuration
