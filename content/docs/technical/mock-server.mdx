---
title: OpenAPI-based Mock Server
description: Realistic API responses for frontend development and testing with dynamic mock data generation
---

## Overview

The mock server provides realistic API responses based on the OpenAPI specification, enabling frontend development and testing without backend dependencies. It generates dynamic mock data using Faker.js and supports all documented endpoints.

## Features

- **OpenAPI-driven**: Responses based on OpenAPI specification
- **Realistic Data**: Generated using Faker.js for authentic-looking data
- **Configurable Delays**: Simulate network latency
- **Error Simulation**: Test error handling with configurable error rates
- **CORS Support**: Full CORS support for frontend development
- **Rate Limiting**: Same rate limiting as production API
- **Development Mode**: Automatically enabled in development environment

## Usage

### Base URL

```
http://localhost:3000/api/mock
```

### Endpoint Format

```
/api/mock/{original-api-path}
```

### Examples

```bash
# Health check
GET /api/mock/api/health

# Job extraction
POST /api/mock/api/extract

# Job listing with delay
GET /api/mock/api/jobs?delay=500

# Force error response
GET /api/mock/api/health?error=true&status=503

# Custom status code
GET /api/mock/api/versions?status=201
```

## Query Parameters

| Parameter | Type    | Description                      | Default | Range      |
| --------- | ------- | -------------------------------- | ------- | ---------- |
| `delay`   | number  | Artificial delay in milliseconds | 100     | 0-5000     |
| `status`  | number  | Override response status code    | 200     | 200-599    |
| `error`   | boolean | Force error response             | false   | true/false |

## Supported Endpoints

### Health & Monitoring

- `GET /api/mock/api/health` - System health status
- `GET /api/mock/api/versions` - API version information
- `GET /api/mock/api/monitoring` - System monitoring data
- `GET /api/mock/api/monitoring/health` - Health monitoring

### Data Processing

- `POST /api/mock/api/extract` - Job extraction
- `POST /api/mock/api/pipeline-ingest` - Pipeline ingestion
- `GET /api/mock/api/pipeline-ingest` - Pipeline health

### Data Access

- `GET /api/mock/api/jobs` - Job listings
- `GET /api/mock/api/search` - Job search
- `GET /api/mock/api/job-stats` - Job statistics

### Configuration

- `GET /api/mock/api/airtable-config` - Airtable configuration
- `POST /api/mock/api/airtable-config` - Update Airtable config
- `GET /api/mock/api/job-boards` - Job boards listing

### Generic Endpoints

Any unmapped endpoint will return a generic mock response with helpful information.

## Response Headers

All mock responses include these headers:

```http
X-Mock-Server: true
X-Mock-Path: /api/health
X-Mock-Method: GET
X-Mock-Type: specific
X-Mock-Delay: 100
Cache-Control: no-cache, no-store, must-revalidate
```

## Configuration

### Environment Variables

```bash
# Enable/disable mock server
ENABLE_MOCK_SERVER=true

# Mock server configuration (optional)
MOCK_DEFAULT_DELAY=100
MOCK_ERROR_RATE=0.05
```

### Development Mode

Mock server is automatically enabled when `NODE_ENV=development`.

## Mock Data Examples

### Job Extraction Response

```json
{
  "success": true,
  "data": {
    "title": "Senior Software Engineer",
    "company": "TechCorp",
    "type": "full-time",
    "description": "Lorem ipsum dolor sit amet...",
    "apply_url": "https://example.com/apply",
    "salary_min": 120000,
    "salary_max": 180000,
    "salary_currency": "USD",
    "workplace_type": "remote",
    "skills": "JavaScript, TypeScript, React, Node.js"
  },
  "processingTime": 1250,
  "confidence": 0.95
}
```

### Health Check Response

```json
{
  "status": "healthy",
  "services": [
    {
      "service": "database",
      "status": "healthy",
      "features": ["PostgreSQL", "Supabase"],
      "environment": {
        "connected": true,
        "latency": 45
      },
      "timestamp": "2024-01-01T12:00:00.000Z"
    }
  ],
  "timestamp": "2024-01-01T12:00:00.000Z",
  "uptime": 86400
}
```

## Frontend Integration

### JavaScript/TypeScript

```typescript
// Configure base URL for development
const API_BASE_URL =
  process.env.NODE_ENV === "development"
    ? "http://localhost:3000/api/mock/api"
    : "http://localhost:3000/api";

// Make API calls normally
const response = await fetch(`${API_BASE_URL}/health`);
const data = await response.json();
```

### React Hook

```typescript
import { useState, useEffect } from 'react';

function useApi<T>(endpoint: string) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const baseUrl = process.env.NODE_ENV === 'development'
      ? '/api/mock/api'
      : '/api';

    fetch(`${baseUrl}${endpoint}`)
      .then(res => res.json())
      .then(setData)
      .catch(err => setError(err.message))
      .finally(() => setLoading(false));
  }, [endpoint]);

  return { data, loading, error };
}

// Usage
function HealthStatus() {
  const { data, loading, error } = useApi<HealthResponse>('/health');

  if (loading) return <div>Loading...</div>;
  if (error) return <div>Error: {error}</div>;

  return <div>Status: {data?.status}</div>;
}
```

## Testing with Mock Server

### Jest Tests

```typescript
// Setup mock server for tests
beforeAll(() => {
  process.env.ENABLE_MOCK_SERVER = "true";
});

test("should fetch job data", async () => {
  const response = await fetch("/api/mock/api/jobs");
  const data = await response.json();

  expect(response.status).toBe(200);
  expect(data.jobs).toBeInstanceOf(Array);
  expect(data.jobs.length).toBeGreaterThan(0);
});

test("should handle errors gracefully", async () => {
  const response = await fetch("/api/mock/api/jobs?error=true");

  expect(response.status).toBeGreaterThanOrEqual(400);
});
```

### Cypress E2E Tests

```typescript
describe("API Integration", () => {
  beforeEach(() => {
    // Intercept API calls and use mock server
    cy.intercept("GET", "/api/**", (req) => {
      req.url = req.url.replace("/api/", "/api/mock/api/");
    });
  });

  it("should display job listings", () => {
    cy.visit("/jobs");
    cy.get('[data-testid="job-list"]').should("be.visible");
    cy.get('[data-testid="job-item"]').should("have.length.greaterThan", 0);
  });
});
```

## Adding Custom Mock Endpoints

### 1. Add to Mock Server

```typescript
// lib/mock-server.ts
export const MOCK_ENDPOINTS = {
  // ... existing endpoints
  "GET:/api/custom-endpoint": () => ({
    customData: faker.lorem.paragraph(),
    timestamp: new Date().toISOString(),
  }),
};
```

### 2. Add Schema-based Generation

```typescript
// For complex responses, use schema-based generation
import { CustomResponseSchema } from "./api-schemas";

export function generateMockCustomResponse(): CustomResponse {
  return {
    id: faker.string.uuid(),
    name: faker.company.name(),
    data: faker.lorem.paragraphs(2),
    // ... more fields
  };
}
```

## Troubleshooting

### Common Issues

1. **Mock server not responding**
   - Check `ENABLE_MOCK_SERVER` environment variable
   - Verify development mode is enabled
   - Check console for error messages

2. **CORS errors**
   - Mock server includes CORS headers by default
   - Check if origin is in allowed list
   - Use OPTIONS request to verify CORS setup

3. **Unrealistic data**
   - Customize mock generators in `lib/mock-server.ts`
   - Use schema-based generation for complex types
   - Add seed data for consistent testing

### Debug Information

Get mock server info:

```bash
curl http://localhost:3000/api/mock/info
```

Check available endpoints:

```bash
curl -X OPTIONS http://localhost:3000/api/mock/
```

## Best Practices

1. **Use in Development Only**: Mock server should not be enabled in production
2. **Realistic Data**: Ensure mock data closely matches production data structure
3. **Error Testing**: Use error simulation to test error handling
4. **Performance Testing**: Use delay parameter to test loading states
5. **Schema Validation**: Keep mock responses in sync with OpenAPI schemas

---

_The mock server enables rapid frontend development and comprehensive testing without backend dependencies._
