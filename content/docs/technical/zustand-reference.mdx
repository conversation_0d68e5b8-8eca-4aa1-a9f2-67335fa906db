---
title: Zustand Quick Reference
description: Complete implementation guide and API reference for Zustand state management in Bordfeed
---

Complete implementation guide and API reference for Zustand state management in Bordfeed.

## Overview

Bordfeed uses Zustand for centralized state management across the dashboard application. This guide covers store architecture, usage patterns, and best practices.

## Store Architecture

### Core Stores

| Store            | Purpose                  | Key Features                  |
| ---------------- | ------------------------ | ----------------------------- |
| **HealthStore**  | System health monitoring | Auto-refresh, error handling  |
| **SourcesStore** | Data source management   | Real-time updates, caching    |
| **BoardsStore**  | Job board configuration  | CRUD operations, validation   |
| **JobsStore**    | Job data management      | Filtering, pagination, search |
| **UIStore**      | UI state management      | Modals, notifications, themes |

### Store Structure

```typescript
interface BaseStore {
  // Data
  data: T | null;

  // Loading states
  isLoading: boolean;
  error: string | null;
  lastUpdated: Date | null;

  // Actions
  fetch: () => Promise<void>;
  reset: () => void;
  setError: (error: string) => void;
}
```

## Usage Patterns

### Basic Usage

```typescript
import { useHealthStore } from '@/lib/stores';

function HealthComponent() {
  const { systemHealth, isLoading, fetchHealth } = useHealthStore();

  useEffect(() => {
    fetchHealth();
  }, [fetchHealth]);

  if (isLoading) return <div>Loading...</div>;

  return (
    <div>
      <h2>System Status: {systemHealth?.status}</h2>
    </div>
  );
}
```

### Selective Subscriptions

```typescript
// Only re-render when specific data changes
function JobCounter() {
  const jobCount = useHealthStore(state => state.jobStats?.total);

  return <span>Total Jobs: {jobCount}</span>;
}
```

### Actions and Mutations

```typescript
function SourceControls({ sourceId }: { sourceId: string }) {
  const { runSource, sources } = useSourcesStore();
  const source = sources?.find(s => s.id === sourceId);

  const handleRun = async () => {
    try {
      await runSource(sourceId);
    } catch (error) {
      console.error('Failed to run source:', error);
    }
  };

  return (
    <button onClick={handleRun} disabled={source?.status === 'running'}>
      {source?.status === 'running' ? 'Running...' : 'Run Source'}
    </button>
  );
}
```

## Store Implementations

### Health Store

```typescript
interface HealthStore {
  systemHealth: SystemHealth | null;
  sourcesHealth: SourceHealth[] | null;
  jobStats: JobStats | null;
  dedupStats: DedupStats | null;
  isLoading: boolean;
  error: string | null;

  fetchAllHealth: () => Promise<void>;
  startAutoRefresh: () => void;
  stopAutoRefresh: () => void;
}

export const useHealthStore = create<HealthStore>((set, get) => ({
  // Initial state
  systemHealth: null,
  sourcesHealth: null,
  jobStats: null,
  dedupStats: null,
  isLoading: false,
  error: null,

  // Actions
  fetchAllHealth: async () => {
    set({ isLoading: true, error: null });

    try {
      const [system, sources, jobs, dedup] = await Promise.all([
        fetchSystemHealth(),
        fetchSourcesHealth(),
        fetchJobStats(),
        fetchDedupStats(),
      ]);

      set({
        systemHealth: system,
        sourcesHealth: sources,
        jobStats: jobs,
        dedupStats: dedup,
        isLoading: false,
        lastUpdated: new Date(),
      });
    } catch (error) {
      set({ error: error.message, isLoading: false });
    }
  },
}));
```

## Performance Optimizations

### 1. Selective Subscriptions

```typescript
// ❌ Bad - subscribes to entire store
const store = useHealthStore();

// ✅ Good - subscribes to specific data
const isLoading = useHealthStore((state) => state.isLoading);
const jobCount = useHealthStore((state) => state.jobStats?.total);
```

### 2. Memoized Selectors

```typescript
// Use shallow comparison for objects
import { shallow } from "zustand/shallow";

const { systemHealth, jobStats } = useHealthStore(
  (state) => ({ systemHealth: state.systemHealth, jobStats: state.jobStats }),
  shallow
);
```

### 3. Batched Updates

```typescript
// Batch multiple state updates
set((state) => ({
  ...state,
  isLoading: false,
  data: newData,
  lastUpdated: new Date(),
}));
```

## Best Practices

### 1. **Single Responsibility**

- Each store manages one domain
- Keep stores focused and cohesive
- Avoid cross-domain dependencies

### 2. **Immutable Updates**

```typescript
// ✅ Good - immutable update
set((state) => ({
  ...state,
  items: [...state.items, newItem],
}));

// ❌ Bad - mutating state
set((state) => {
  state.items.push(newItem);
  return state;
});
```

### 3. **Error Boundaries**

```typescript
// Always handle errors gracefully
try {
  await apiCall();
} catch (error) {
  set({ error: error.message, isLoading: false });
}
```

### 4. **Type Safety**

```typescript
// Use TypeScript interfaces
interface StoreState {
  data: DataType | null;
  isLoading: boolean;
  error: string | null;
}

// Type the store
export const useStore = create<StoreState>((set) => ({
  // Implementation
}));
```

## Related Documentation

- **[Performance Analysis](./zustand-performance)** - Detailed performance metrics and improvements
- **[Development Guide](../development)** - General development practices
