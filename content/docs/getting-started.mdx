---
title: Getting Started
description: Learn how to set up and configure Bordfeed for your job board automation needs
---

This guide will help you get started with <PERSON><PERSON><PERSON><PERSON> and set up your first automated job board.

## Prerequisites

Before you begin, ensure you have:

- Node.js 18+ installed
- A Supabase account and project
- OpenAI API key
- Airtable account (for publishing)

## Installation

1. **Clone the repository:**

   ```bash
   git clone <your-repo-url>
   cd bordfeed
   ```

2. **Install dependencies:**

   ```bash
   bun install
   ```

3. **Set up environment variables:**

   ```bash
   bun run env:pull # Pull all secrets from Vercel to .env.local
   ```

4. **Start the development server:**
   ```bash
   bun run dev  # Starts on http://localhost:3000
   ```

## First Steps

### 1. Configure Job Sources

Navigate to the Sources section in the dashboard to configure your job data sources:

- **Apify Scrapers** - Configure web scraping for job sites
- **RSS Feeds** - Add RSS feeds from job boards
- **API Integrations** - Connect to job APIs

### 2. Set Up Filters

Configure intelligent filters to ensure only relevant jobs are processed:

- **Job Types** - Full-time, Part-time, Contract, etc.
- **Workplace Types** - Remote, Hybrid, On-site
- **Career Levels** - Junior, Mid, Senior, Lead, Principal
- **Keywords** - Include/exclude specific terms
- **Salary Ranges** - Set minimum salary requirements

### 3. Configure Publishing

Set up your Airtable bases for job publishing:

- Connect your Airtable account
- Configure base schemas
- Set publishing schedules
- Test the integration

## Next Steps

- [Configuration Guide](./configuration) - Detailed configuration options
- [API Reference](./api) - Explore available endpoints
- [Monitoring](./monitoring) - Set up job monitoring and alerts
