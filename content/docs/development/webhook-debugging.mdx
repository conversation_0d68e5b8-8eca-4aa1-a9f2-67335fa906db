---
title: Webhook Debugging Guide
description: Complete guide to debugging and monitoring webhook notifications in Bordfeed
---

Complete logging solution for troubleshooting and monitoring your webhook notification pipeline in Bordfeed.

## Overview

Bordfeed uses a sophisticated webhook notification system with QStash for reliable job processing. This guide covers debugging techniques, log analysis, and troubleshooting common issues.

## Architecture

```
Apify Actor → QStash → /api/pipeline-ingest → QStash Callback → /api/webhook-callbacks
     ↓           ↓            ↓                    ↓                ↓
  Job Data   Schedule      Process Jobs        Success/Fail     Log Results
```

## Debug Coverage

### 1. QStash Callback Handler (`/api/webhook-callbacks`)

#### Incoming Request Logging

```bash
🔔 [cb_1234567890_xyz123] QStash callback received
  ├── timestamp: "2025-01-19T10:30:45.123Z"
  ├── headers: { "x-signature": "...", "content-type": "application/json" }
  ├── url: "https://bordfeed.com/api/webhook-callbacks"
  └── method: "POST"
```

#### Success Callback Processing

```bash
✅ [cb_1234567890_xyz123] Processing SUCCESS callback
  ├── status: 200
  ├── messageId: "msg_abc123def456"
  ├── retryCount: 0
  ├── processingTime: 2456ms
  └── targetUrl: "https://bordfeed.com/api/pipeline-ingest"
```

## Debugging Techniques

### Real-Time Log Monitoring

**Development Environment:**

```bash
bun run dev
# Watch logs in terminal for real-time debugging
```

**Production Environment:**

```bash
# Check Vercel logs
vercel logs --follow

# Or use the Vercel dashboard
https://vercel.com/your-project/functions
```

### Common Debug Scenarios

#### 1. Webhook Not Receiving Callbacks

**Symptoms:**

- No callback logs in `/api/webhook-callbacks`
- Jobs stuck in "processing" state

**Debug Steps:**

1. Check QStash dashboard for failed deliveries
2. Verify webhook URL is accessible
3. Check signature verification

#### 2. Job Processing Failures

**Symptoms:**

- Success callbacks but jobs not appearing in database
- Error logs in pipeline ingestion

**Debug Steps:**

1. Check database connection
2. Verify job data format
3. Review AI processing logs

## Troubleshooting Guide

### Issue: Webhooks Not Working

1. **Check Environment Variables**

   ```bash
   # Verify QStash configuration
   echo $QSTASH_TOKEN
   echo $QSTASH_CURRENT_SIGNING_KEY
   echo $QSTASH_NEXT_SIGNING_KEY
   ```

2. **Test Webhook Endpoint**

   ```bash
   curl -X POST https://your-domain.com/api/webhook-callbacks \
     -H "Content-Type: application/json" \
     -d '{"test": true}'
   ```

3. **Verify Signature Validation**
   - Check QStash signature headers
   - Ensure signing keys are current
   - Review signature verification logic

### Issue: Slow Processing

1. **Database Performance**
   - Check Supabase connection pool
   - Review query performance
   - Monitor database load

2. **AI Processing**
   - Check OpenAI API response times
   - Review batch sizes
   - Monitor rate limits

## Best Practices

### Development

1. **Local Testing**

   ```bash
   # Use ngrok for webhook testing
   ngrok http 3000
   # Update webhook URL in QStash
   ```

2. **Staging Environment**
   - Test with real webhook URLs
   - Use separate QStash project
   - Monitor logs closely

### Production

1. **Monitoring**
   - Set up alerts for critical metrics
   - Regular log review
   - Performance monitoring

2. **Error Handling**
   - Graceful failure handling
   - Retry mechanisms
   - Dead letter queue monitoring

## Related Documentation

- **[Environment Setup](./environment-setup)** - Configure webhook environment variables
- **[API Reference](../api)** - Complete API documentation
- **[Sources Documentation](../sources)** - Data source configurations
