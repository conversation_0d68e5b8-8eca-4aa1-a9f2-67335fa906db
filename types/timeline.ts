export interface TimelineActivity {
  id: number;
  type:
    | 'created'
    | 'fetched'
    | 'processed'
    | 'synced'
    | 'posted'
    | 'monitored'
    | 'updated'
    | 'viewed';
  person: {
    name: string;
    imageUrl?: string;
  };
  date: string;
  dateTime: string;
  comment?: string;
}

export interface DatabaseJob {
  id: string;
  created_at: string;
  updated_at: string;
  raw_data_fetched_at?: string;
  processing_status?: string;
  airtable_synced_at?: string;
  last_posted_at?: string;
  last_checked_at?: string;
  source_name?: string;
}
