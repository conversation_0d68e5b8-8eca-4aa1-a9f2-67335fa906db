#!/usr/bin/env tsx

/**
 * Workflow Test Runner - Day 4 Testing Strategy
 *
 * Runs all workflow-related tests from the rapid build plan
 * Completely detached from production - safe to delete
 */

import { spawn } from 'node:child_process';
import { performance } from 'perf_hooks';

interface TestResult {
  suite: string;
  passed: boolean;
  duration: number;
  error?: string;
}

const results: TestResult[] = [];

function runTestSuite(
  name: string,
  command: string,
  args: string[]
): Promise<TestResult> {
  const start = performance.now();

  return new Promise((resolve) => {
    console.log(`\n🧪 Running ${name}...`);
    console.log(`Command: ${command} ${args.join(' ')}`);
    console.log('─'.repeat(50));

    const child = spawn(command, args, {
      stdio: 'inherit',
      shell: true,
    });

    child.on('close', (code) => {
      const duration = performance.now() - start;
      const passed = code === 0;

      const result: TestResult = {
        suite: name,
        passed,
        duration,
        error: passed ? undefined : `Process exited with code ${code}`,
      };

      results.push(result);

      if (passed) {
        console.log(`✅ ${name} passed in ${(duration / 1000).toFixed(2)}s`);
      } else {
        console.log(`❌ ${name} failed in ${(duration / 1000).toFixed(2)}s`);
      }

      resolve(result);
    });

    child.on('error', (error) => {
      const duration = performance.now() - start;
      const result: TestResult = {
        suite: name,
        passed: false,
        duration,
        error: error.message,
      };

      results.push(result);
      console.log(`❌ ${name} failed: ${error.message}`);
      resolve(result);
    });
  });
}

async function runWorkflowTests() {
  console.log('🚀 Starting Workflow Test Suite (Day 4 Strategy)');
  console.log('='.repeat(60));

  const testSuites = [
    {
      name: 'Unit Tests - Workflow Processing',
      command: 'npx',
      args: [
        'vitest',
        'run',
        'tests/unit/workflow-processing.test.ts',
        '--reporter=verbose',
      ],
    },
    {
      name: 'Integration Tests - Webhook to Workflow',
      command: 'npx',
      args: [
        'vitest',
        'run',
        'tests/integration/webhook-to-workflow.test.ts',
        '--reporter=verbose',
      ],
    },
    {
      name: 'Integration Tests - Workflow Validation',
      command: 'npx',
      args: [
        'vitest',
        'run',
        'tests/integration/workflow-validation.test.ts',
        '--reporter=verbose',
      ],
    },
  ];

  // Run all test suites
  for (const suite of testSuites) {
    await runTestSuite(suite.name, suite.command, suite.args);
  }

  // Print summary
  printSummary();

  // Exit with appropriate code
  const allPassed = results.every((result) => result.passed);
  process.exit(allPassed ? 0 : 1);
}

function printSummary() {
  console.log('\n' + '='.repeat(60));
  console.log('📊 WORKFLOW TEST SUMMARY');
  console.log('='.repeat(60));

  const totalTests = results.length;
  const passedTests = results.filter((r) => r.passed).length;
  const failedTests = totalTests - passedTests;
  const totalDuration = results.reduce((sum, r) => sum + r.duration, 0);

  console.log('\n📈 Overall Results:');
  console.log(`  Total Test Suites: ${totalTests}`);
  console.log(
    `  Passed: ${passedTests} (${((passedTests / totalTests) * 100).toFixed(1)}%)`
  );
  console.log(
    `  Failed: ${failedTests} (${((failedTests / totalTests) * 100).toFixed(1)}%)`
  );
  console.log(`  Total Duration: ${(totalDuration / 1000).toFixed(2)}s`);

  console.log('\n📋 Detailed Results:');
  results.forEach((result) => {
    const status = result.passed ? '✅' : '❌';
    const duration = (result.duration / 1000).toFixed(2);
    console.log(`  ${status} ${result.suite}: ${duration}s`);

    if (result.error) {
      console.log(`    Error: ${result.error}`);
    }
  });

  if (failedTests > 0) {
    console.log(
      `\n⚠️  ${failedTests} test suite(s) failed. Check the output above for details.`
    );
  } else {
    console.log(
      '\n🎉 All workflow tests passed! The Day 4 testing strategy is complete.'
    );
  }

  console.log('\n' + '='.repeat(60));
}

// Load testing function
async function runLoadTests() {
  console.log('\n🔥 Running Load Tests...');

  const loadTestTypes = ['light', 'moderate'];

  for (const testType of loadTestTypes) {
    try {
      console.log(`\n🧪 Running ${testType} load test...`);

      const result = await runTestSuite(`Load Test - ${testType}`, 'npx', [
        'tsx',
        'tests/scripts/load-test-workflows.ts',
        testType,
        'http://localhost:3000',
      ]);

      if (!result.passed) {
        console.log(
          `⚠️  ${testType} load test failed - this may indicate performance issues`
        );
      }
    } catch (error) {
      console.log(`❌ Load test ${testType} failed:`, error.message);
    }
  }
}

// CLI interface
async function main() {
  const args = process.argv.slice(2);
  const includeLoadTests = args.includes('--load');
  const loadTestOnly = args.includes('--load-only');

  try {
    if (loadTestOnly) {
      await runLoadTests();
    } else {
      await runWorkflowTests();

      if (includeLoadTests) {
        await runLoadTests();
      }
    }
  } catch (error) {
    console.error('❌ Test runner failed:', error.message);
    process.exit(1);
  }
}

// Help text
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`
🧪 Workflow Test Runner - Day 4 Testing Strategy

Usage:
  npm run test:workflow              # Run all workflow tests
  npm run test:workflow -- --load   # Run workflow tests + load tests
  npm run test:workflow -- --load-only  # Run only load tests
  
Test Suites:
  • Unit Tests - Workflow Processing Logic
  • Integration Tests - Webhook to Workflow Flow  
  • Integration Tests - Workflow Validation
  • Load Tests - Performance under concurrent load

Examples:
  npx tsx tests/scripts/run-workflow-tests.ts
  npx tsx tests/scripts/run-workflow-tests.ts --load
  npx tsx tests/scripts/run-workflow-tests.ts --load-only
`);
  process.exit(0);
}

// Run if called directly
if (require.main === module) {
  main().catch(console.error);
}
