/**
 * Workflow Validation Tests
 *
 * Tests the validation checklist from the rapid build plan
 * Completely detached from production - safe to delete
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock database client for validation queries
vi.mock('@/lib/supabase', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      select: vi.fn(() => ({
        eq: vi.fn(() => ({
          lt: vi.fn(() =>
            Promise.resolve({
              data: mockValidationData.stuckJobs,
              error: null,
            })
          ),
        })),
        gte: vi.fn(() => ({
          lte: vi.fn(() =>
            Promise.resolve({
              data: mockValidationData.duplicateProcessing,
              error: null,
            })
          ),
        })),
        order: vi.fn(() => ({
          limit: vi.fn(() =>
            Promise.resolve({
              data: mockValidationData.recentRuns,
              error: null,
            })
          ),
        })),
      })),
      count: vi.fn(() =>
        Promise.resolve({
          count: mockValidationData.pendingJobsCount,
          error: null,
        })
      ),
    })),
  })),
}));

// Mock fetch for API testing
global.fetch = vi.fn();

// Mock validation data
const mockValidationData = {
  stuckJobs: [], // Should be empty - no stuck jobs
  duplicateProcessing: [], // Should be empty - no duplicate processing
  recentRuns: [
    {
      id: 'wfr_test123',
      workflow_type: 'process-jobs',
      status: 'completed',
      started_at: '2025-01-01T12:00:00Z',
      completed_at: '2025-01-01T12:01:30Z',
      job_count: 5,
      error_message: null,
    },
    {
      id: 'wfr_test124',
      workflow_type: 'process-jobs',
      status: 'completed',
      started_at: '2025-01-01T12:05:00Z',
      completed_at: '2025-01-01T12:06:15Z',
      job_count: 3,
      error_message: null,
    },
  ],
  pendingJobsCount: 100,
};

describe('Workflow Validation Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Setup default fetch mock
    (global.fetch as any).mockResolvedValue({
      ok: true,
      status: 200,
      json: () =>
        Promise.resolve({
          success: true,
          messageId: 'wfr_validation123',
        }),
    });
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Race Condition Prevention', () => {
    it('should have no duplicate job processing', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      // Query for jobs that might be processed multiple times
      // This simulates checking for duplicate ai_metadata or concurrent processing
      const { data: duplicates } = await supabase
        .from('jobs')
        .select('id, processing_status, workflow_run_id, locked_at')
        .eq('processing_status', 'processing')
        .gte('locked_at', '2025-01-01T00:00:00Z')
        .lte('locked_at', '2025-01-01T23:59:59Z');

      expect(duplicates).toEqual([]);
      expect(duplicates.length).toBe(0);
    });

    it('should have no stuck jobs in processing state', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      // Check for jobs stuck in processing for more than 5 minutes
      const fiveMinutesAgo = new Date(Date.now() - 5 * 60 * 1000).toISOString();

      const { data: stuckJobs } = await supabase
        .from('jobs')
        .select('id, processing_status, locked_at, workflow_run_id')
        .eq('processing_status', 'processing')
        .lt('locked_at', fiveMinutesAgo);

      expect(stuckJobs).toEqual([]);
      expect(stuckJobs.length).toBe(0);
    });

    it('should have proper workflow_run_id isolation', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      // Verify that jobs with the same workflow_run_id are processed together
      // and different workflow runs don't interfere
      const { data: workflowJobs } = await supabase
        .from('jobs')
        .select('id, workflow_run_id, processing_status')
        .eq('processing_status', 'processing');

      // Group by workflow_run_id to check isolation
      const workflowGroups = workflowJobs.reduce(
        (groups, job) => {
          if (job.workflow_run_id) {
            groups[job.workflow_run_id] = groups[job.workflow_run_id] || [];
            groups[job.workflow_run_id].push(job);
          }
          return groups;
        },
        {} as Record<string, any[]>
      );

      // Each workflow run should have consistent processing
      Object.values(workflowGroups).forEach((jobs) => {
        const statuses = jobs.map((job) => job.processing_status);
        const uniqueStatuses = [...new Set(statuses)];
        expect(uniqueStatuses.length).toBeLessThanOrEqual(1);
      });
    });
  });

  describe('Workflow Execution Validation', () => {
    it('should have workflow runs completing successfully', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      // Get recent workflow runs
      const { data: runs } = await supabase
        .from('workflow_runs')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(10);

      expect(runs).toEqual(mockValidationData.recentRuns);
      expect(runs.length).toBeGreaterThan(0);

      // Check that recent runs are completing successfully
      const recentSuccessfulRuns = runs.filter(
        (run) => run.status === 'completed' && !run.error_message
      );

      expect(recentSuccessfulRuns.length).toBeGreaterThan(0);

      // Verify run completion times are reasonable (< 5 minutes)
      runs.forEach((run) => {
        if (run.completed_at) {
          const duration =
            new Date(run.completed_at).getTime() -
            new Date(run.started_at).getTime();
          const durationMinutes = duration / (1000 * 60);
          expect(durationMinutes).toBeLessThan(5);
        }
      });
    });

    it('should handle workflow errors gracefully', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      // Mock a failed workflow run
      const failedRun = {
        id: 'wfr_failed123',
        workflow_type: 'process-jobs',
        status: 'failed',
        started_at: '2025-01-01T12:00:00Z',
        completed_at: '2025-01-01T12:00:30Z',
        job_count: 0,
        error_message: 'Test error handling',
      };

      // Override mock to include failed run
      supabase
        .from()
        .select()
        .order()
        .limit.mockResolvedValueOnce({
          data: [failedRun, ...mockValidationData.recentRuns],
          error: null,
        });

      const { data: runs } = await supabase
        .from('workflow_runs')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(10);

      const failedRuns = runs.filter((run) => run.status === 'failed');
      expect(failedRuns.length).toBeGreaterThan(0);

      // Failed runs should have error messages
      failedRuns.forEach((run) => {
        expect(run.error_message).toBeTruthy();
        expect(run.completed_at).toBeTruthy();
      });
    });
  });

  describe('API Endpoint Validation', () => {
    it('should have workflow trigger endpoint responding correctly', async () => {
      const response = await fetch(
        'http://localhost:3000/api/workflows/status',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'trigger_processing',
            batchSize: 3,
          }),
        }
      );

      expect(response.ok).toBe(true);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.success).toBe(true);
      expect(data.messageId).toBeTruthy();
    });

    it('should have workflow status endpoint responding correctly', async () => {
      // Mock status response
      (global.fetch as any).mockResolvedValueOnce({
        ok: true,
        status: 200,
        json: () =>
          Promise.resolve({
            stats: {
              active: 0,
              completed: 5,
              failed: 0,
              partial_failure: 0,
            },
            queue: {
              pending: 100,
              processing: 0,
            },
            recent_runs: mockValidationData.recentRuns,
          }),
      });

      const response = await fetch(
        'http://localhost:3000/api/workflows/status'
      );

      expect(response.ok).toBe(true);
      expect(response.status).toBe(200);

      const data = await response.json();
      expect(data.stats).toBeDefined();
      expect(data.queue).toBeDefined();
      expect(data.recent_runs).toBeDefined();
      expect(Array.isArray(data.recent_runs)).toBe(true);
    });

    it('should handle invalid workflow trigger requests', async () => {
      // Mock error response
      (global.fetch as any).mockResolvedValueOnce({
        ok: false,
        status: 400,
        json: () =>
          Promise.resolve({
            error: 'Invalid action',
            message: 'Action must be "trigger_processing"',
          }),
      });

      const response = await fetch(
        'http://localhost:3000/api/workflows/status',
        {
          method: 'POST',
          headers: { 'Content-Type': 'application/json' },
          body: JSON.stringify({
            action: 'invalid_action',
          }),
        }
      );

      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);

      const data = await response.json();
      expect(data.error).toBeTruthy();
    });
  });

  describe('Performance Validation', () => {
    it('should process jobs within acceptable time limits', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      // Check recent workflow runs for performance
      const { data: runs } = await supabase
        .from('workflow_runs')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(5);

      runs.forEach((run) => {
        if (run.completed_at) {
          const duration =
            new Date(run.completed_at).getTime() -
            new Date(run.started_at).getTime();
          const durationSeconds = duration / 1000;

          // Should process within 30 seconds for small batches (< 10 jobs)
          if (run.job_count <= 10) {
            expect(durationSeconds).toBeLessThan(30);
          }

          // Should process within 60 seconds for larger batches
          expect(durationSeconds).toBeLessThan(60);
        }
      });
    });

    it('should have reasonable job processing throughput', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      const { data: runs } = await supabase
        .from('workflow_runs')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(5);

      const completedRuns = runs.filter(
        (run) => run.status === 'completed' && run.completed_at
      );

      completedRuns.forEach((run) => {
        const duration =
          new Date(run.completed_at).getTime() -
          new Date(run.started_at).getTime();
        const durationSeconds = duration / 1000;
        const jobsPerSecond = run.job_count / durationSeconds;

        // Should process at least 0.1 jobs per second (reasonable for AI processing)
        expect(jobsPerSecond).toBeGreaterThan(0.1);
      });
    });
  });

  describe('Data Integrity Validation', () => {
    it('should have consistent job status transitions', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      // Mock jobs with various statuses
      const mockJobs = [
        { id: '1', processing_status: 'pending', workflow_run_id: null },
        {
          id: '2',
          processing_status: 'completed',
          workflow_run_id: null,
          processed_at: '2025-01-01T12:00:00Z',
        },
        {
          id: '3',
          processing_status: 'failed',
          workflow_run_id: null,
          processed_at: '2025-01-01T12:00:00Z',
        },
      ];

      supabase.from().select().eq().lt.mockResolvedValueOnce({
        data: mockJobs,
        error: null,
      });

      const { data: jobs } = await supabase
        .from('jobs')
        .select('id, processing_status, workflow_run_id, processed_at')
        .eq('processing_status', 'completed')
        .lt('created_at', '2025-01-01T23:59:59Z');

      // Completed jobs should have processed_at timestamp
      const completedJobs = jobs.filter(
        (job) => job.processing_status === 'completed'
      );
      completedJobs.forEach((job) => {
        expect(job.processed_at).toBeTruthy();
        expect(job.workflow_run_id).toBeNull(); // Should be cleared after processing
      });
    });

    it('should have proper workflow run tracking', async () => {
      const { createClient } = await import('@/lib/supabase');
      const supabase = createClient();

      const { data: runs } = await supabase
        .from('workflow_runs')
        .select('*')
        .order('started_at', { ascending: false })
        .limit(10);

      runs.forEach((run) => {
        // All runs should have required fields
        expect(run.id).toBeTruthy();
        expect(run.workflow_type).toBeTruthy();
        expect(run.status).toBeTruthy();
        expect(run.started_at).toBeTruthy();
        expect(typeof run.job_count).toBe('number');

        // Completed/failed runs should have completion timestamp
        if (run.status === 'completed' || run.status === 'failed') {
          expect(run.completed_at).toBeTruthy();
        }
      });
    });
  });
});
