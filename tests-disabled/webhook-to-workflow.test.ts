/**
 * Integration Tests for Webhook → Workflow Flow
 *
 * Tests the complete integration from webhook ingestion to workflow triggering
 * Completely detached from production - safe to delete
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest';

// Mock all external dependencies
vi.mock('@upstash/qstash', () => ({
  Client: vi.fn(() => ({
    publishJSON: vi.fn(),
  })),
}));

vi.mock('@/lib/supabase', () => ({
  createClient: vi.fn(() => ({
    from: vi.fn(() => ({
      insert: vi.fn(() => ({
        select: vi.fn(() =>
          Promise.resolve({
            data: mockInsertedJobs,
            error: null,
          })
        ),
      })),
      select: vi.fn(() => ({
        eq: vi.fn(() =>
          Promise.resolve({
            data: [],
            error: null,
          })
        ),
      })),
    })),
  })),
}));

vi.mock('@/lib/utils', () => ({
  logger: {
    info: vi.fn(),
    error: vi.fn(),
    warn: vi.fn(),
  },
}));

// Mock data
const mockWebhookPayload = {
  jobs: [
    {
      id: 'ext-job-1',
      title: 'Senior Software Engineer',
      company: 'TechCorp',
      description: 'Build amazing software',
      url: 'https://example.com/job/1',
      location: 'San Francisco, CA',
      salary: '$120k-$180k',
    },
    {
      id: 'ext-job-2',
      title: 'Product Manager',
      company: 'StartupCo',
      description: 'Lead product strategy',
      url: 'https://example.com/job/2',
      location: 'Remote',
      salary: '$100k-$150k',
    },
  ],
};

const mockInsertedJobs = [
  {
    id: 'uuid-1',
    external_id: 'ext-job-1',
    processing_status: 'pending',
    raw_sourced_job_data: mockWebhookPayload.jobs[0],
    created_at: '2025-01-01T00:00:00Z',
  },
  {
    id: 'uuid-2',
    external_id: 'ext-job-2',
    processing_status: 'pending',
    raw_sourced_job_data: mockWebhookPayload.jobs[1],
    created_at: '2025-01-01T00:01:00Z',
  },
];

describe('Webhook → Workflow Integration Tests', () => {
  beforeEach(() => {
    vi.clearAllMocks();

    // Reset environment variables
    process.env.VERCEL_URL = 'test-app.vercel.app';
    process.env.QSTASH_TOKEN = 'test-token';
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('Webhook Data Ingestion', () => {
    it('should ingest webhook data and save to database', async () => {
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      // Simulate webhook data ingestion
      const jobsToInsert = mockWebhookPayload.jobs.map((job) => ({
        external_id: job.id,
        processing_status: 'pending',
        raw_sourced_job_data: job,
        source_name: 'test_webhook',
      }));

      const result = await mockSupabase
        .from('jobs')
        .insert(jobsToInsert)
        .select('*');

      expect(result.data).toEqual(mockInsertedJobs);
      expect(result.error).toBeNull();
      expect(mockSupabase.from).toHaveBeenCalledWith('jobs');
    });

    it('should handle duplicate external_id gracefully', async () => {
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      // Mock existing job check
      mockSupabase
        .from()
        .select()
        .eq.mockResolvedValueOnce({
          data: [{ external_id: 'ext-job-1' }], // Existing job
          error: null,
        });

      // Check for existing job
      const existingCheck = await mockSupabase
        .from('jobs')
        .select('external_id')
        .eq('external_id', 'ext-job-1');

      expect(existingCheck.data).toHaveLength(1);
      expect(existingCheck.data[0].external_id).toBe('ext-job-1');
    });

    it('should handle database insertion errors', async () => {
      const { createClient } = await import('@/lib/supabase');
      const mockSupabase = createClient();

      // Mock database error
      const dbError = new Error('Database constraint violation');
      mockSupabase.from().insert().select.mockResolvedValueOnce({
        data: null,
        error: dbError,
      });

      const result = await mockSupabase.from('jobs').insert([]).select('*');

      expect(result.data).toBeNull();
      expect(result.error).toEqual(dbError);
    });
  });

  describe('Workflow Triggering', () => {
    it('should trigger workflow after successful job insertion', async () => {
      const { Client } = await import('@upstash/qstash');
      const mockQStash = new Client({ token: 'test-token' });

      // Mock successful workflow trigger
      mockQStash.publishJSON.mockResolvedValue({
        messageId: 'msg_test123',
        url: 'https://test-app.vercel.app/api/workflows/process-jobs',
      });

      // Simulate workflow trigger
      const result = await mockQStash.publishJSON({
        url: 'https://test-app.vercel.app/api/workflows/process-jobs',
        body: {
          batchSize: 2,
          source: 'test_webhook',
        },
      });

      expect(result.messageId).toBe('msg_test123');
      expect(mockQStash.publishJSON).toHaveBeenCalledWith({
        url: 'https://test-app.vercel.app/api/workflows/process-jobs',
        body: {
          batchSize: 2,
          source: 'test_webhook',
        },
      });
    });

    it('should handle workflow trigger failures', async () => {
      const { Client } = await import('@upstash/qstash');
      const mockQStash = new Client({ token: 'test-token' });

      // Mock workflow trigger failure
      const triggerError = new Error('QStash service unavailable');
      mockQStash.publishJSON.mockRejectedValue(triggerError);

      try {
        await mockQStash.publishJSON({
          url: 'https://test-app.vercel.app/api/workflows/process-jobs',
          body: { batchSize: 2 },
        });
      } catch (error) {
        expect(error).toEqual(triggerError);
      }
    });

    it('should use correct environment-based URL', async () => {
      const { Client } = await import('@upstash/qstash');
      const mockQStash = new Client({ token: 'test-token' });

      // Test production URL
      process.env.VERCEL_URL = 'bordfeed.com';

      mockQStash.publishJSON.mockResolvedValue({
        messageId: 'msg_prod123',
      });

      await mockQStash.publishJSON({
        url: 'https://bordfeed.com/api/workflows/process-jobs',
        body: { batchSize: 5 },
      });

      expect(mockQStash.publishJSON).toHaveBeenCalledWith({
        url: 'https://bordfeed.com/api/workflows/process-jobs',
        body: { batchSize: 5 },
      });

      // Test localhost fallback
      delete process.env.VERCEL_URL;

      await mockQStash.publishJSON({
        url: 'http://localhost:3000/api/workflows/process-jobs',
        body: { batchSize: 3 },
      });

      expect(mockQStash.publishJSON).toHaveBeenCalledWith({
        url: 'http://localhost:3000/api/workflows/process-jobs',
        body: { batchSize: 3 },
      });
    });
  });

  describe('End-to-End Webhook Flow', () => {
    it('should complete full webhook → database → workflow flow', async () => {
      const { createClient } = await import('@/lib/supabase');
      const { Client } = await import('@upstash/qstash');

      const mockSupabase = createClient();
      const mockQStash = new Client({ token: 'test-token' });

      // Step 1: Webhook receives data
      const webhookData = mockWebhookPayload;

      // Step 2: Check for duplicates (none found)
      mockSupabase.from().select().eq.mockResolvedValue({
        data: [],
        error: null,
      });

      // Step 3: Insert jobs to database
      mockSupabase.from().insert().select.mockResolvedValue({
        data: mockInsertedJobs,
        error: null,
      });

      // Step 4: Trigger workflow
      mockQStash.publishJSON.mockResolvedValue({
        messageId: 'msg_e2e123',
      });

      // Execute the flow
      const duplicateCheck = await mockSupabase
        .from('jobs')
        .select('external_id')
        .eq('external_id', 'ext-job-1');

      const insertResult = await mockSupabase
        .from('jobs')
        .insert(
          webhookData.jobs.map((job) => ({
            external_id: job.id,
            processing_status: 'pending',
            raw_sourced_job_data: job,
          }))
        )
        .select('*');

      const workflowTrigger = await mockQStash.publishJSON({
        url: 'https://test-app.vercel.app/api/workflows/process-jobs',
        body: {
          batchSize: insertResult.data.length,
          source: 'test_webhook',
        },
      });

      // Verify complete flow
      expect(duplicateCheck.data).toEqual([]);
      expect(insertResult.data).toEqual(mockInsertedJobs);
      expect(workflowTrigger.messageId).toBe('msg_e2e123');
    });

    it('should handle partial failures gracefully', async () => {
      const { createClient } = await import('@/lib/supabase');
      const { Client } = await import('@upstash/qstash');

      const mockSupabase = createClient();
      const mockQStash = new Client({ token: 'test-token' });

      // Simulate partial database success (only 1 job inserted)
      mockSupabase
        .from()
        .insert()
        .select.mockResolvedValue({
          data: [mockInsertedJobs[0]], // Only first job
          error: null,
        });

      // Workflow should still trigger for the successful job
      mockQStash.publishJSON.mockResolvedValue({
        messageId: 'msg_partial123',
      });

      const insertResult = await mockSupabase
        .from('jobs')
        .insert([])
        .select('*');

      if (insertResult.data && insertResult.data.length > 0) {
        const workflowTrigger = await mockQStash.publishJSON({
          url: 'https://test-app.vercel.app/api/workflows/process-jobs',
          body: {
            batchSize: insertResult.data.length,
            source: 'test_webhook',
          },
        });

        expect(workflowTrigger.messageId).toBe('msg_partial123');
      }

      expect(insertResult.data).toHaveLength(1);
    });
  });

  describe('Webhook Source Handling', () => {
    it('should handle different webhook sources correctly', async () => {
      const { Client } = await import('@upstash/qstash');
      const mockQStash = new Client({ token: 'test-token' });

      const sources = ['jobdata_api', 'wwr_webhook', 'workable_webhook'];

      for (const source of sources) {
        mockQStash.publishJSON.mockResolvedValue({
          messageId: `msg_${source}_123`,
        });

        const result = await mockQStash.publishJSON({
          url: 'https://test-app.vercel.app/api/workflows/process-jobs',
          body: {
            batchSize: 5,
            source,
          },
        });

        expect(result.messageId).toBe(`msg_${source}_123`);
      }

      expect(mockQStash.publishJSON).toHaveBeenCalledTimes(3);
    });
  });

  describe('Batch Size Optimization', () => {
    it('should optimize batch size based on inserted job count', async () => {
      const { Client } = await import('@upstash/qstash');
      const mockQStash = new Client({ token: 'test-token' });

      const testCases = [
        { insertedCount: 1, expectedBatchSize: 1 },
        { insertedCount: 5, expectedBatchSize: 5 },
        { insertedCount: 15, expectedBatchSize: 10 }, // Should cap at 10
        { insertedCount: 0, expectedBatchSize: 0 }, // Should not trigger
      ];

      for (const testCase of testCases) {
        if (testCase.insertedCount > 0) {
          mockQStash.publishJSON.mockResolvedValue({
            messageId: `msg_batch_${testCase.insertedCount}`,
          });

          const actualBatchSize = Math.min(testCase.insertedCount, 10);

          await mockQStash.publishJSON({
            url: 'https://test-app.vercel.app/api/workflows/process-jobs',
            body: {
              batchSize: actualBatchSize,
              source: 'test_webhook',
            },
          });

          expect(mockQStash.publishJSON).toHaveBeenLastCalledWith({
            url: 'https://test-app.vercel.app/api/workflows/process-jobs',
            body: {
              batchSize: testCase.expectedBatchSize,
              source: 'test_webhook',
            },
          });
        }
      }
    });
  });
});
