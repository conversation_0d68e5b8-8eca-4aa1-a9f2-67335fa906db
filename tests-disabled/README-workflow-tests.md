# Workflow Testing Suite - Day 4 Implementation

This directory contains the complete Day 4 testing strategy from the rapid workflow build plan. All tests are completely detached from production code and safe to delete.

## 🧪 Test Structure

```
tests/
├── unit/
│   └── workflow-processing.test.ts      # Unit tests for workflow logic
├── integration/
│   ├── webhook-to-workflow.test.ts      # End-to-end webhook integration
│   └── workflow-validation.test.ts      # Validation checklist tests
├── scripts/
│   ├── run-workflow-tests.ts           # Main test runner
│   └── load-test-workflows.ts          # Load testing script
└── README-workflow-tests.md            # This documentation
```

## 🚀 Running Tests

### Quick Start
```bash
# Run all workflow tests
npm run test:workflow

# Run workflow tests with load testing
npm run test:workflow:load

# Run only load tests
npm run test:load
```

### Individual Test Suites
```bash
# Unit tests only
npx vitest run tests/unit/workflow-processing.test.ts

# Integration tests only
npx vitest run tests/integration/webhook-to-workflow.test.ts
npx vitest run tests/integration/workflow-validation.test.ts

# Load tests with different intensities
npx tsx tests/scripts/load-test-workflows.ts light
npx tsx tests/scripts/load-test-workflows.ts moderate
npx tsx tests/scripts/load-test-workflows.ts heavy
```

## 📋 Test Coverage

### Unit Tests (`workflow-processing.test.ts`)
Tests the core workflow processing logic in isolation:

- **Job Locking Logic**
  - ✅ Atomic job locking with correct parameters
  - ✅ Empty job queue handling
  - ✅ Database error handling during locking

- **AI Processing Logic**
  - ✅ Successful AI processing with structured output
  - ✅ AI processing failure handling
  - ✅ Parallel job processing

- **Result Saving Logic**
  - ✅ Successful processing result storage
  - ✅ Failed processing result handling

- **Workflow Step Execution**
  - ✅ Correct step execution order
  - ✅ Step isolation and error boundaries

### Integration Tests (`webhook-to-workflow.test.ts`)
Tests the complete webhook → database → workflow flow:

- **Webhook Data Ingestion**
  - ✅ Data ingestion and database storage
  - ✅ Duplicate external_id handling
  - ✅ Database insertion error handling

- **Workflow Triggering**
  - ✅ Successful workflow trigger after job insertion
  - ✅ Workflow trigger failure handling
  - ✅ Environment-based URL configuration

- **End-to-End Flow**
  - ✅ Complete webhook → database → workflow pipeline
  - ✅ Partial failure handling
  - ✅ Different webhook source handling
  - ✅ Batch size optimization

### Validation Tests (`workflow-validation.test.ts`)
Tests the validation checklist from the rapid build plan:

- **Race Condition Prevention**
  - ✅ No duplicate job processing
  - ✅ No stuck jobs in processing state
  - ✅ Proper workflow_run_id isolation

- **Workflow Execution Validation**
  - ✅ Workflow runs completing successfully
  - ✅ Graceful error handling
  - ✅ Reasonable completion times

- **API Endpoint Validation**
  - ✅ Workflow trigger endpoint functionality
  - ✅ Workflow status endpoint functionality
  - ✅ Invalid request handling

- **Performance Validation**
  - ✅ Acceptable processing time limits
  - ✅ Reasonable job processing throughput

- **Data Integrity Validation**
  - ✅ Consistent job status transitions
  - ✅ Proper workflow run tracking

### Load Tests (`load-test-workflows.ts`)
Tests workflow performance under concurrent load:

- **Test Configurations**
  - `light`: 2 concurrent requests, 3 iterations
  - `moderate`: 5 concurrent requests, 5 iterations  
  - `heavy`: 10 concurrent requests, 3 iterations
  - `production`: 3 concurrent requests against live site

- **Metrics Tracked**
  - Total/successful/failed requests
  - Response time statistics (min/max/average)
  - Requests per second
  - Error categorization

## 🔧 Configuration

### Environment Variables
Tests use these environment variables (with fallbacks):
- `VERCEL_URL` - Base URL for API calls (defaults to localhost:3000)
- `QSTASH_TOKEN` - Upstash Workflow token for triggers (mocked in tests)

### Test Data
All tests use mock data and don't interact with production:
- Mock Supabase client with controlled responses
- Mock Upstash Workflow client for triggering
- Mock AI SDK for processing simulation
- Predefined test job data

## 📊 Expected Results

### Success Criteria
- ✅ All unit tests pass (100% success rate)
- ✅ All integration tests pass (100% success rate)
- ✅ Load tests complete with <10% failure rate
- ✅ Response times under acceptable limits
- ✅ No race conditions detected
- ✅ Proper error handling verified

### Performance Benchmarks
- **Response Time**: <2000ms for workflow triggers
- **Throughput**: >0.1 jobs/second processing rate
- **Concurrency**: Handle 5+ concurrent workflow triggers
- **Error Rate**: <10% under normal load

## 🛠️ Troubleshooting

### Common Issues

**Tests fail with "Cannot find module" errors:**
```bash
# Install dependencies
npm install
```

**Load tests fail with connection errors:**
```bash
# Start local development server first
npm run dev

# Then run load tests
npm run test:load light
```

**Vitest not found:**
```bash
# Install vitest globally or use npx
npm install -g vitest
# or
npx vitest --version
```

### Debug Mode
Run tests with verbose output:
```bash
npx vitest run tests/unit/workflow-processing.test.ts --reporter=verbose
```

## 🎯 Day 4 Completion Checklist

- [x] **Unit Tests**: Core workflow processing logic tested
- [x] **Integration Tests**: Webhook → workflow flow tested  
- [x] **Load Testing**: Performance under concurrent load tested
- [x] **Validation Tests**: Rapid build plan checklist verified
- [x] **Test Runner**: Automated test execution implemented
- [x] **Documentation**: Complete test documentation provided

## 🚀 Next Steps

After running these tests:

1. **Fix any failing tests** - Address issues discovered during testing
2. **Run validation against production** - Use production URL for final validation
3. **Monitor real workflow execution** - Verify tests match production behavior
4. **Optimize based on results** - Improve performance based on load test results

The Day 4 testing strategy is now complete and ready for execution!
