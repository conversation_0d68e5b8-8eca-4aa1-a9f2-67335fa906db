import type { Metada<PERSON> } from 'next';
import { Geist_Mono, Inter } from 'next/font/google';
import './globals.css';
import { RootProvider } from 'fumadocs-ui/provider';
import { NuqsAdapter } from 'nuqs/adapters/next/app';
import { Toaster } from 'sonner';
import { StoreProvider } from '@/lib/stores/provider';

const inter = Inter({
  variable: '--font-inter-sans',
  subsets: ['latin'],
});

const geistMono = Geist_Mono({
  variable: '--font-geist-mono',
  subsets: ['latin'],
});

export const metadata: Metadata = {
  title: 'Bordfeed',
  description:
    'AI-powered job board automation platform with intelligent extraction, monitoring, and multi-platform publishing',
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={`${inter.variable} ${geistMono.variable} antialiased`}>
        <StoreProvider>
          <RootProvider>
            <NuqsAdapter>{children}</NuqsAdapter>
            <Toaster position="top-right" richColors />
          </RootProvider>
        </StoreProvider>
      </body>
    </html>
  );
}
