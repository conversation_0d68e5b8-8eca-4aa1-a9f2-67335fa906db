@import "tailwindcss";
@import "fumadocs-ui/css/shadcn.css";
@import "fumadocs-ui/css/preset.css";
@import "fumadocs-openapi/css/preset.css";
@import "@scalar/api-client-react/style.css" layer(base);

/* Scalar API Reference Integration Styles */
.scalar-app {
  --scalar-font-family:
    ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji",
    "Segoe UI Symbol", "Noto Color Emoji";
}

/* HTTP Method Badges - Perplexity Style */
.http-method-badge {
  @apply inline-flex items-center px-2 py-1 text-xs font-semibold rounded uppercase;
}

.http-method-get {
  @apply bg-green-500 text-white;
}

.http-method-post {
  @apply bg-blue-500 text-white;
}

.http-method-put {
  @apply bg-amber-500 text-white;
}

.http-method-patch {
  @apply bg-purple-500 text-white;
}

.http-method-delete {
  @apply bg-red-500 text-white;
}

/* Scalar iframe integration */
.docs-iframe {
  border: 1px solid hsl(var(--border));
  border-radius: 0.5rem;
  box-shadow:
    0 10px 15px -3px rgb(0 0 0 / 0.1),
    0 4px 6px -4px rgb(0 0 0 / 0.1);
  transition: box-shadow 0.2s ease;
}

.docs-iframe:hover {
  box-shadow:
    0 20px 25px -5px rgb(0 0 0 / 0.1),
    0 10px 10px -5px rgb(0 0 0 / 0.1);
}

@media (prefers-color-scheme: dark) {
  .docs-iframe {
    border-color: hsl(var(--border));
    box-shadow:
      0 10px 15px -3px rgb(0 0 0 / 0.3),
      0 4px 6px -4px rgb(0 0 0 / 0.3);
  }

  .docs-iframe:hover {
    box-shadow:
      0 20px 25px -5px rgb(0 0 0 / 0.3),
      0 10px 10px -5px rgb(0 0 0 / 0.3);
  }
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-inter-sans);
  --font-mono: var(--font-geist-mono);

  /* Ensure Fumadocs uses our fonts */
  --font-family-sans: var(--font-inter-sans);
  --font-family-mono: var(--font-geist-mono);

  /* shadcn/ui semantic color mappings */
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --radius: 0.625rem;
  --background: oklch(1 0 0);
  --foreground: oklch(0.129 0.042 264.695);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.129 0.042 264.695);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.129 0.042 264.695);
  --primary: oklch(0.208 0.042 265.755);
  --primary-foreground: oklch(0.984 0.003 247.858);
  --secondary: oklch(0.968 0.007 247.896);
  --secondary-foreground: oklch(0.208 0.042 265.755);
  --muted: oklch(0.968 0.007 247.896);
  --muted-foreground: oklch(0.554 0.046 257.417);
  --accent: oklch(0.968 0.007 247.896);
  --accent-foreground: oklch(0.208 0.042 265.755);
  --destructive: oklch(0.577 0.245 27.325);
  --border: oklch(0.929 0.013 255.508);
  --input: oklch(0.929 0.013 255.508);
  --ring: oklch(0.704 0.04 256.788);
  --chart-1: oklch(0.646 0.222 41.116);
  --chart-2: oklch(0.6 0.118 184.704);
  --chart-3: oklch(0.398 0.07 227.392);
  --chart-4: oklch(0.828 0.189 84.429);
  --chart-5: oklch(0.769 0.188 70.08);
  --sidebar: oklch(0.984 0.003 247.858);
  --sidebar-foreground: oklch(0.129 0.042 264.695);
  --sidebar-primary: oklch(0.208 0.042 265.755);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.968 0.007 247.896);
  --sidebar-accent-foreground: oklch(0.208 0.042 265.755);
  --sidebar-border: oklch(0.929 0.013 255.508);
  --sidebar-ring: oklch(0.704 0.04 256.788);
}

.dark {
  --background: oklch(0.129 0.042 264.695);
  /* Soften foreground brightness to reduce harsh contrast */
  --foreground: oklch(0.92 0.003 247.858);
  --card: oklch(0.208 0.042 265.755);
  --card-foreground: oklch(0.92 0.003 247.858);
  --popover: oklch(0.208 0.042 265.755);
  --popover-foreground: oklch(0.92 0.003 247.858);
  /* Slightly soften brand/primary brightness in dark mode */
  --primary: oklch(0.88 0.013 255.508);
  --primary-foreground: oklch(0.208 0.042 265.755);
  --secondary: oklch(0.279 0.041 260.031);
  --secondary-foreground: oklch(0.92 0.003 247.858);
  --muted: oklch(0.279 0.041 260.031);
  /* Keep muted readable but not stark */
  --muted-foreground: oklch(0.72 0.035 256.788);
  --accent: oklch(0.279 0.041 260.031);
  --accent-foreground: oklch(0.92 0.003 247.858);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.551 0.027 264.364);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.208 0.042 265.755);
  --sidebar-foreground: oklch(0.984 0.003 247.858);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.984 0.003 247.858);
  --sidebar-accent: oklch(0.279 0.041 260.031);
  --sidebar-accent-foreground: oklch(0.984 0.003 247.858);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.551 0.027 264.364);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
  }

  h1,
  h2,
  h3,
  h4,
  h5,
  h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-3xl;
  }

  h2 {
    @apply text-2xl;
  }

  h3 {
    @apply text-xl;
  }

  h4 {
    @apply text-lg;
  }

  p {
    @apply text-sm leading-relaxed text-muted-foreground;
  }

  small {
    @apply text-xs;
  }

  code {
    @apply font-mono text-sm;
  }
}

/* Fumadocs customizations */
/* Fix sidebar behavior - make it less floating/more integrated */
@media (min-width: 1024px) {
  [data-docs-layout] aside[data-sidebar] {
    position: sticky !important;
    top: 0;
    height: 100vh;
    border-right: 1px solid hsl(var(--border));
    transition: transform 0.2s ease-in-out;
  }

  /* When sidebar is collapsed, hide it completely on desktop */
  [data-docs-layout][data-sidebar-collapsed="true"] aside[data-sidebar] {
    transform: translateX(-100%);
    width: 0;
    min-width: 0;
    border-right: none;
  }
}

/* Ensure consistent font usage in docs */
[data-docs-layout] {
  font-family: var(--font-inter-sans), ui-sans-serif, system-ui, sans-serif;
}

[data-docs-layout] code,
[data-docs-layout] pre {
  font-family: var(--font-geist-mono), ui-monospace, monospace;
}
