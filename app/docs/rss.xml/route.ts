import { NextResponse } from 'next/server';
import { source } from '@/lib/source';

export const revalidate = false;

const baseUrl = 'https://bordfeed.com';

export function GET() {
  const pages = source.getPages();

  const rssItems = pages
    .map((page) => {
      const pubDate = new Date().toUTCString(); // You could add a date field to your MDX frontmatter

      return `
    <item>
      <title><![CDATA[${page.data.title}]]></title>
      <description><![CDATA[${page.data.description || ''}]]></description>
      <link>${baseUrl}${page.url}</link>
      <guid>${baseUrl}${page.url}</guid>
      <pubDate>${pubDate}</pubDate>
    </item>`;
    })
    .join('');

  const rss = `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>Bordfeed Documentation</title>
    <description>AI-powered job board automation platform documentation</description>
    <link>${baseUrl}/docs</link>
    <atom:link href="${baseUrl}/docs/rss.xml" rel="self" type="application/rss+xml"/>
    <language>en</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    ${rssItems}
  </channel>
</rss>`;

  return new NextResponse(rss, {
    headers: {
      'Content-Type': 'application/xml',
    },
  });
}
