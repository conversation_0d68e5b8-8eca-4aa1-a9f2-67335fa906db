import type { Metadata } from 'next';
import { InteractiveExamples } from './interactive-examples';

export const metadata: Metadata = {
  title: 'API Examples - Bordfeed',
  description: 'Interactive examples using real data from the Bordfeed API',
};

export default function APIExamplesPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mx-auto max-w-4xl">
        <div className="mb-8">
          <h1 className="mb-4 font-bold text-3xl">Bordfeed API Examples</h1>
          <p className="text-lg text-muted-foreground">
            Interactive examples using real (sanitized) data from the Bordfeed
            application. These examples demonstrate actual API responses and
            data structures.
          </p>
        </div>

        <InteractiveExamples />
      </div>
    </div>
  );
}
