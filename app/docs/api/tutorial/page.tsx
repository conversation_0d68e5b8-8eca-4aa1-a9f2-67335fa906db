import { AlertCircle, CheckCircle, ExternalLink } from 'lucide-react';
import type { Metadata } from 'next';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';

export const metadata: Metadata = {
  title: 'API Tutorial - Bordfeed',
  description:
    'Step-by-step tutorial for using the Bordfeed API with real examples',
};

export default function APITutorialPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      <div className="mx-auto max-w-4xl">
        <div className="mb-8">
          <h1 className="mb-4 font-bold text-3xl">Bordfeed API Tutorial</h1>
          <p className="text-lg text-muted-foreground">
            Learn how to integrate with the Bordfeed API using real examples and
            step-by-step instructions.
          </p>
        </div>

        <div className="space-y-8">
          {/* Getting Started */}
          <section>
            <h2 className="mb-4 font-semibold text-2xl">Getting Started</h2>

            <Alert className="mb-6">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                The Bordfeed API uses QStash for secure webhook authentication.
                You'll need a QStash account and signature for protected
                endpoints.
              </AlertDescription>
            </Alert>

            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Step 1: Check API Health
                  </CardTitle>
                  <CardDescription>
                    Start by verifying the API is operational
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="rounded-lg bg-muted p-4">
                      <code className="text-sm">
                        curl https://bordfeed.com/api/health
                      </code>
                    </div>
                    <p className="text-muted-foreground text-sm">
                      This endpoint requires no authentication and returns the
                      system status.
                    </p>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    Step 2: Set Up Authentication
                  </CardTitle>
                  <CardDescription>
                    Configure QStash signature for protected endpoints
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div className="rounded-lg bg-muted p-4">
                      <pre className="text-sm">{`# Set your QStash signature
export QSTASH_SIGNATURE="your-qstash-signature"

# Include in requests
curl -H "Authorization: Bearer $QSTASH_SIGNATURE" \\
     https://bordfeed.com/api/pipeline-ingest`}</pre>
                    </div>
                    <p className="text-muted-foreground text-sm">
                      Get your QStash signature from the{' '}
                      <a
                        className="text-blue-500 hover:underline"
                        href="https://console.upstash.com"
                      >
                        Upstash Console
                      </a>
                      .
                    </p>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Processing Jobs */}
          <section>
            <h2 className="mb-4 font-semibold text-2xl">
              Processing Job Postings
            </h2>

            <Tabs className="w-full" defaultValue="single">
              <TabsList className="grid w-full grid-cols-2">
                <TabsTrigger value="single">Single Job</TabsTrigger>
                <TabsTrigger value="batch">Batch Processing</TabsTrigger>
              </TabsList>

              <TabsContent value="single">
                <Card>
                  <CardHeader>
                    <CardTitle>Process a Single Job Posting</CardTitle>
                    <CardDescription>
                      Extract job data from a single URL using AI-powered
                      processing
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="rounded-lg bg-muted p-4">
                        <pre className="text-sm">{`curl -X POST https://bordfeed.com/api/pipeline-ingest \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer $QSTASH_SIGNATURE" \\
  -d '{
    "content": "Senior Software Engineer position at TechCorp...",
    "sourceUrl": "https://techcorp.com/careers/senior-engineer",
    "source": "techcorp-careers",
    "metadata": {
      "title": "Senior Software Engineer",
      "company": "TechCorp",
      "priority": "high"
    }
  }'`}</pre>
                      </div>

                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                          The API will extract structured job data including
                          title, company, salary, skills, and more using AI.
                        </AlertDescription>
                      </Alert>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>

              <TabsContent value="batch">
                <Card>
                  <CardHeader>
                    <CardTitle>Process Multiple Jobs</CardTitle>
                    <CardDescription>
                      Process multiple job postings in a single request for
                      efficiency
                    </CardDescription>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="rounded-lg bg-muted p-4">
                        <pre className="text-sm">{`curl -X POST https://bordfeed.com/api/pipeline-ingest \\
  -H "Content-Type: application/json" \\
  -H "Authorization: Bearer $QSTASH_SIGNATURE" \\
  -d '{
    "jobs": [
      {
        "content": "Frontend Developer role...",
        "sourceUrl": "https://example.com/job/124",
        "source": "example-board"
      },
      {
        "content": "Backend Engineer position...",
        "sourceUrl": "https://example.com/job/125",
        "source": "example-board"
      }
    ],
    "batchId": "batch-002",
    "priority": "high"
  }'`}</pre>
                      </div>

                      <Alert>
                        <CheckCircle className="h-4 w-4" />
                        <AlertDescription>
                          Batch processing is more cost-effective and provides
                          detailed metrics for each job in the response.
                        </AlertDescription>
                      </Alert>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </section>

          {/* Understanding Responses */}
          <section>
            <h2 className="mb-4 font-semibold text-2xl">
              Understanding API Responses
            </h2>

            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle className="text-green-600">
                    Success Response (200)
                  </CardTitle>
                  <CardDescription>
                    When job processing completes successfully
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="rounded-lg bg-muted p-4">
                    <pre className="text-sm">{`{
  "success": true,
  "message": "✅ Pipeline processing completed successfully",
  "summary": {
    "total": 1,
    "successful": 1,
    "failed": 0,
    "duplicates": 0
  },
  "results": [
    {
      "jobId": "job-001",
      "status": "success",
      "extractedData": {
        "title": "Senior Software Engineer",
        "company": "TechCorp",
        "location": "San Francisco, CA",
        "salary": "$120,000 - $180,000",
        "skills": ["JavaScript", "React", "Node.js"]
      },
      "metadata": {
        "cost": 0.0012,
        "duration": 1250
      }
    }
  ],
  "metrics": {
    "totalCost": 0.0012,
    "totalDuration": 1250
  }
}`}</pre>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="text-red-600">
                    Error Response (400)
                  </CardTitle>
                  <CardDescription>
                    When request validation fails
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="rounded-lg bg-muted p-4">
                    <pre className="text-sm">{`{
  "error": "Validation Error",
  "message": "Invalid job data: missing required field 'sourceUrl'",
  "details": {
    "code": "REQUIRED_FIELD_MISSING",
    "field": "sourceUrl",
    "provided_fields": ["content", "source"],
    "required_fields": ["content", "sourceUrl", "source"]
  },
  "timestamp": "2024-01-17T10:00:00Z"
}`}</pre>
                  </div>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Webhook Integration */}
          <section>
            <h2 className="mb-4 font-semibold text-2xl">Webhook Integration</h2>

            <Card>
              <CardHeader>
                <CardTitle>Handling QStash Callbacks</CardTitle>
                <CardDescription>
                  Process webhook callbacks from QStash for async job processing
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="rounded-lg bg-muted p-4">
                    <pre className="text-sm">{`// Example webhook handler
app.post('/webhook-handler', (req, res) => {
  const { messageId, status, body } = req.body;

  if (status === 'success') {
    const result = JSON.parse(atob(body));
    console.log('Job processed:', result.summary);

    // Process successful results
    result.results.forEach(job => {
      if (job.status === 'success') {
        // Save to database, send notifications, etc.
        saveJobToDatabase(job.extractedData);
      }
    });
  } else {
    console.error('Job processing failed:', messageId);
  }

  res.status(200).json({ received: true });
});`}</pre>
                  </div>

                  <Alert>
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription>
                      Always verify QStash signatures in production to ensure
                      webhook authenticity.
                    </AlertDescription>
                  </Alert>
                </div>
              </CardContent>
            </Card>
          </section>

          {/* Best Practices */}
          <section>
            <h2 className="mb-4 font-semibold text-2xl">Best Practices</h2>

            <div className="grid gap-4">
              <Card>
                <CardHeader>
                  <CardTitle>Rate Limiting & Costs</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>
                      • Use batch processing for multiple jobs to reduce costs
                    </li>
                    <li>• Monitor API usage through response metrics</li>
                    <li>• Implement exponential backoff for retries</li>
                    <li>• Cache results to avoid duplicate processing</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Error Handling</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>
                      • Always check the <code>success</code> field in responses
                    </li>
                    <li>• Handle partial failures in batch processing</li>
                    <li>• Log error codes and details for debugging</li>
                    <li>• Implement retry logic for transient errors</li>
                  </ul>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Data Quality</CardTitle>
                </CardHeader>
                <CardContent>
                  <ul className="space-y-2 text-sm">
                    <li>• Provide clean, well-formatted job content</li>
                    <li>• Include relevant metadata for better extraction</li>
                    <li>• Validate extracted data before using</li>
                    <li>• Monitor duplicate detection results</li>
                  </ul>
                </CardContent>
              </Card>
            </div>
          </section>

          {/* Next Steps */}
          <section>
            <Card>
              <CardHeader>
                <CardTitle>Next Steps</CardTitle>
                <CardDescription>
                  Ready to start building with the Bordfeed API?
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="flex gap-4">
                  <Button asChild>
                    <a
                      className="flex items-center gap-2"
                      href="/docs/api/reference"
                    >
                      <ExternalLink className="h-4 w-4" />
                      Try Interactive API Reference
                    </a>
                  </Button>
                  <Button asChild variant="outline">
                    <a
                      className="flex items-center gap-2"
                      href="/docs/api/examples"
                    >
                      <ExternalLink className="h-4 w-4" />
                      View Real Examples
                    </a>
                  </Button>
                  <Button asChild variant="outline">
                    <a
                      className="flex items-center gap-2"
                      href="/api/reference"
                    >
                      <ExternalLink className="h-4 w-4" />
                      Full-Screen API Reference
                    </a>
                  </Button>
                  <Button asChild variant="outline">
                    <a
                      className="flex items-center gap-2"
                      href="/api/openapi.json"
                    >
                      <ExternalLink className="h-4 w-4" />
                      Download OpenAPI Spec
                    </a>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </section>
        </div>
      </div>
    </div>
  );
}
