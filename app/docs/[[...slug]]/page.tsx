import { DocsBody, DocsPage } from 'fumadocs-ui/page';
import type { Metadata } from 'next';
import { notFound } from 'next/navigation';
import { logger } from '@/lib/logger';
import { source } from '@/lib/source';

// Enable static generation for better performance
export const dynamic = 'force-static';
export const revalidate = 3600; // Revalidate every hour

export default async function Page({
  params,
}: {
  params: Promise<{ slug?: string[] }>;
}) {
  try {
    const { slug = [] } = await params;
    const page = source.getPage(slug);

    if (!page) {
      notFound();
    }

    const MDX = page.data.body;

    return (
      <DocsPage full={page.data.full} toc={page.data.toc}>
        <DocsBody>
          <h1>{page.data.title}</h1>
          <MDX />
        </DocsBody>
      </DocsPage>
    );
  } catch (error) {
    logger.error('Error rendering docs page:', error);
    notFound();
  }
}

export function generateStaticParams() {
  return source.generateParams();
}

export async function generateMetadata({
  params,
}: {
  params: Promise<{ slug?: string[] }>;
}): Promise<Metadata> {
  const { slug = [] } = await params;
  const page = source.getPage(slug);
  if (!page) {
    notFound();
  }

  const title = page.data.title;
  const description = page.data.description;

  return {
    title,
    description,
    openGraph: {
      title,
      description,
      type: 'article',
      url: `https://bordfeed.com/docs/${slug.join('/')}`,
      siteName: 'Bordfeed Documentation',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
    },
    alternates: {
      canonical: `https://bordfeed.com/docs/${slug.join('/')}`,
    },
  };
}
