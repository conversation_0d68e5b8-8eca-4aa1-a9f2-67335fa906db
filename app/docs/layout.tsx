import { DocsLayout } from 'fumadocs-ui/layouts/docs';
import { ArrowLeft } from 'lucide-react';
import type { ReactNode } from 'react';
import { source } from '@/lib/source';

// Revalidate docs every hour in production
export const revalidate = 3600;

export default function Layout({ children }: { children: ReactNode }) {
  return (
    <DocsLayout
      links={[
        {
          icon: <ArrowLeft />,
          text: 'Back to Dashboard',
          url: '/dashboard',
          secondary: true,
        },
      ]}
      nav={{
        title: 'Bordfeed Docs',
        url: '/docs',
      }}
      tree={source.pageTree}
    >
      {children}
    </DocsLayout>
  );
}
