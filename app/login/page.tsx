'use client';

import { useRouter } from 'next/navigation';
import { useState } from 'react';

interface CookieStoreSetOptions {
  name: string;
  value: string;
  path?: string;
  sameSite?: 'strict' | 'lax' | 'none';
  secure?: boolean;
}

interface CookieStore {
  set(options: CookieStoreSetOptions): Promise<void>;
}

export default function LoginPage() {
  const [password, setPassword] = useState('');
  const [error, setError] = useState('');
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      const response = await fetch('/api/auth', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ password }),
      });

      const data = await response.json();

      if (response.ok && data.success) {
        // Set cookie using a more secure approach
        const cookieValue =
          'auth=authenticated; path=/; SameSite=Strict; Secure';
        if (typeof document !== 'undefined' && 'cookieStore' in window) {
          // Use modern Cookie Store API if available
          await (
            window as Window & { cookieStore: CookieStore }
          ).cookieStore.set({
            name: 'auth',
            value: 'authenticated',
            path: '/',
            sameSite: 'strict',
            secure: true,
          });
        } else if (typeof document !== 'undefined') {
          // Fallback to document.cookie for browsers without Cookie Store API support
          // This is intentional and necessary for compatibility with older browsers
          // biome-ignore lint/suspicious/noDocumentCookie: Required fallback for older browsers
          document.cookie = cookieValue;
        }
        router.push('/');
        router.refresh();
      } else {
        setError('Invalid password');
      }
    } catch (_error) {
      setError('Something went wrong');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 px-4 py-12 sm:px-6 lg:px-8">
      <div className="w-full max-w-md space-y-8">
        <div>
          <h2 className="mt-6 text-center font-extrabold text-3xl text-gray-900">
            Access Required
          </h2>
          <p className="mt-2 text-center text-gray-600 text-sm">
            Enter the password to continue
          </p>
        </div>
        <form className="mt-8 space-y-6" onSubmit={handleSubmit}>
          <div>
            <label className="sr-only" htmlFor="password">
              Password
            </label>
            <input
              autoComplete="current-password"
              className="relative block w-full appearance-none rounded-md border border-gray-300 px-3 py-2 text-gray-900 placeholder-gray-500 focus:z-10 focus:border-indigo-500 focus:outline-none focus:ring-indigo-500 sm:text-sm"
              disabled={loading}
              id="password"
              name="password"
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Password"
              required
              type="password"
              value={password}
            />
          </div>

          {error && (
            <div className="text-center text-red-600 text-sm">{error}</div>
          )}

          <div>
            <button
              className="group relative flex w-full justify-center rounded-md border border-transparent bg-indigo-600 px-4 py-2 font-medium text-sm text-white hover:bg-indigo-700 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2 disabled:opacity-50"
              disabled={loading}
              type="submit"
            >
              {loading ? 'Checking...' : 'Sign in'}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
