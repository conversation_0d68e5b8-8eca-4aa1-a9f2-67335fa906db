import { NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

/**
 * API Versions Information Endpoint
 *
 * Provides comprehensive information about API versions, support status,
 * and migration paths for the Bordfeed API.
 */

// Version configuration
const API_VERSIONS = {
  current: '0.1.7',
  latest_stable: '0.1.7',
  supported: ['0.1.7'] as string[],
  deprecated: [] as string[],
  retired: [] as string[],
  versions: {
    '0.1.7': {
      status: 'current' as const,
      released: '2024-01-15T00:00:00Z',
      openapi: '/api/openapi.json',
      documentation: '/docs/api',
      changelog: '/docs/changelog/v0.1.7',
      features: [
        'Pipeline job processing',
        'Health monitoring',
        'Webhook callbacks',
        'QStash integration',
        'AI-powered extraction',
        'Batch processing',
        'Error handling',
        'Rate limiting',
        'OpenAPI documentation',
      ],
      breaking_changes: [],
      migration_guide: null,
      support_until: null, // Current version - no end date
    },
    // Future versions will be added here
    '1.0.0': {
      status: 'planned' as const,
      released: null,
      openapi: '/api/v1/openapi.json',
      documentation: '/docs/api/v1',
      changelog: '/docs/changelog/v1.0.0',
      features: [
        'All v0.1.7 features',
        'Stable API guarantees',
        'Enhanced authentication',
        'Improved rate limiting',
        'Advanced monitoring',
        'Client SDK generation',
        'Webhook retry policies',
        'Bulk operations',
        'Real-time status updates',
      ],
      breaking_changes: [
        'URL structure changes to /api/v1/',
        'Enhanced error response format',
        'Updated authentication headers',
        'Modified webhook payload structure',
      ],
      migration_guide: '/docs/api/migration/v0-to-v1',
      support_until: null,
    },
  },
};

// Version status type
type VersionStatus =
  | 'current'
  | 'supported'
  | 'deprecated'
  | 'retired'
  | 'planned';

interface VersionInfo {
  status: VersionStatus;
  released: string | null;
  openapi: string;
  documentation: string;
  changelog: string;
  features: string[];
  breaking_changes: string[];
  migration_guide: string | null;
  support_until: string | null;
}

interface VersionsResponse {
  current: string;
  latest_stable: string;
  supported: string[];
  deprecated: string[];
  retired: string[];
  versions: Record<string, VersionInfo>;
  api_info: {
    name: string;
    description: string;
    base_url: string;
    versioning_scheme: string;
    support_policy: string;
  };
  deprecation_policy: {
    notice_period: string;
    support_duration: string;
    migration_assistance: boolean;
  };
}

export function GET() {
  try {
    const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

    const response: VersionsResponse = {
      ...API_VERSIONS,
      api_info: {
        name: 'Bordfeed API',
        description: 'AI-powered job board automation platform API',
        base_url: baseUrl,
        versioning_scheme: 'Semantic Versioning (SemVer)',
        support_policy: 'Current + Previous Major Version',
      },
      deprecation_policy: {
        notice_period: '6 months',
        support_duration: '12 months for major versions',
        migration_assistance: true,
      },
    };

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'Content-Type': 'application/json',
        'Cache-Control': 'public, max-age=3600', // Cache for 1 hour
        'API-Version': API_VERSIONS.current,
        'API-Supported-Versions': API_VERSIONS.supported.join(', '),
        'API-Deprecated-Versions': API_VERSIONS.deprecated.join(', '),
      },
    });
  } catch (error) {
    logger.error('Error in versions endpoint:', error);

    return NextResponse.json(
      {
        error: 'Internal Server Error',
        message: 'Failed to retrieve version information',
        code: 'VERSION_INFO_ERROR',
        timestamp: new Date().toISOString(),
      },
      {
        status: 500,
        headers: {
          'Content-Type': 'application/json',
          'API-Version': API_VERSIONS.current,
        },
      }
    );
  }
}

// Health check for versions endpoint
export function HEAD() {
  return NextResponse.json(null, {
    status: 200,
    headers: {
      'API-Version': API_VERSIONS.current,
      'API-Supported-Versions': API_VERSIONS.supported.join(', '),
      'Cache-Control': 'public, max-age=3600',
    },
  });
}

// Version configuration is available within this module only
