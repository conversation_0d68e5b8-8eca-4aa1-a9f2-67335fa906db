import { NextResponse } from 'next/server';
import { handleApiError } from '@/lib/api-utils';
import { createClient } from '@/lib/supabase';

export async function GET() {
  const supabase = createClient();

  try {
    // Core processing status counts
    const [totalRes, pendingRes, completedRes] = await Promise.all([
      supabase.from('jobs').select('*', { count: 'exact', head: true }),
      supabase
        .from('jobs')
        .select('*', { count: 'exact', head: true })
        .eq('processing_status', 'pending'),
      supabase
        .from('jobs')
        .select('*', { count: 'exact', head: true })
        .eq('processing_status', 'completed'),
    ]);

    // Monitoring status counts (active, closed, filled, unknown)
    const monitorKeys = ['active', 'closed', 'filled', 'unknown'] as const;
    const statusQueries = monitorKeys.map((status) =>
      supabase
        .from('jobs')
        .select('*', { count: 'exact', head: true })
        .eq('status', status)
    );

    const statusResults = await Promise.all(statusQueries);

    if (
      totalRes.error ||
      pendingRes.error ||
      completedRes.error ||
      statusResults.some((r) => r.error)
    ) {
      throw (
        totalRes.error ||
        pendingRes.error ||
        completedRes.error ||
        statusResults.find((r) => r.error)?.error ||
        new Error('Unknown Supabase error')
      );
    }

    const monitoring: Record<string, number> = {};
    for (const [idx, res] of statusResults.entries()) {
      monitoring[monitorKeys[idx]] = res.count ?? 0;
    }

    return NextResponse.json({
      success: true,
      stats: {
        total: totalRes.count ?? 0,
        pending: pendingRes.count ?? 0,
        completed: completedRes.count ?? 0,
        monitoring,
      },
    });
  } catch (error) {
    return handleApiError(error, 'Failed to fetch job statistics');
  }
}
