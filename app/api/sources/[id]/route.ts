import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';
import { logger } from '@/lib/utils';

export const dynamic = 'force-dynamic';

/**
 * Individual Source Operations API
 *
 * GET    /api/sources/[id] - Get single source with stats
 * POST   /api/sources/[id] - Log health check result
 */

interface SourceHealthLog {
  status: 'success' | 'error' | 'timeout' | 'rate_limited';
  response_time_ms?: number;
  jobs_returned?: number;
  error_message?: string;
  metadata?: Record<string, unknown>;
}

async function logHealthCheck(
  // biome-ignore lint/suspicious/noExplicitAny: Supabase client return types are complex
  supabase: any,
  sourceId: string,
  body: SourceHealthLog
) {
  const { error: logError } = await supabase
    .from('job_source_health_logs')
    .insert({
      source_id: sourceId,
      status: body.status,
      response_time_ms: body.response_time_ms,
      jobs_returned: body.jobs_returned || 0,
      error_message: body.error_message,
      metadata: body.metadata || {},
    });

  if (logError) {
    logger.error('Error logging health check:', logError);
  }
}

async function updateSuccessStats(
  // biome-ignore lint/suspicious/noExplicitAny: Supabase client return types are complex
  supabase: any,
  sourceId: string,
  body: SourceHealthLog
) {
  // Get current stats first to calculate new values
  const { data: currentStats } = await supabase
    .from('job_source_stats')
    .select('total_jobs_fetched, jobs_fetched_today, avg_response_time_ms')
    .eq('source_id', sourceId)
    .single();

  if (currentStats) {
    const jobsReturned = body.jobs_returned || 0;
    const newTotalJobs = currentStats.total_jobs_fetched + jobsReturned;
    const newTodayJobs = currentStats.jobs_fetched_today + jobsReturned;
    const newAvgResponseTime =
      body.response_time_ms || currentStats.avg_response_time_ms;

    const { error: statsError } = await supabase
      .from('job_source_stats')
      .update({
        last_fetch_at: new Date().toISOString(),
        total_jobs_fetched: newTotalJobs,
        jobs_fetched_today: newTodayJobs,
        avg_response_time_ms: newAvgResponseTime,
        updated_at: new Date().toISOString(),
      })
      .eq('source_id', sourceId);

    if (statsError) {
      logger.error('Error updating source stats:', statsError);
    }
  }
}

async function updateErrorStats(
  // biome-ignore lint/suspicious/noExplicitAny: Supabase client return types are complex
  supabase: any,
  sourceId: string,
  body: SourceHealthLog
) {
  // Get current error count first
  const { data: currentStats } = await supabase
    .from('job_source_stats')
    .select('error_count_24h')
    .eq('source_id', sourceId)
    .single();

  if (currentStats) {
    const { error: statsError } = await supabase
      .from('job_source_stats')
      .update({
        error_count_24h: currentStats.error_count_24h + 1,
        last_error: body.error_message,
        last_error_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      })
      .eq('source_id', sourceId);

    if (statsError) {
      logger.error('Error updating source error stats:', statsError);
    }
  }
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const supabase = createClient();

    const { data: source, error } = await supabase
      .from('job_sources')
      .select(
        `
        *,
        job_source_stats (*)
      `
      )
      .eq('id', id)
      .single();

    if (error || !source) {
      return NextResponse.json({ error: 'Source not found' }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      source: {
        ...source,
        stats: source.job_source_stats?.[0] || null,
      },
    });
  } catch (error) {
    logger.error('Error fetching source:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = (await request.json()) as SourceHealthLog;
    const supabase = createClient();

    // Log the health check
    await logHealthCheck(supabase, id, body);

    // Update stats based on the health check
    if (body.status === 'success') {
      await updateSuccessStats(supabase, id, body);
    } else {
      await updateErrorStats(supabase, id, body);
    }

    return NextResponse.json({
      success: true,
      message: 'Health check logged successfully',
    });
  } catch (error) {
    logger.error('Error in source health logging:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
