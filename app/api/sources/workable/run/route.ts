import { type NextRequest, NextResponse } from 'next/server';

export async function POST(_request: NextRequest): Promise<NextResponse> {
  try {
    const apifyToken = process.env.APIFY_TOKEN;
    const actorId = process.env.WORKABLE_ACTOR_ID;

    if (!apifyToken) {
      return NextResponse.json(
        { error: 'Apify token not configured' },
        { status: 500 }
      );
    }

    if (!actorId) {
      return NextResponse.json(
        { error: 'Workable actor ID not configured' },
        { status: 500 }
      );
    }

    // First, get the schedule configuration to use the same input
    let scheduleInput = {};
    try {
      const schedulesResponse = await fetch(
        'https://api.apify.com/v2/schedules',
        {
          headers: {
            Authorization: `Bearer ${apifyToken}`,
            'Content-Type': 'application/json',
          },
        }
      );

      if (schedulesResponse.ok) {
        const schedulesData = await schedulesResponse.json();
        type ScheduleAction = {
          actorId: string;
          input?: Record<string, unknown>;
        };
        const actorSchedule = schedulesData.data?.items?.find(
          (schedule: { actions?: ScheduleAction[] }) =>
            schedule.actions?.some(
              (actionItem: ScheduleAction) => actionItem.actorId === actorId
            )
        );

        if (actorSchedule) {
          const action = actorSchedule.actions.find(
            (actionItem: ScheduleAction) => actionItem.actorId === actorId
          );
          scheduleInput = action.input || {};
        }
      }
    } catch (_error) {
      // Continue with empty input if schedule fetch fails
      // Note: Error logged but not propagated to maintain functionality
    }

    // Trigger immediate run via Apify API using schedule input
    const response = await fetch(
      `https://api.apify.com/v2/acts/${actorId}/runs`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scheduleInput),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        {
          success: false,
          error: `Failed to trigger run: ${response.status} ${errorText}`,
        },
        { status: response.status }
      );
    }

    const runData = await response.json();

    return NextResponse.json({
      success: true,
      message: 'Workable run triggered successfully',
      data: {
        runId: runData.data?.id,
        status: runData.data?.status,
        apifyUrl: `https://console.apify.com/actors/${actorId}/runs/${runData.data?.id}`,
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
