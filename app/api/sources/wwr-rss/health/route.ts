import { performHealthCheck } from '@/lib/health-check-utils';

export const dynamic = 'force-dynamic';

/**
 * WeWorkRemotely Health Check Endpoint
 *
 * Tests the connection to WeWorkRemotely via Apify actor
 * Returns health status, response time, and actor information
 */
export function GET() {
  return performHealthCheck({
    source: 'wwr-rss',
    actorIdEnvVar: 'WWR_ACTOR_ID',
    actorDisplayName: 'WeWorkRemotely',
  });
}
