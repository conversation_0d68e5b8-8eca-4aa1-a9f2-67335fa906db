import { type NextRequest, NextResponse } from 'next/server';

type ScheduleAction = {
  actorId: string;
  input?: Record<string, unknown>;
};

async function fetchScheduleInput(
  apifyToken: string,
  actorId: string
): Promise<Record<string, unknown>> {
  try {
    const schedulesResponse = await fetch(
      'https://api.apify.com/v2/schedules',
      {
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
      }
    );

    if (schedulesResponse.ok) {
      const schedulesData = await schedulesResponse.json();
      const actorSchedule = schedulesData.data?.items?.find(
        (schedule: { actions?: ScheduleAction[] }) =>
          schedule.actions?.some(
            (actionItem: ScheduleAction) => actionItem.actorId === actorId
          )
      );

      if (actorSchedule) {
        const action = actorSchedule.actions.find(
          (actionItem: ScheduleAction) => actionItem.actorId === actorId
        );
        return action.input || {};
      }
    }
  } catch (_error) {
    // Continue with empty input if schedule fetch fails
  }
  return {};
}

function ensureApiKey(scheduleInput: Record<string, unknown>): void {
  if (!scheduleInput.api_key) {
    const jobDataApiKey = process.env.JOBDATA_API_KEY;
    if (jobDataApiKey && jobDataApiKey !== 'YOUR_API_KEY') {
      scheduleInput.api_key = jobDataApiKey;
    } else {
      scheduleInput.api_key = 'YOUR_API_KEY';
    }
  }
}

export async function POST(_request: NextRequest): Promise<NextResponse> {
  try {
    const apifyToken = process.env.APIFY_TOKEN;
    const actorId = process.env.JOBDATAAPI_ACTOR_ID;

    if (!apifyToken) {
      return NextResponse.json(
        { error: 'Apify token not configured' },
        { status: 500 }
      );
    }

    if (!actorId) {
      return NextResponse.json(
        { error: 'JobDataAPI actor ID not configured' },
        { status: 500 }
      );
    }

    // Get the schedule configuration and ensure API key is present
    const scheduleInput = await fetchScheduleInput(apifyToken, actorId);
    ensureApiKey(scheduleInput);

    // Trigger immediate run via Apify API using schedule input
    const response = await fetch(
      `https://api.apify.com/v2/acts/${actorId}/runs`,
      {
        method: 'POST',
        headers: {
          Authorization: `Bearer ${apifyToken}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(scheduleInput),
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      return NextResponse.json(
        {
          success: false,
          error: `Failed to trigger run: ${response.status} ${errorText}`,
        },
        { status: response.status }
      );
    }

    const runData = await response.json();

    return NextResponse.json({
      success: true,
      message: 'JobDataAPI run triggered successfully',
      data: {
        runId: runData.data?.id,
        status: runData.data?.status,
        apifyUrl: `https://console.apify.com/actors/${actorId}/runs/${runData.data?.id}`,
      },
    });
  } catch (error) {
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
