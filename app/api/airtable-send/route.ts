import { NextResponse } from 'next/server';
import {
  mapCareerLevelToAirtableFormat,
  mapCurrencyToAirtableFormat,
  mapLanguagesToAirtableFormat,
} from '@/lib/airtable-mappings';
import { formatDateForAirtable } from '@/lib/date-utils';
import type { JobData } from '@/lib/types';
import { logger } from '@/lib/utils';

/**
 * Map basic job information to Airtable fields
 */
function mapBasicJobFields(
  jobData: Partial<JobData>
): Record<string, string | number | boolean> {
  const fields: Record<string, string | number | boolean> = {};

  if (jobData.title) {
    fields.title = jobData.title;
  }
  if (jobData.company) {
    fields.company = jobData.company;
  }
  if (jobData.type) {
    fields.type = jobData.type;
  }
  if (jobData.description) {
    fields.description = jobData.description;
  }
  if (jobData.status) {
    fields.status = jobData.status;
  }

  return fields;
}

/**
 * Map location information to Airtable fields
 */
function mapLocationFields(
  jobData: Partial<JobData>
): Record<string, string | number | boolean> {
  const fields: Record<string, string | number | boolean> = {};

  if (jobData.workplace_type) {
    fields.workplace_type = jobData.workplace_type;
  }
  if (jobData.workplace_country) {
    fields.workplace_country = jobData.workplace_country;
  }
  if (jobData.workplace_city) {
    fields.workplace_city = jobData.workplace_city;
  }
  if (jobData.remote_region) {
    fields.remote_region = jobData.remote_region;
  }

  return fields;
}

/**
 * Map salary information to Airtable fields
 */
function mapSalaryFields(
  jobData: Partial<JobData>
): Record<string, string | number | boolean> {
  const fields: Record<string, string | number | boolean> = {};

  if (jobData.salary_min !== null && jobData.salary_min !== undefined) {
    fields.salary_min = jobData.salary_min;
  }
  if (jobData.salary_max !== null && jobData.salary_max !== undefined) {
    fields.salary_max = jobData.salary_max;
  }
  if (jobData.salary_currency) {
    // Convert currency to Airtable format
    fields.salary_currency = mapCurrencyToAirtableFormat(
      jobData.salary_currency
    );
  }
  if (jobData.salary_unit) {
    fields.salary_unit = jobData.salary_unit;
  }

  return fields;
}

/**
 * Map career and application information to Airtable fields
 */
function mapCareerAndApplicationFields(
  jobData: Partial<JobData>
): Record<string, string | number | boolean | string[]> {
  const fields: Record<string, string | number | boolean | string[]> = {};

  // Career information - Convert to Airtable format
  if (jobData.career_level && jobData.career_level.length > 0) {
    fields.career_level = mapCareerLevelToAirtableFormat(jobData.career_level);
  }
  if (jobData.skills) {
    fields.skills = jobData.skills;
  }
  if (jobData.qualifications) {
    fields.qualifications = jobData.qualifications;
  }
  if (jobData.experience_requirements) {
    fields.experience_requirements = jobData.experience_requirements;
  }
  if (jobData.education_requirements) {
    fields.education_requirements = jobData.education_requirements;
  }

  // Application information
  if (jobData.apply_url) {
    fields.apply_url = jobData.apply_url;
  }
  if (jobData.apply_method) {
    fields.apply_method = jobData.apply_method;
  }
  if (jobData.application_requirements) {
    fields.application_requirements = jobData.application_requirements;
  }

  return fields;
}

/**
 * Map date fields to Airtable format
 */
function mapDateFields(jobData: Partial<JobData>): Record<string, string> {
  const fields: Record<string, string> = {};

  // Use centralized date formatting
  const postedDate = formatDateForAirtable(jobData.posted_date);
  if (postedDate) {
    fields.posted_date = postedDate;
  }

  const validThrough = formatDateForAirtable(jobData.valid_through);
  if (validThrough) {
    fields.valid_through = validThrough;
  }

  return fields;
}

/**
 * Map source information to Airtable fields
 */
function mapSourceFields(jobData: Partial<JobData>): Record<string, string> {
  const fields: Record<string, string> = {};

  if (jobData.job_source_name) {
    fields.job_source_name = jobData.job_source_name;
  }
  if (jobData.job_identifier) {
    fields.job_identifier = jobData.job_identifier;
  }
  if (jobData.sourcedAt) {
    fields.sourced_at = jobData.sourcedAt;
  }
  if (jobData.sourceUrl) {
    fields.source_url = jobData.sourceUrl;
  }

  return fields;
}

/**
 * Map additional metadata to Airtable fields
 */
function mapMetadataFields(
  jobData: Partial<JobData>
): Record<string, string | number | boolean | string[]> {
  const fields: Record<string, string | number | boolean | string[]> = {};

  if (jobData.benefits) {
    fields.benefits = jobData.benefits;
  }
  if (jobData.responsibilities) {
    fields.responsibilities = jobData.responsibilities;
  }
  if (jobData.department) {
    fields.department = jobData.department;
  }
  if (
    jobData.travel_required !== null &&
    jobData.travel_required !== undefined
  ) {
    fields.travel_required = jobData.travel_required;
  }
  if (jobData.visa_sponsorship) {
    fields.visa_sponsorship = jobData.visa_sponsorship;
  }
  // Convert languages to Airtable format
  if (jobData.languages && jobData.languages.length > 0) {
    fields.languages = mapLanguagesToAirtableFormat(jobData.languages);
  }

  return {
    ...fields,
    ...mapDateFields(jobData),
    ...mapSourceFields(jobData),
  };
}

/**
 * Map all job data to Airtable fields format
 */
function mapJobDataToAirtableFields(
  jobData: Partial<JobData>
): Record<string, string | number | boolean | string[]> {
  return {
    ...mapBasicJobFields(jobData),
    ...mapLocationFields(jobData),
    ...mapSalaryFields(jobData),
    ...mapCareerAndApplicationFields(jobData),
    ...mapMetadataFields(jobData),
  };
}

// Removed duplicate mapping functions - now using centralized ones from @/lib/airtable-mappings

// Type for airtable configuration
interface AirtableConfig {
  pat?: string;
  baseId?: string;
  tableName?: string;
}

// Helper function to validate airtable configuration
function validateAirtableConfiguration(
  airtableConfig: AirtableConfig
): { pat: string; baseId: string; tableName: string } | NextResponse {
  const pat = airtableConfig?.pat;
  const baseId = airtableConfig?.baseId;
  const tableName = airtableConfig?.tableName;

  if (!(pat && baseId && tableName)) {
    return NextResponse.json(
      {
        error:
          'Airtable not configured. Please provide complete airtableConfig with pat, baseId, and tableName.',
      },
      { status: 400 }
    );
  }

  return { pat, baseId, tableName };
}

// Helper function to send data to Airtable API
function sendToAirtableAPI(
  baseId: string,
  tableName: string,
  pat: string,
  fields: Record<string, unknown>
): Promise<Response> {
  return fetch(
    `https://api.airtable.com/v0/${baseId}/${encodeURIComponent(tableName)}`,
    {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${pat}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        records: [{ fields }],
      }),
    }
  );
}

// Helper function to handle Airtable API errors
async function handleAirtableError(
  response: Response,
  fields: Record<string, unknown>
): Promise<NextResponse> {
  const errorData = await response.text();
  logger.error('Airtable API Error Details:', {
    status: response.status,
    statusText: response.statusText,
    errorBody: errorData,
    sentFields: fields,
  });

  try {
    const parsedError = JSON.parse(errorData);
    return NextResponse.json(
      {
        error: 'Failed to send data to Airtable',
        details: parsedError,
        debug: {
          sentFields: Object.keys(fields),
          problematicValues: fields,
        },
      },
      { status: response.status }
    );
  } catch {
    return NextResponse.json(
      {
        error: 'Failed to send data to Airtable',
        rawError: errorData,
        sentFields: fields,
      },
      { status: response.status }
    );
  }
}

// Helper function to log successful sync
function logSuccessfulSync(
  jobData: Partial<JobData> | undefined,
  airtableRecordId: string | null,
  baseId: string,
  tableName: string
): void {
  logger.pipeline({
    step: 'SYNCED',
    source: 'airtable',
    jobCount: 1,
    success: true,
    metadata: {
      recordId: airtableRecordId,
      title: jobData?.title || 'Unknown',
      company: jobData?.company || 'Unknown',
      baseId: baseId || 'Unknown',
      tableName: tableName || 'Unknown',
    },
  });
}

// Helper function to log failed sync
function logFailedSync(
  jobData: Partial<JobData> | undefined,
  error: unknown,
  baseId: string | undefined,
  tableName: string | undefined
): void {
  logger.pipeline({
    step: 'SYNCED',
    source: 'airtable',
    jobCount: 1,
    success: false,
    error: error instanceof Error ? error.message : 'Unknown error',
    metadata: {
      title: jobData?.title || 'Unknown',
      company: jobData?.company || 'Unknown',
      baseId: baseId || 'Unknown',
      tableName: tableName || 'Unknown',
    },
  });
}

/**
 * POST /api/airtable-send
 * Sends job data to Airtable using provided configuration (database-only, no environment fallbacks)
 */
export async function POST(request: Request) {
  let jobData: Partial<JobData> | undefined;
  let baseId: string | undefined;
  let tableName: string | undefined;

  try {
    const requestData = await request.json();
    jobData = requestData.jobData;
    const airtableConfig = requestData.airtableConfig;

    // Validate Airtable configuration
    const configResult = validateAirtableConfiguration(airtableConfig);
    if (configResult instanceof NextResponse) {
      return configResult;
    }
    const {
      pat,
      baseId: validatedBaseId,
      tableName: validatedTableName,
    } = configResult;
    baseId = validatedBaseId;
    tableName = validatedTableName;

    // Map job data fields to Airtable-friendly format
    const fields = mapJobDataToAirtableFields(jobData || {});

    // Send to Airtable
    const response = await sendToAirtableAPI(baseId, tableName, pat, fields);

    if (!response.ok) {
      return await handleAirtableError(response, fields);
    }

    const result = await response.json();

    // Return both the Airtable response and extract the record ID for sync tracking
    const airtableRecordId = result.records?.[0]?.id || null;

    // Track successful Airtable sync
    logSuccessfulSync(jobData, airtableRecordId, baseId, tableName);

    return NextResponse.json({
      ...result,
      recordId: airtableRecordId, // Add record ID for sync tracking
    });
  } catch (_error) {
    // Track Airtable sync failure
    logFailedSync(jobData, _error, baseId, tableName);

    return NextResponse.json(
      { error: 'Failed to send data to Airtable' },
      { status: 500 }
    );
  }
}
