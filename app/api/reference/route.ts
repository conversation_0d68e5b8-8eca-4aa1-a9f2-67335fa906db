import { ApiReference } from '@scalar/nextjs-api-reference';

const config = {
  url: process.env.NEXT_PUBLIC_SPEC_URL ?? '/api/openapi.json',
  theme: 'default' as const,
  layout: 'classic' as const,
  metaData: {
    title: 'Bordfeed API Reference',
    description:
      'Interactive API documentation for Bordfeed - AI-powered job board automation platform',
  },
  // Enable dark mode support
  darkMode: true,
  // Hide the Scalar branding
  hideModels: false,
  hideFooter: true,
  // Show the download button
  showSidebar: true,
  hideTestRequestButton: false,
  hideDownloadButton: false,
  // Authentication configuration
  authentication: {
    preferredSecurityScheme: 'QStash',
    apiKey: {
      token: 'YOUR_API_KEY',
    },
  },
  // Custom CSS for better integration
  customCss: `
    .scalar-app {
      --scalar-font-family: ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    }
    .scalar-app .scalar-api-reference__header {
      border-bottom: 1px solid hsl(var(--border));
    }
  `,
};

export const GET = ApiReference(config);
