import { NextResponse } from 'next/server';
import { SLACK_CONFIG } from '@/lib/constants';
import {
  getNotificationSettings,
  invalidateNotificationSettingsCache,
  type NotificationSettings,
} from '@/lib/notification-settings';
import { createServiceRoleClient } from '@/lib/supabase';

export async function GET() {
  const settings = await getNotificationSettings();
  return NextResponse.json({
    configured: <PERSON><PERSON><PERSON>(SLACK_CONFIG.webhookUrl),
    flags: {
      enableCritical: settings.enableCritical,
      enableAlert: settings.enableAlert,
      enableInfo: settings.enableInfo,
      enablePipeline: settings.enablePipeline,
      pipelineOnlyFailures: settings.pipelineOnlyFailures,
      pipelineSteps: settings.pipelineSteps,
    },
    frequency: {
      healthCadence: settings.healthCadence,
      jobsScrapedOnComplete: settings.jobsScrapedOnComplete,
      jobsProcessedOnComplete: settings.jobsProcessedOnComplete,
      jobsProcessedDaily: settings.jobsProcessedDaily,
      jobsProcessedDailyTime: settings.jobsProcessedDailyTime,
    },
  });
}

export async function PUT(request: Request) {
  const body = (await request.json()) as Partial<NotificationSettings>;
  const supabase = createServiceRoleClient();
  const { error } = await supabase
    .from('notification_settings')
    .upsert(
      { id: 'global', settings: body, updated_at: new Date().toISOString() },
      { onConflict: 'id' }
    );
  if (error) {
    return NextResponse.json(
      { success: false, error: error.message },
      { status: 500 }
    );
  }
  invalidateNotificationSettingsCache();
  const settings = await getNotificationSettings();
  return NextResponse.json({ success: true, settings });
}
