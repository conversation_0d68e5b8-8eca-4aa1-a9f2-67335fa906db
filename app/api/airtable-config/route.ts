import { NextResponse } from 'next/server';
import { logger } from '@/lib/utils';

/**
 * GET /api/airtable-config
 * This endpoint is deprecated as it relied on environment variables.
 * Use board-specific configuration instead.
 */
export function GET() {
  try {
    return NextResponse.json(
      {
        error:
          'This endpoint is deprecated. Airtable configuration is now board-specific.',
        help: 'Use the job boards API to manage Airtable configurations per board.',
        hasConfig: false,
        baseId: null,
        tableName: null,
        hasPat: false,
      },
      { status: 410 }
    );
  } catch (error) {
    logger.error('Airtable config error:', error);
    return NextResponse.json(
      { error: 'Failed to load Airtable configuration' },
      { status: 500 }
    );
  }
}
