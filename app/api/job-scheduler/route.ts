import { type NextRequest, NextResponse } from 'next/server';
import { runSimpleJobScheduler } from '@/lib/simple-job-scheduler';
import { logger } from '@/lib/utils';

// Protect this endpoint with a secret
const SCHEDULER_SECRET = process.env.SCHEDULER_SECRET;

export async function POST(req: NextRequest) {
  try {
    // Check authorization
    const authHeader = req.headers.get('authorization');
    const providedSecret = authHeader?.replace('Bearer ', '');

    if (!SCHEDULER_SECRET || providedSecret !== SCHEDULER_SECRET) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }

    // Optional: Check if request is from allowed cron service
    const userAgent = req.headers.get('user-agent');
    logger.info(`Job scheduler triggered by: ${userAgent}`);

    // Run the scheduler
    const results = await runSimpleJobScheduler();

    logger.info('Job scheduler completed', results);

    // Aggregate results
    const totalPosted = results.reduce((sum, r) => sum + r.jobsPosted, 0);
    const totalFailed = results.reduce((sum, r) => sum + r.errors.length, 0);
    const success = results.length > 0 && totalFailed === 0;

    return NextResponse.json({
      success,
      totalPosted,
      totalFailed,
      boardsProcessed: results.length,
      results,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    logger.error('Job scheduler error:', error);
    return NextResponse.json(
      {
        error: 'Internal server error',
        message: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

// GET endpoint for manual testing/status check
export function GET(req: NextRequest) {
  const authHeader = req.headers.get('authorization');
  const providedSecret = authHeader?.replace('Bearer ', '');

  if (!SCHEDULER_SECRET || providedSecret !== SCHEDULER_SECRET) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  return NextResponse.json({
    status: 'ready',
    message: 'Job scheduler is ready. Send POST request to run.',
    configuration: {
      secretConfigured: !!SCHEDULER_SECRET,
      endpoint: '/api/job-scheduler',
      method: 'POST',
      headers: {
        Authorization: 'Bearer YOUR_SCHEDULER_SECRET',
        'Content-Type': 'application/json',
      },
    },
  });
}
