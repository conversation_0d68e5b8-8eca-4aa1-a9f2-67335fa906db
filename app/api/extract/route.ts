import { type NextRequest, NextResponse } from 'next/server';
import { handleApiError, validationErrorResponse } from '@/lib/api-utils';
import { DEBUG_MODE } from '@/lib/constants';
import { extractJob } from '@/lib/extract-job';
import type { DatabaseJob, StorageMetadata } from '@/lib/storage';
import { saveProcessedJob, updateJobWithAIProcessing } from '@/lib/storage';
import type { JobData } from '@/lib/types';
import { logger } from '@/lib/utils';
import { ExtractJobRequestSchema } from '@/lib/validation';

/**
 * Set basic job information with defaults
 */
function setBasicJobInfo(job: Partial<JobData>, sourceUrl: string) {
  return {
    sourcedAt: new Date().toISOString(),
    sourceUrl: sourceUrl || '',
    title: job.title || 'Untitled Job',
    description: job.description || '',
    job_source_name: job.job_source_name || null,
    // Do not include source_name - preserve existing value in database
    source_name: null, // Required by JobData type, but will be excluded from update
    job_identifier: job.job_identifier || null,
    featured: job.featured ?? null,
    status: job.status || 'active',
  };
}

/**
 * Set company and application details
 */
function setCompanyInfo(job: Partial<JobData>) {
  return {
    company: job.company ?? null,
    type: job.type ?? null,
    apply_url: job.apply_url ?? null,
    apply_method: job.apply_method ?? null,
    posted_date: job.posted_date ?? null,
  };
}

/**
 * Set salary information
 */
function setSalaryInfo(job: Partial<JobData>) {
  return {
    salary_min: job.salary_min ?? null,
    salary_max: job.salary_max ?? null,
    salary_currency: job.salary_currency ?? null,
    salary_unit: job.salary_unit ?? null,
  };
}

/**
 * Set workplace information
 */
function setWorkplaceInfo(job: Partial<JobData>) {
  return {
    workplace_type: job.workplace_type ?? null,
    remote_region: job.remote_region ?? null,
    timezone_requirements: job.timezone_requirements ?? null,
    workplace_city: job.workplace_city ?? null,
    workplace_country: job.workplace_country ?? null,
  };
}

/**
 * Set job requirements and content
 */
function setJobRequirements(job: Partial<JobData>) {
  return {
    benefits: job.benefits ?? null,
    application_requirements: job.application_requirements ?? null,
    skills: job.skills ?? null,
    qualifications: job.qualifications ?? null,
    education_requirements: job.education_requirements ?? null,
    experience_requirements: job.experience_requirements ?? null,
    responsibilities: job.responsibilities ?? null,
  };
}

/**
 * Set categorization and metadata
 */
function setCategorization(job: Partial<JobData>) {
  return {
    industry: job.industry ?? null,
    occupational_category: job.occupational_category ?? null,
    career_level: job.career_level ?? null,
    valid_through: job.valid_through ?? null,
    visa_sponsorship: job.visa_sponsorship ?? null,
    languages: job.languages ?? null,
    department: job.department ?? null,
    travel_required: job.travel_required ?? null,
  };
}

/**
 * Normalize job data to ensure required fields are present
 */
function normalizeJobData(job: Partial<JobData>, sourceUrl: string): JobData {
  return {
    ...setBasicJobInfo(job, sourceUrl),
    ...setCompanyInfo(job),
    ...setSalaryInfo(job),
    ...setWorkplaceInfo(job),
    ...setJobRequirements(job),
    ...setCategorization(job),
  };
}

/**
 * Process and save job data
 */
async function processAndSaveJob(
  jobId: string | undefined,
  fullJob: JobData,
  metadata: StorageMetadata
): Promise<SaveResult> {
  if (jobId) {
    const saveResult = await updateJobWithAIProcessing(
      jobId,
      fullJob,
      metadata
    );
    if (!saveResult.success) {
      throw new Error(
        `Failed to update job ${jobId}: ${saveResult.error || 'Unknown error'}`
      );
    }
    // Normalize the update result to include id from data
    return {
      success: saveResult.success,
      id: saveResult.data?.id || jobId,
      data: saveResult.data,
      updated: true,
    };
  }

  const saveResult = await saveProcessedJob(fullJob, metadata);
  if (!saveResult.success) {
    throw new Error(
      `Failed to create job: ${saveResult.error || 'Unknown error'}`
    );
  }
  // saveProcessedJob already returns the correct structure
  return {
    success: saveResult.success,
    id: saveResult.id,
    data: saveResult.data,
    updated: false,
  };
}

interface SaveResult {
  success: boolean;
  id?: string;
  data?: DatabaseJob;
  updated?: boolean;
  error?: string;
}

interface RequestBody {
  jobId?: string;
  sourceUrl?: string;
  content: string;
}

/**
 * Log debug information if enabled
 */
function logDebugInfo(
  body: RequestBody,
  saveResult: SaveResult,
  jobId?: string
) {
  if (!DEBUG_MODE) {
    return;
  }

  logger.log(
    `Extract API - Request size: ${JSON.stringify(body).length} chars`
  );
  logger.log(`Content length: ${body.content?.length || 0} chars`);

  if (saveResult.success) {
    const action = jobId ? 'Updated existing job' : 'Created new job';
    logger.log(`💾 ${action} in Supabase with ID: ${saveResult.id}`);
  }
}

export async function POST(req: NextRequest) {
  try {
    const body = await req.json();

    const validationResult = ExtractJobRequestSchema.safeParse(body);
    if (!validationResult.success) {
      return validationErrorResponse(validationResult.error);
    }

    const { jobId, sourceUrl, content } = validationResult.data;

    const { job, metadata } = await extractJob({
      sourcedAt: new Date().toISOString(),
      sourceUrl: sourceUrl || '',
      content,
    });

    const fullJob = normalizeJobData(job as Partial<JobData>, sourceUrl || '');
    const saveResult = await processAndSaveJob(jobId, fullJob, metadata);

    logDebugInfo(body, saveResult, jobId);

    return NextResponse.json({
      job: fullJob,
      metadata,
      jobId: saveResult.id,
      updated: Boolean(jobId),
    });
  } catch (error) {
    return handleApiError(error, 'Job extraction');
  }
}
