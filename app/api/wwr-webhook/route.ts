import { type NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { createHealthCheckResponse } from '@/lib/api-utils';
import { insertJobsToDatabase } from '@/lib/database-utils';
import { mapWWRFields, validateJob } from '@/lib/job-validation';
import { logger } from '@/lib/logger';
import { triggerJobProcessing } from '@/lib/workflow-client';

/**
 * WeWorkRemotely Webhook Handler - Simplified Architecture
 *
 * Database-only deduplication using external_id uniqueness constraint
 * Fast & simple flow:
 * 1. Receive webhook from Apify
 * 2. Fetch jobs from dataset
 * 3. Map minimal fields & validate
 * 4. Insert unique jobs to Supabase (database deduplication)
 * 5. Return immediately (< 200ms)
 *
 * Processing happens async via Upstash Workflow
 */

// Apify webhook schema
const ApifyWebhookSchema = z
  .object({
    eventType: z.string(),
    eventData: z.object({
      actorRunId: z.string(),
    }),
    resource: z.object({
      id: z.string(),
      status: z.string(),
      defaultDatasetId: z.string(),
    }),
  })
  .passthrough();

// WeWorkRemotely job structure from Apify
interface WWRJob {
  id: string;
  title: string;
  content: string;
  source_type: string;
  source_name: string;
  raw_data: {
    rss_item: {
      title: string;
      description: string;
      link: string;
      pubDate: string;
      category: string;
    };
    source_url: string;
    fetched_at: string;
  };
  [key: string]: unknown;
}

/**
 * Fetches jobs from Apify dataset
 */
async function fetchJobsFromDataset(datasetId: string): Promise<WWRJob[]> {
  const apifyToken = process.env.APIFY_TOKEN;
  if (!apifyToken) {
    throw new Error('APIFY_TOKEN not configured');
  }

  const datasetUrl = `https://api.apify.com/v2/datasets/${datasetId}/items?format=json&clean=true`;
  const response = await fetch(datasetUrl, {
    headers: { Authorization: `Bearer ${apifyToken}` },
  });

  if (!response.ok) {
    throw new Error(`Failed to fetch dataset: ${response.status}`);
  }

  return await response.json();
}

/**
 * Processes and validates jobs for database insertion
 */
function processJobsForInsertion(jobs: WWRJob[]): {
  jobsToInsert: Record<string, unknown>[];
  skippedCount: number;
} {
  const jobsToInsert: Record<string, unknown>[] = [];
  let skippedCount = 0;

  for (const job of jobs) {
    const mappedJob = mapWWRFields(job);
    const validation = validateJob(mappedJob, 'wwr');

    if (!validation.isValid) {
      logger.warn(
        `Skipping invalid job: ${validation.missingFields.join(', ')}`,
        {
          jobId: job.id,
          title: job.title,
        }
      );
      skippedCount++;
      continue;
    }

    const dbJob = {
      ...mappedJob,
      external_id: validation.externalId,
      raw_sourced_job_data: job, // Store complete original job structure
      source_type: 'wwr_rss',
      source_name: 'WeWorkRemotely',
      processing_status: 'pending', // Key for database-as-queue
      sourced_at: new Date().toISOString(),
    };

    jobsToInsert.push(dbJob);
  }

  return { jobsToInsert, skippedCount };
}

// insertJobsToDatabase function now imported from @/lib/database-utils

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    logger.info('🔔 WeWorkRemotely webhook received');

    // Check for Apify webhook header (optional)
    const apifyHeader = request.headers.get('X-Apify-Webhook');
    if (!apifyHeader) {
      logger.warn('Missing X-Apify-Webhook header');
    }

    // Parse webhook payload
    const body = await request.json();
    const webhook = ApifyWebhookSchema.parse(body);

    logger.info('Processing WeWorkRemotely actor run', {
      runId: webhook.eventData.actorRunId,
      datasetId: webhook.resource.defaultDatasetId,
      actorId: webhook.resource.id,
    });

    // Only process successful runs
    if (webhook.eventType !== 'ACTOR.RUN.SUCCEEDED') {
      return NextResponse.json({
        message: 'Ignoring non-success event',
        eventType: webhook.eventType,
      });
    }

    const runId = webhook.resource.id;
    const datasetId = webhook.resource.defaultDatasetId;

    // Fetch jobs from Apify dataset
    const jobs = await fetchJobsFromDataset(datasetId);

    if (!Array.isArray(jobs) || jobs.length === 0) {
      logger.info('No jobs found in dataset', { datasetId });
      return NextResponse.json({
        success: true,
        message: 'No jobs to process',
        jobsReceived: 0,
        jobsSaved: 0,
        jobsSkipped: 0,
        processingTime: `${Date.now() - startTime}ms`,
      });
    }

    logger.info(`📥 Received ${jobs.length} jobs from WeWorkRemotely`);

    // Process and validate jobs
    const { jobsToInsert, skippedCount } = processJobsForInsertion(jobs);

    // Insert jobs to database
    const savedCount = await insertJobsToDatabase(jobsToInsert);

    const processingTime = Date.now() - startTime;

    logger.info('✅ WeWorkRemotely webhook completed', {
      runId,
      jobsReceived: jobs.length,
      jobsSaved: savedCount,
      jobsSkipped: skippedCount,
      processingTime: `${processingTime}ms`,
    });

    // Track pipeline metrics
    logger.pipeline({
      step: 'STORED',
      source: 'wwr',
      jobCount: savedCount,
      success: true,
      batchId: runId,
      duration: processingTime,
      metadata: {
        apifyRunId: runId,
        apifyDatasetId: datasetId,
        jobsSkipped: skippedCount,
      },
    });

    // Trigger workflow processing for saved jobs
    let workflowMessageId: string | undefined;
    if (savedCount > 0) {
      try {
        workflowMessageId = await triggerJobProcessing(
          Math.min(savedCount, 10),
          'wwr'
        );
        logger.info('✅ Triggered workflow processing', {
          workflowMessageId,
          jobCount: savedCount,
        });
      } catch (workflowError) {
        logger.error('❌ Failed to trigger workflow processing', {
          workflowError,
        });
        // Don't fail the webhook - jobs are saved, processing can be triggered manually
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Jobs saved successfully',
      runId,
      jobsReceived: jobs.length,
      jobsSaved: savedCount,
      jobsSkipped: skippedCount,
      processingTime: `${processingTime}ms`,
      workflowTriggered: !!workflowMessageId,
      workflowMessageId,
    });
  } catch (error) {
    logger.error('WeWorkRemotely webhook failed', { error });

    // Always return 200 to Apify to prevent retries
    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Webhook acknowledged but processing failed',
        processingTime: `${Date.now() - startTime}ms`,
      },
      { status: 200 }
    );
  }
}

// Health check endpoint
export function GET() {
  return createHealthCheckResponse({
    service: 'WeWorkRemotely Webhook',
    architecture: 'Database-Only Deduplication',
    features: [
      'Direct Supabase insertion',
      'Database-level deduplication',
      'Fast response (< 200ms)',
      'Async AI processing',
    ],
  });
}
