import { NextResponse } from 'next/server';
import { createClient } from '../../../../lib/supabase';

const supabase = createClient();

export async function GET() {
  try {
    const { data, error } = await supabase.from('jobs').select('*');
    if (error) {
      throw error;
    }

    return new NextResponse(JSON.stringify(data), {
      headers: {
        'Content-Type': 'application/json',
        'Content-Disposition': `attachment; filename="jobs-backup-${new Date()
          .toISOString()
          .slice(0, 10)}.json"`,
      },
    });
  } catch (err) {
    return NextResponse.json(
      {
        error: 'Failed to export jobs backup',
        details: err instanceof Error ? err.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
