import { type NextRequest, NextResponse } from 'next/server';
import { createServerClient } from '@/lib/supabase';

export const dynamic = 'force-dynamic';

interface MonitorLog {
  id: string;
  job_id: string | null;
  previous_status: string | null;
  new_status: string | null;
  checked_at: string | null;
  duration_millis: number | null;
  cost_input_tokens: number | null;
  cost_output_tokens: number | null;
  total_tokens: number | null;
  model: string | null;
  head_status: number | null;
  head_ok: boolean | null;
  decision_layer: string | null;
}

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: jobId } = await params;

    if (!jobId) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    const supabase = await createServerClient();

    // Fetch monitoring logs for this specific job
    const { data: logs, error } = await supabase
      .from('job_monitor_logs')
      .select('*')
      .eq('job_id', jobId)
      .order('checked_at', { ascending: false })
      .limit(50); // Limit to last 50 monitoring attempts

    if (error) {
      return NextResponse.json(
        { error: 'Failed to fetch monitoring logs' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      logs: logs as MonitorLog[],
      count: logs?.length || 0,
    });
  } catch (_error) {
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
