import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../lib/supabase';

// Supabase client (service role on the server)
const supabase = createClient();

export async function GET(
  _request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    const { data: job, error } = await supabase
      .from('jobs')
      .select('*')
      .eq('id', id)
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 });
      }
      throw error;
    }

    return NextResponse.json({
      success: true,
      job,
    });
  } catch (err) {
    return NextResponse.json(
      {
        error: 'Failed to fetch job',
        details: err instanceof Error ? err.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }

    const body = await request.json();

    // Remove read-only fields that shouldn't be updated
    const {
      id: _id,
      created_at: _created_at,
      updated_at: _updated_at,
      ...updateData
    } = body;

    // Update the job with the provided data
    const { data: updatedJob, error } = await supabase
      .from('jobs')
      .update({
        ...updateData,
        updated_at: new Date().toISOString(),
      })
      .eq('id', id)
      .select()
      .single();

    if (error) {
      if (error.code === 'PGRST116') {
        return NextResponse.json({ error: 'Job not found' }, { status: 404 });
      }
      throw error;
    }

    return NextResponse.json({
      success: true,
      job: updatedJob,
    });
  } catch (err) {
    return NextResponse.json(
      {
        error: 'Failed to update job',
        details: err instanceof Error ? err.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    if (!id) {
      return NextResponse.json(
        { error: 'Job ID is required' },
        { status: 400 }
      );
    }
    const body = await request.json();
    if (body.action === 'updateStatus') {
      const { status } = body as { status?: string };
      if (!status) {
        return NextResponse.json(
          { error: 'Status is required' },
          { status: 400 }
        );
      }
      const { error } = await supabase
        .from('jobs')
        .update({ status })
        .eq('id', id);
      if (error) {
        throw error;
      }
    } else {
      return NextResponse.json(
        { error: 'Unsupported action' },
        { status: 400 }
      );
    }

    return NextResponse.json({ success: true });
  } catch (err) {
    return NextResponse.json(
      {
        error: 'Failed to update job',
        details: err instanceof Error ? err.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
