import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '../../../../lib/supabase';

const supabase = createClient();

export async function GET(_req: NextRequest) {
  try {
    const mandatoryFields = ['title', 'description', 'status'];

    const { data: jobs, error } = await supabase
      .from('jobs')
      .select('id, title, description, status');

    if (error) {
      throw error;
    }

    const issues: { id: string; missingFields: string[] }[] = [];

    for (const job of jobs || []) {
      const missing: string[] = [];
      for (const field of mandatoryFields) {
        if (!job[field as keyof typeof job]) {
          missing.push(field);
        }
      }
      if (missing.length) {
        issues.push({ id: job.id, missingFields: missing });
      }
    }

    return NextResponse.json({ total: jobs?.length ?? 0, invalid: issues });
  } catch (err) {
    return NextResponse.json(
      {
        error: 'Integrity check failed',
        details: err instanceof Error ? err.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
