import { NextResponse } from 'next/server';
import type { DatabaseJob } from '../../../../lib/storage';
import { createClient } from '../../../../lib/supabase';

const supabase = createClient();

export async function POST(request: Request) {
  try {
    const { jobs } = (await request.json()) as { jobs: DatabaseJob[] };

    if (!Array.isArray(jobs)) {
      return NextResponse.json(
        { error: 'Invalid payload: jobs must be an array' },
        { status: 400 }
      );
    }

    // Upsert into jobs table using id as primary key
    const { error, data } = await supabase
      .from('jobs')
      .upsert(jobs, { onConflict: 'id' })
      .select('id');

    if (error) {
      throw error;
    }

    return NextResponse.json({ restored: data?.length ?? 0 });
  } catch (err) {
    return NextResponse.json(
      {
        error: 'Failed to restore backup',
        details: err instanceof Error ? err.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}
