import { type NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase';

export const dynamic = 'force-dynamic';

export async function GET(_req: NextRequest) {
  const supabase = createClient();
  const five_minutes_ago = new Date(Date.now() - 5 * 60 * 1000).toISOString();

  const { data, error } = await supabase
    .from('job_monitor_logs')
    .select('decision_layer')
    .gt('created_at', five_minutes_ago);

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  const counts = data.reduce(
    (
      acc: Record<string, number>,
      { decision_layer }: { decision_layer: string }
    ) => {
      acc[decision_layer] = (acc[decision_layer] || 0) + 1;
      return acc;
    },
    {} as Record<string, number>
  );

  return NextResponse.json(counts);
}
