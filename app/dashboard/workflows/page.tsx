"use client";

import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  CheckCircle,
  Clock,
  Play,
  RefreshCw,
  XCircle,
} from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";

interface WorkflowStats {
  active: number;
  completed: number;
  failed: number;
  partial_failure: number;
  total_today: number;
  jobs_processed_today: number;
}

interface WorkflowQueue {
  pending: number;
  processing: number;
}

interface WorkflowRun {
  id: string;
  workflow_type: string;
  status: "running" | "completed" | "failed" | "partial_failure";
  started_at: string;
  completed_at?: string;
  job_count: number;
  error_message?: string;
}

interface WorkflowStatus {
  stats: WorkflowStats;
  queue: WorkflowQueue;
  recent_runs: WorkflowRun[];
  timestamp: string;
}

export default function WorkflowsPage() {
  const [status, setStatus] = useState<WorkflowStatus | null>(null);
  const [loading, setLoading] = useState(true);
  const [triggering, setTriggering] = useState(false);
  const [batchSize, setBatchSize] = useState(10);
  const [source, setSource] = useState("");

  const fetchStatus = async () => {
    try {
      const response = await fetch("/api/workflows/status");
      if (!response.ok) throw new Error("Failed to fetch status");
      const data = await response.json();
      setStatus(data);
    } catch (error) {
      console.error("Failed to fetch workflow status:", error);
      toast.error("Failed to fetch workflow status");
    } finally {
      setLoading(false);
    }
  };

  const triggerWorkflow = async () => {
    setTriggering(true);
    try {
      const response = await fetch("/api/workflows/status", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          action: "trigger_processing",
          batchSize,
          source: source || undefined,
        }),
      });

      if (!response.ok) throw new Error("Failed to trigger workflow");

      const result = await response.json();
      toast.success(
        `Workflow triggered successfully! Message ID: ${result.messageId}`
      );

      // Refresh status after a short delay
      setTimeout(fetchStatus, 2000);
    } catch (error) {
      console.error("Failed to trigger workflow:", error);
      toast.error("Failed to trigger workflow");
    } finally {
      setTriggering(false);
    }
  };

  useEffect(() => {
    fetchStatus();
    // No auto-refresh - only manual refresh via button
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "running":
        return <Clock className="h-4 w-4 text-blue-500" />;
      case "completed":
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case "failed":
        return <XCircle className="h-4 w-4 text-red-500" />;
      case "partial_failure":
        return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    const variants = {
      running: "default",
      completed: "default",
      failed: "destructive",
      partial_failure: "secondary",
    } as const;

    return (
      <Badge variant={variants[status as keyof typeof variants] || "outline"}>
        {status.replace("_", " ")}
      </Badge>
    );
  };

  const formatDuration = (startedAt: string, completedAt?: string) => {
    const start = new Date(startedAt);
    const end = completedAt ? new Date(completedAt) : new Date();
    const duration = Math.round((end.getTime() - start.getTime()) / 1000);

    if (duration < 60) return `${duration}s`;
    if (duration < 3600) return `${Math.round(duration / 60)}m`;
    return `${Math.round(duration / 3600)}h`;
  };

  if (loading) {
    return (
      <div className="flex h-64 items-center justify-center">
        <RefreshCw className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (!status) {
    return (
      <div className="flex h-64 items-center justify-center">
        <p className="text-muted-foreground">Failed to load workflow status</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h1 className="font-bold text-3xl">Workflow Monitor</h1>
          <p className="text-muted-foreground">
            Real-time monitoring of Upstash Workflow execution
          </p>
        </div>
        <Button onClick={fetchStatus} size="sm" variant="outline">
          <RefreshCw className="mr-2 h-4 w-4" />
          Refresh
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 gap-4 md:grid-cols-3 lg:grid-cols-6">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-sm">Active</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl text-blue-600">
              {status.stats.active}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-sm">
              Completed Today
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl text-green-600">
              {status.stats.completed}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-sm">Failed Today</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl text-red-600">
              {status.stats.failed}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-sm">
              Partial Failures
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl text-yellow-600">
              {status.stats.partial_failure}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-sm">Pending Jobs</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl text-orange-600">
              {status.queue.pending}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="font-medium text-sm">
              Processing Jobs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="font-bold text-2xl text-purple-600">
              {status.queue.processing}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Manual Trigger */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Workflow Trigger</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
            <div>
              <Label htmlFor="batchSize">Batch Size</Label>
              <Input
                id="batchSize"
                max={50}
                min={1}
                onChange={(e) => setBatchSize(Number(e.target.value))}
                type="number"
                value={batchSize}
              />
            </div>
            <div>
              <Label htmlFor="source">Source (optional)</Label>
              <Input
                id="source"
                onChange={(e) => setSource(e.target.value)}
                placeholder="e.g., jobdata, wwr, workable"
                value={source}
              />
            </div>
            <div className="flex items-end">
              <Button
                className="w-full"
                disabled={triggering}
                onClick={triggerWorkflow}
              >
                {triggering ? (
                  <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                ) : (
                  <Play className="mr-2 h-4 w-4" />
                )}
                Trigger Workflow
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Recent Workflow Runs */}
      <Card>
        <CardHeader>
          <CardTitle>Recent Workflow Runs</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {status.recent_runs.length === 0 ? (
              <p className="py-8 text-center text-muted-foreground">
                No recent workflow runs found
              </p>
            ) : (
              status.recent_runs.map((run) => (
                <div
                  className="flex items-center justify-between rounded-lg border p-4"
                  key={run.id}
                >
                  <div className="flex items-center space-x-4">
                    {getStatusIcon(run.status)}
                    <div>
                      <div className="font-medium">{run.workflow_type}</div>
                      <div className="text-muted-foreground text-sm">
                        {run.job_count} jobs •{" "}
                        {formatDuration(run.started_at, run.completed_at)}
                      </div>
                      {run.error_message && (
                        <div className="mt-1 text-red-600 text-sm">
                          {run.error_message}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    {getStatusBadge(run.status)}
                    <div className="text-muted-foreground text-sm">
                      {new Date(run.started_at).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))
            )}
          </div>
        </CardContent>
      </Card>

      <div className="text-center text-muted-foreground text-xs">
        Last updated: {new Date(status.timestamp).toLocaleString()}
      </div>
    </div>
  );
}
