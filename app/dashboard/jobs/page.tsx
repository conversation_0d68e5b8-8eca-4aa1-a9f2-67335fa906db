import { Suspense } from 'react';
import { ExtractStats } from '@/components/extract/extract-stats';
import { JobsTable } from '@/components/jobs/jobs-table';

function JobsTableWithSuspense() {
  return (
    <Suspense
      fallback={
        <div className="flex items-center justify-center p-8">
          Loading jobs...
        </div>
      }
    >
      <JobsTable />
    </Suspense>
  );
}

export default function JobsPage() {
  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div>
        <h1 className="font-semibold text-3xl tracking-tight">Jobs</h1>
        <p className="text-muted-foreground">
          Manage and view all job postings with powerful filtering and sorting.
        </p>
      </div>

      {/* Job Statistics Cards */}
      <ExtractStats />

      <JobsTableWithSuspense />
    </div>
  );
}
