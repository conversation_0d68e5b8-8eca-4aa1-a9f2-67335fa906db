'use client';

import { ArrowLeft, Edit, Play, Save, X } from 'lucide-react';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import type React from 'react';
import { use as usePromise, useState } from 'react';
import { toast } from 'sonner';
import { JobFieldRenderer } from '@/components/job-detail/job-field-renderer';
import { JobMonitoringLog } from '@/components/monitoring/job-monitoring-log';
import { JobTimeline } from '@/components/timeline/job-timeline';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { useJobDetail } from '@/lib/hooks/use-job-detail';
import { useJobProcessing } from '@/lib/hooks/use-job-processing';
// getSourceDisplayName no longer used after header refactor
// import { getSourceDisplayName } from '@/lib/job-board-constants';
import type { DatabaseJob } from '@/lib/storage';
import {
  getJobStatusBadgeClass,
  getProcessingStatusBadgeClass,
  getProcessingStatusText,
} from '@/lib/utils/job-status-utils';

interface JobDetailPageProps {
  params: Promise<{ id: string }>;
}

/**
 * Loading state component
 */
function LoadingState() {
  return (
    <div className="min-h-screen bg-background">
      <div className="mx-auto max-w-4xl px-4 py-8">
        <div className="flex items-center justify-center py-12">
          <div className="text-muted-foreground">Loading job details...</div>
        </div>
      </div>
    </div>
  );
}

/**
 * Error state component
 */
function ErrorState({
  error,
  onRetry,
  onGoBack,
}: {
  error: string;
  onRetry: () => void;
  onGoBack: () => void;
}) {
  return (
    <div className="min-h-screen bg-background">
      <div className="mx-auto max-w-4xl px-4 py-8">
        <div className="rounded-lg bg-destructive/10 p-6">
          <div className="text-destructive">Error: {error}</div>
          <div className="mt-4 space-x-3">
            <Button
              onClick={onRetry}
              size="sm"
              type="button"
              variant="destructive"
            >
              Retry
            </Button>
            <Button
              onClick={onGoBack}
              size="sm"
              type="button"
              variant="outline"
            >
              Go Back
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Not found state component
 */
function NotFoundState({ onGoBack }: { onGoBack: () => void }) {
  return (
    <div className="min-h-screen bg-background">
      <div className="mx-auto max-w-4xl px-4 py-8">
        <div className="text-center">
          <div className="text-muted-foreground">Job not found</div>
          <div className="mt-4">
            <Button
              onClick={onGoBack}
              size="sm"
              type="button"
              variant="outline"
            >
              Go Back
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}

/**
 * Render the main job content section
 */
function JobContent({
  job,
  isEditing,
  editForm,
  saving,
  isProcessing,
  handleEditToggle,
  handleSave,
  handleInputChange,
  handleProcessNow,
}: {
  job: DatabaseJob;
  isEditing: boolean;
  editForm: Partial<DatabaseJob>;
  saving: boolean;
  isProcessing: (id: string) => boolean;
  handleEditToggle: () => void;
  handleSave: () => void;
  handleInputChange: (
    field: keyof DatabaseJob,
    value: string | boolean | null
  ) => void;
  handleProcessNow: () => void;
}) {
  const [faviconError, setFaviconError] = useState(false);

  let sourceHost: string | null = null;
  let faviconUrl: string | null = null;
  try {
    if (job.source_url) {
      const url = new URL(job.source_url);
      sourceHost = url.hostname;
      faviconUrl = `https://www.google.com/s2/favicons?domain=${sourceHost}&sz=32`;
    }
  } catch {
    // ignore invalid URLs
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="mx-auto max-w-4xl px-4 py-8">
        <div className="mb-6 flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <Button
              onClick={() => window.history.back()}
              size="sm"
              variant="outline"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Back to Jobs
            </Button>
          </div>
          <div className="flex items-center space-x-3">
            {isEditing ? (
              <>
                <Button onClick={handleEditToggle} size="sm" variant="outline">
                  <X className="mr-2 h-4 w-4" />
                  Cancel
                </Button>
                <Button disabled={saving} onClick={handleSave} size="sm">
                  <Save className="mr-2 h-4 w-4" />
                  {saving ? 'Saving...' : 'Save Changes'}
                </Button>
              </>
            ) : (
              <>
                <Button
                  disabled={isProcessing(job.id)}
                  onClick={handleProcessNow}
                  size="sm"
                >
                  <Play className="mr-2 h-4 w-4" />
                  {isProcessing(job.id) ? 'Processing...' : 'Process Now'}
                </Button>
                <Button onClick={handleEditToggle} size="sm">
                  <Edit className="mr-2 h-4 w-4" />
                  Edit
                </Button>
              </>
            )}
          </div>
        </div>

        {/* Title and metadata */}
        <div className="mb-6">
          <h1 className="mb-3 font-bold text-2xl text-foreground">
            {job.title}
          </h1>
          <div className="mb-2 flex items-center gap-2">
            <Badge
              className={getJobStatusBadgeClass(job.status)}
              variant="outline"
            >
              {job.status}
            </Badge>
            <Badge
              className={getProcessingStatusBadgeClass(job.processing_status)}
              variant="outline"
            >
              {getProcessingStatusText(job.processing_status)}
            </Badge>
            {sourceHost && (
              <Badge
                className="flex items-center gap-1 text-xs"
                variant="outline"
              >
                {!faviconError && faviconUrl ? (
                  <Image
                    alt={`${sourceHost} favicon`}
                    className="h-3 w-3 rounded-full"
                    height={12}
                    onError={() => setFaviconError(true)}
                    src={faviconUrl}
                    width={12}
                  />
                ) : null}
                <span>{job.source_name || sourceHost}</span>
              </Badge>
            )}
          </div>
          <p className="text-muted-foreground">
            {job.company} • Job ID: {job.id}
          </p>
        </div>

        <div className="space-y-6">
          <div className="rounded-lg border bg-card p-6 text-card-foreground shadow">
            <div className="mb-6 flex items-center justify-between">
              <h2 className="font-semibold text-foreground text-lg">
                Job Information
              </h2>
            </div>
            <div className="space-y-4">
              {Object.entries(job).map(([fieldKey, fieldValue]) => (
                <JobFieldRenderer
                  editForm={editForm}
                  fieldKey={fieldKey}
                  fieldValue={fieldValue}
                  isEditing={isEditing}
                  job={job}
                  key={fieldKey}
                  onInputChange={handleInputChange}
                />
              ))}
            </div>
          </div>

          <JobTimeline job={job} />
          <JobMonitoringLog job={job} jobId={job.id} />
        </div>
      </div>
    </div>
  );
}

/**
 * Handle job processing with toast notifications
 */
function useJobProcessingHandler(
  job: { id: string } | null,
  processJob: (id: string, callback: () => void) => Promise<void>
) {
  const handleProcessNow = async () => {
    if (!job) {
      return;
    }

    // Show loading toast
    const loadingToast = toast.loading('Starting AI job processing...', {
      description: 'Initializing extraction pipeline',
    });

    try {
      await processJob(job.id, () => {
        // This callback will be called when processing completes
        // We'll handle the refresh after showing the success toast
      });

      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show success toast
      toast.success('Job processing completed successfully!', {
        description: 'AI extraction finished • Page will refresh in 3 seconds',
        duration: 4000,
        action: {
          label: 'Refresh Now',
          onClick: () => window.location.reload(),
        },
      });

      // Refresh after a delay so user can read the toast
      setTimeout(() => {
        window.location.reload();
      }, 3000);
    } catch (_processError) {
      // Dismiss loading toast
      toast.dismiss(loadingToast);

      // Show error toast
      toast.error('Failed to process job', {
        description: ((): string => {
          try {
            // Try to surface the last processing error if available
            const w = window as unknown as { lastProcessJobError?: string };
            return (
              w?.lastProcessJobError ||
              'Please try again or check the job details for issues'
            );
          } catch {
            return 'Please try again or check the job details for issues';
          }
        })(),
        duration: 6000,
        action: {
          label: 'Retry',
          onClick: () => handleProcessNow(),
        },
      });
    }
  };

  return { handleProcessNow };
}

const JobDetailPage: React.FC<JobDetailPageProps> = ({ params }) => {
  const router = useRouter();
  const resolvedParams = usePromise(params);
  const jobId = resolvedParams.id;

  // Custom hooks for data management
  const {
    job,
    loading,
    error,
    editForm,
    isEditing,
    saving,
    handleEditToggle,
    handleSave,
    handleInputChange,
  } = useJobDetail(jobId);

  // Process Now functionality
  const { processJob, isProcessing } = useJobProcessing();
  const { handleProcessNow } = useJobProcessingHandler(job, processJob);

  if (loading) {
    return <LoadingState />;
  }

  if (error) {
    return (
      <ErrorState
        error={error}
        onGoBack={() => router.back()}
        onRetry={() => window.location.reload()}
      />
    );
  }

  if (!job) {
    return <NotFoundState onGoBack={() => router.back()} />;
  }

  return (
    <JobContent
      editForm={editForm}
      handleEditToggle={handleEditToggle}
      handleInputChange={handleInputChange}
      handleProcessNow={handleProcessNow}
      handleSave={handleSave}
      isEditing={isEditing}
      isProcessing={isProcessing}
      job={job}
      saving={saving}
    />
  );
};

export default JobDetailPage;
