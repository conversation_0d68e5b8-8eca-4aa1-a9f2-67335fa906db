'use client';

import React from 'react';
import { BoardSchemaInspector } from '@/components/boards/board-schema-inspector';
import { BoardsStats } from '@/components/boards/boards-stats';
import { BoardsTable } from '@/components/boards/boards-table';
import type { JobBoard } from '@/components/boards/columns';

export default function BoardsPage() {
  const [refreshTrigger, setRefreshTrigger] = React.useState(0);
  const [boards, setBoards] = React.useState<JobBoard[]>([]);

  const handleDataChange = React.useCallback(() => {
    // Increment the trigger to refresh stats
    setRefreshTrigger((prev) => prev + 1);
  }, []);

  const handleBoardsLoaded = React.useCallback((boardsData: JobBoard[]) => {
    setBoards(boardsData);
  }, []);

  return (
    <div className="flex-1 space-y-6 p-8 pt-6">
      <div>
        <h1 className="font-semibold text-3xl tracking-tight">Job Boards</h1>
        <p className="text-muted-foreground">
          Configure and manage your automated job posting boards with advanced
          filtering and Airtable integration.
        </p>
      </div>

      <BoardsStats refreshTrigger={refreshTrigger} />
      <BoardsTable
        onBoardsLoaded={handleBoardsLoaded}
        onDataChange={handleDataChange}
      />

      {/* Airtable Schema Inspector */}
      <div className="space-y-4">
        <div>
          <h2 className="font-semibold text-xl tracking-tight">
            Schema Inspector
          </h2>
          <p className="text-muted-foreground text-sm">
            Inspect Airtable table schemas and validate field mappings for your
            job boards.
          </p>
        </div>
        <BoardSchemaInspector boards={boards} />
      </div>
    </div>
  );
}
