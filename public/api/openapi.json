{"openapi": "3.1.0", "jsonSchemaDialect": "https://json-schema.org/draft/2020-12/schema", "info": {"title": "Bordfeed API", "version": "0.1.7", "description": "AI-powered job board automation platform API with comprehensive versioning support", "contact": {"name": "Bordfeed Support", "url": "https://bordfeed.com", "email": "<EMAIL>"}, "license": {"name": "MIT", "url": "https://opensource.org/licenses/MIT"}, "termsOfService": "https://bordfeed.com/terms", "x-logo": {"url": "https://bordfeed.com/logo.svg", "backgroundColor": "#ffffff", "altText": "Bordfeed logo"}, "x-api-version": "0.1.7", "x-api-status": "development", "x-versioning-scheme": "semantic", "x-deprecation-policy": "6 months notice, 12 months support", "x-changelog": "https://bordfeed.com/docs/changelog", "x-migration-guides": "https://bordfeed.com/docs/api/migration"}, "servers": [{"url": "http://localhost:3000", "description": "Development server"}, {"url": "https://bordfeed.com", "description": "Production server"}], "tags": [{"name": "Versioning", "description": "API version information and management endpoints"}, {"name": "Health", "description": "Health check endpoints"}, {"name": "Pipeline", "description": "Data processing pipeline endpoints"}, {"name": "Webhooks", "description": "Webhook endpoints for external services"}], "paths": {"/api/versions": {"get": {"tags": ["Versioning"], "summary": "Get API version information", "description": "Returns comprehensive information about API versions, support status, deprecation timeline, and migration paths.", "operationId": "getVersions", "responses": {"200": {"description": "API version information", "headers": {"API-Version": {"description": "Current API version", "schema": {"type": "string", "example": "0.1.7"}}, "API-Supported-Versions": {"description": "Comma-separated list of supported versions", "schema": {"type": "string", "example": "0.1.7"}}, "API-Deprecated-Versions": {"description": "Comma-separated list of deprecated versions", "schema": {"type": "string", "example": ""}}, "Cache-Control": {"description": "Cache control header", "schema": {"type": "string", "example": "public, max-age=3600"}}}, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VersionsResponse"}, "examples": {"current": {"summary": "Current version information", "value": {"current": "0.1.7", "latest_stable": "0.1.7", "supported": ["0.1.7"], "deprecated": [], "retired": [], "versions": {"0.1.7": {"status": "current", "released": "2024-01-15T00:00:00Z", "openapi": "/api/openapi.json", "documentation": "/docs/api", "changelog": "/docs/changelog/v0.1.7", "features": ["Pipeline job processing", "Health monitoring", "Webhook callbacks", "Upstash Workflow integration", "AI-powered extraction"], "breaking_changes": [], "migration_guide": null, "support_until": null}}, "api_info": {"name": "Bordfeed API", "description": "AI-powered job board automation platform API", "base_url": "https://bordfeed.com", "versioning_scheme": "Semantic Versioning (SemVer)", "support_policy": "Current + Previous Major Version"}, "deprecation_policy": {"notice_period": "6 months", "support_duration": "12 months for major versions", "migration_assistance": true}}}}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"serverError": {"summary": "Version information retrieval failed", "value": {"error": "Internal Server Error", "message": "Failed to retrieve version information", "code": "VERSION_INFO_ERROR", "timestamp": "2024-01-17T10:00:00Z"}}}}}}}}, "head": {"tags": ["Versioning"], "summary": "Check versions endpoint availability", "description": "Health check for the versions endpoint. Returns version headers without response body.", "operationId": "checkVersionsEndpoint", "responses": {"200": {"description": "Versions endpoint is available", "headers": {"API-Version": {"description": "Current API version", "schema": {"type": "string", "example": "0.1.7"}}, "API-Supported-Versions": {"description": "Comma-separated list of supported versions", "schema": {"type": "string", "example": "0.1.7"}}}}}}}, "/api/pipeline-ingest": {"get": {"tags": ["Health"], "summary": "Pipeline health check", "description": "Returns the health status and capabilities of the pipeline ingestion service. Only available in development environment.", "operationId": "getPipelineHealth", "x-code-samples": [{"lang": "curl", "source": "curl -X GET \"http://localhost:3000/api/pipeline-ingest\" \\\n  -H \"Accept: application/json\""}, {"lang": "javascript", "source": "// Using fetch API\nconst response = await fetch('http://localhost:3000/api/pipeline-ingest', {\n  method: 'GET',\n  headers: {\n    'Accept': 'application/json'\n  }\n});\nconst data = await response.json();\nconsole.log(data);"}, {"lang": "typescript", "source": "// Using fetch with TypeScript\ninterface PipelineHealthResponse {\n  status: 'healthy' | 'degraded' | 'unhealthy';\n  timestamp: string;\n  capabilities: string[];\n  environment: string;\n}\n\nconst response = await fetch('http://localhost:3000/api/pipeline-ingest', {\n  method: 'GET',\n  headers: {\n    'Accept': 'application/json'\n  }\n});\nconst data: PipelineHealthResponse = await response.json();"}, {"lang": "python", "source": "import requests\n\nresponse = requests.get(\n    'http://localhost:3000/api/pipeline-ingest',\n    headers={'Accept': 'application/json'}\n)\ndata = response.json()\nprint(data)"}], "responses": {"200": {"description": "Service health status and capabilities", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PipelineHealthResponse"}, "examples": {"healthy": {"summary": "Healthy pipeline service", "value": {"message": "🚀 Pipeline ingestion endpoint ready", "version": "v2.0.0", "features": ["✅ Upstash Workflow processing", "✅ Batch job processing", "✅ AI extraction with GPT-4o-mini", "✅ Database persistence (FIXED!)", "✅ Job normalization & validation", "✅ Error handling & retry", "✅ Cost tracking", "✅ Processing metrics", "✅ Flow control support"], "usage": {"single": "POST with PipelineJob schema", "batch": "POST with PipelineBatch schema"}, "timestamp": "2024-01-15T10:30:00Z"}}}}}}, "403": {"description": "Not allowed in production environment", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}, "post": {"tags": ["Pipeline"], "summary": "Process job data through AI extraction pipeline", "description": "Accepts single jobs or batches for AI-powered extraction and database storage. Processed via Upstash Workflow.", "operationId": "processPipelineJobs", "x-code-samples": [{"lang": "curl", "source": "# Single job processing\ncurl -X POST \"http://localhost:3000/api/pipeline-ingest\" \\\n  -H \"Content-Type: application/json\" \\\n  -H \"Upstash-Signature: your-signature\" \\\n  -d '{\n    \"content\": \"Software Engineer position at TechCorp. We are looking for a skilled developer...\",\n    \"sourceUrl\": \"https://example.com/jobs/software-engineer-123\",\n    \"source\": \"example-board\",\n    \"metadata\": {\n      \"title\": \"Software Engineer\",\n      \"priority\": \"high\"\n    }\n  }'"}, {"lang": "javascript", "source": "// Single job processing with fetch\nconst jobData = {\n  content: 'Software Engineer position at TechCorp. We are looking for a skilled developer...',\n  sourceUrl: 'https://example.com/jobs/software-engineer-123',\n  source: 'example-board',\n  metadata: {\n    title: 'Software Engineer',\n    priority: 'high'\n  }\n};\n\nconst response = await fetch('http://localhost:3000/api/pipeline-ingest', {\n  method: 'POST',\n  headers: {\n    'Content-Type': 'application/json',\n    'Upstash-Signature': 'your-signature'\n  },\n  body: JSON.stringify(jobData)\n});\n\nconst result = await response.json();\nconsole.log(result);"}, {"lang": "typescript", "source": "// TypeScript with proper types\ninterface PipelineJob {\n  content: string;\n  sourceUrl: string;\n  source: string;\n  metadata?: {\n    title?: string;\n    postedDate?: string;\n    priority?: 'low' | 'normal' | 'high';\n    batchId?: string;\n  };\n}\n\ninterface PipelineProcessingResponse {\n  success: boolean;\n  message: string;\n  summary: {\n    total: number;\n    successful: number;\n    failed: number;\n    duplicates: number;\n  };\n  results: Array<{\n    jobId: string;\n    status: 'success' | 'failed' | 'duplicate';\n    extractedData?: any;\n    error?: string;\n  }>;\n}\n\nconst processJob = async (job: PipelineJob): Promise<PipelineProcessingResponse> => {\n  const response = await fetch('http://localhost:3000/api/pipeline-ingest', {\n    method: 'POST',\n    headers: {\n      'Content-Type': 'application/json',\n      'Upstash-Signature': process.env.QSTASH_SIGNATURE || ''\n    },\n    body: JSON.stringify(job)\n  });\n\n  if (!response.ok) {\n    throw new Error(`HTTP error! status: ${response.status}`);\n  }\n\n  return await response.json();\n};"}, {"lang": "python", "source": "import requests\nimport json\nfrom typing import Dict, Any, Optional\n\ndef process_job(job_data: Dict[str, Any], signature: str) -> Dict[str, Any]:\n    \"\"\"Process a single job through the pipeline.\"\"\"\n    url = 'http://localhost:3000/api/pipeline-ingest'\n    headers = {\n        'Content-Type': 'application/json',\n        'Upstash-Signature': signature\n    }\n    \n    response = requests.post(url, headers=headers, json=job_data)\n    response.raise_for_status()\n    return response.json()\n\n# Example usage\njob = {\n    'content': 'Software Engineer position at TechCorp. We are looking for a skilled developer...',\n    'sourceUrl': 'https://example.com/jobs/software-engineer-123',\n    'source': 'example-board',\n    'metadata': {\n        'title': 'Software Engineer',\n        'priority': 'high'\n    }\n}\n\ntry:\n    result = process_job(job, 'your-signature')\n    print(f\"Processing result: {result}\")\nexcept requests.exceptions.RequestException as e:\n    print(f\"Error processing job: {e}\")"}], "security": [{"QStashSignature": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/PipelineJob"}, {"$ref": "#/components/schemas/PipelineBatch"}]}, "examples": {"singleJob": {"summary": "Single job processing", "value": {"content": "Software Engineer position at TechCorp...", "sourceUrl": "https://example.com/job/123", "source": "example-board", "metadata": {"company": "TechCorp", "title": "Software Engineer", "batchId": "batch-001"}}}, "batchJobs": {"summary": "Batch job processing", "value": {"jobs": [{"content": "Frontend Developer role...", "sourceUrl": "https://example.com/job/124", "source": "example-board"}, {"content": "Backend Engineer position...", "sourceUrl": "https://example.com/job/125", "source": "example-board"}], "batchId": "batch-002", "priority": "high"}}}}}}, "responses": {"200": {"description": "Jobs processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/PipelineProcessingResponse"}, "examples": {"singleJobSuccess": {"summary": "Single job processed successfully", "value": {"success": true, "message": "✅ Pipeline processing completed successfully", "summary": {"total": 1, "successful": 1, "failed": 0, "duplicates": 0}, "results": [{"jobId": "job-1", "status": "success", "extractedData": {"title": "Senior Software Engineer", "company": "TechCorp", "location": "San Francisco, CA", "salary": "$120,000 - $180,000", "skills": ["JavaScript", "React", "Node.js"], "experience": "5+ years"}, "metadata": {"cost": 0.0012, "duration": 1250}}], "errors": [], "duplicates": [], "metrics": {"totalCost": 0.0012, "totalDuration": 1250, "averageDuration": 1250}}}, "batchJobsSuccess": {"summary": "Batch jobs processed with mixed results", "value": {"success": true, "message": "✅ Pipeline processing completed successfully", "summary": {"total": 5, "successful": 4, "failed": 0, "duplicates": 1}, "results": [{"jobId": "job-1", "status": "success", "extractedData": {"title": "Frontend Developer", "company": "StartupCo", "location": "Remote", "salary": "$80,000 - $120,000"}, "metadata": {"cost": 0.0008, "duration": 980}}], "errors": [], "duplicates": [{"jobId": "job-5", "sourceUrl": "https://example.com/job/duplicate", "reason": "URL already exists in database", "existingJobId": "existing-job-123"}], "metrics": {"totalCost": 0.0045, "totalDuration": 4200, "averageDuration": 1050}}}}}}}, "400": {"description": "Invalid request data", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"missingContent": {"summary": "Missing required content field", "value": {"error": "Validation Error", "message": "Missing required field: content", "details": {"field": "content", "code": "REQUIRED_FIELD_MISSING"}, "timestamp": "2024-01-15T10:30:00Z"}}, "invalidUrl": {"summary": "Invalid source URL format", "value": {"error": "Validation Error", "message": "Invalid URL format for sourceUrl", "details": {"field": "sourceUrl", "value": "not-a-valid-url", "code": "INVALID_URL_FORMAT"}, "timestamp": "2024-01-15T10:30:00Z"}}}}}}, "401": {"description": "Authentication required", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Authentication Required", "message": "QStash signature verification required for this endpoint", "details": {"code": "MISSING_AUTHENTICATION", "requiredHeader": "Upstash-Signature"}, "timestamp": "2024-01-15T10:30:00Z"}}}}, "403": {"description": "Missing or invalid QStash signature", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"invalidSignature": {"summary": "Invalid QStash signature", "value": {"error": "Forbidden", "message": "Invalid QStash signature verification failed", "details": {"code": "INVALID_SIGNATURE", "hint": "Ensure the Upstash-Signature header contains a valid signature"}, "timestamp": "2024-01-15T10:30:00Z"}}, "expiredSignature": {"summary": "Expired signature", "value": {"error": "Forbidden", "message": "QStash signature has expired", "details": {"code": "SIGNATURE_EXPIRED", "hint": "Request must be made within the signature validity window"}, "timestamp": "2024-01-15T10:30:00Z"}}}}}}, "422": {"description": "Unprocessable entity - validation failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"contentTooLarge": {"summary": "Content exceeds size limit", "value": {"error": "Unprocessable Entity", "message": "Job content exceeds maximum allowed size", "details": {"code": "CONTENT_TOO_LARGE", "maxSize": "100KB", "actualSize": "150KB"}, "timestamp": "2024-01-15T10:30:00Z"}}, "duplicateJob": {"summary": "Duplicate job detected", "value": {"error": "Unprocessable Entity", "message": "Job with this source URL already exists", "details": {"code": "DUPLICATE_JOB", "existingJobId": "job-abc123", "sourceUrl": "https://example.com/job/123"}, "timestamp": "2024-01-15T10:30:00Z"}}}}}}, "429": {"description": "Rate limit exceeded", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Rate Limit Exceeded", "message": "Too many requests. Please try again later.", "details": {"code": "RATE_LIMIT_EXCEEDED", "limit": 100, "remaining": 0, "resetTime": "2024-01-15T10:35:00Z"}, "timestamp": "2024-01-15T10:30:00Z"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"databaseError": {"summary": "Database connection failed", "value": {"error": "Internal Server Error", "message": "Database operation failed", "details": {"code": "DATABASE_ERROR", "component": "database", "retryable": true}, "timestamp": "2024-01-15T10:30:00Z"}}, "aiProcessingError": {"summary": "AI extraction service failed", "value": {"error": "Internal Server Error", "message": "AI processing service temporarily unavailable", "details": {"code": "AI_SERVICE_ERROR", "component": "ai-extraction", "retryable": true, "estimatedRetryAfter": "30s"}, "timestamp": "2024-01-15T10:30:00Z"}}, "queueError": {"summary": "Queue processing failed", "value": {"error": "Internal Server Error", "message": "Failed to queue job for processing", "details": {"code": "QUEUE_ERROR", "component": "qstash", "retryable": true}, "timestamp": "2024-01-15T10:30:00Z"}}}}}}}}}, "/api/health": {"get": {"tags": ["Health"], "summary": "Comprehensive system health check", "description": "Returns detailed health status of all system components including database, Upstash Workflow, Apify, and Slack integrations", "operationId": "getSystemHealth", "x-code-samples": [{"lang": "curl", "source": "curl -X GET \"http://localhost:3000/api/health\" \\\n  -H \"Accept: application/json\""}, {"lang": "javascript", "source": "// Check system health\nconst checkHealth = async () => {\n  try {\n    const response = await fetch('http://localhost:3000/api/health', {\n      method: 'GET',\n      headers: {\n        'Accept': 'application/json'\n      }\n    });\n    \n    const health = await response.json();\n    \n    if (health.status === 'healthy') {\n      console.log('✅ System is healthy');\n    } else {\n      console.warn('⚠️ System has issues:', health);\n    }\n    \n    return health;\n  } catch (error) {\n    console.error('❌ Health check failed:', error);\n    throw error;\n  }\n};\n\ncheckHealth();"}, {"lang": "typescript", "source": "// TypeScript health check with proper types\ninterface HealthCheck {\n  status: 'healthy' | 'degraded' | 'unhealthy';\n  latency: number;\n}\n\ninterface SystemHealthResponse {\n  status: 'healthy' | 'degraded' | 'unhealthy';\n  timestamp: string;\n  version: string;\n  environment: string;\n  checks: {\n    database: HealthCheck;\n    qstash: HealthCheck;\n    apify: HealthCheck;\n    slack: HealthCheck;\n    uptime: number;\n  };\n}\n\nclass HealthMonitor {\n  private baseUrl: string;\n\n  constructor(baseUrl: string = 'http://localhost:3000') {\n    this.baseUrl = baseUrl;\n  }\n\n  async checkHealth(): Promise<SystemHealthResponse> {\n    const response = await fetch(`${this.baseUrl}/api/health`, {\n      method: 'GET',\n      headers: {\n        'Accept': 'application/json'\n      }\n    });\n\n    if (!response.ok) {\n      throw new Error(`Health check failed: ${response.status}`);\n    }\n\n    return await response.json();\n  }\n\n  async isHealthy(): Promise<boolean> {\n    try {\n      const health = await this.checkHealth();\n      return health.status === 'healthy';\n    } catch {\n      return false;\n    }\n  }\n}\n\n// Usage\nconst monitor = new HealthMonitor();\nconst isSystemHealthy = await monitor.isHealthy();"}, {"lang": "python", "source": "import requests\nfrom typing import Dict, Any\nfrom dataclasses import dataclass\nfrom datetime import datetime\n\n@dataclass\nclass HealthCheck:\n    status: str\n    latency: float\n\n@dataclass\nclass SystemHealth:\n    status: str\n    timestamp: str\n    version: str\n    environment: str\n    checks: Dict[str, Any]\n    uptime: float\n\nclass HealthMonitor:\n    def __init__(self, base_url: str = 'http://localhost:3000'):\n        self.base_url = base_url\n    \n    def check_health(self) -> SystemHealth:\n        \"\"\"Check system health and return structured data.\"\"\"\n        response = requests.get(\n            f'{self.base_url}/api/health',\n            headers={'Accept': 'application/json'},\n            timeout=10\n        )\n        response.raise_for_status()\n        \n        data = response.json()\n        return SystemHealth(\n            status=data['status'],\n            timestamp=data['timestamp'],\n            version=data['version'],\n            environment=data['environment'],\n            checks=data['checks'],\n            uptime=data['checks']['uptime']\n        )\n    \n    def is_healthy(self) -> bool:\n        \"\"\"Quick health check returning boolean.\"\"\"\n        try:\n            health = self.check_health()\n            return health.status == 'healthy'\n        except Exception:\n            return False\n\n# Usage\nmonitor = HealthMonitor()\n\nif monitor.is_healthy():\n    print('✅ System is healthy')\nelse:\n    print('❌ System has issues')\n    health = monitor.check_health()\n    print(f'Status: {health.status}')\n    print(f'Environment: {health.environment}')\n    print(f'Uptime: {health.uptime} seconds')"}], "responses": {"200": {"description": "System health status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SystemHealthResponse"}, "examples": {"allHealthy": {"summary": "All systems healthy", "value": {"status": "healthy", "timestamp": "2024-01-15T10:30:00Z", "version": "0.1.7", "environment": "development", "checks": {"database": {"status": "healthy", "latency": 45.2}, "qstash": {"status": "healthy", "latency": 120.8}, "apify": {"status": "healthy", "latency": 89.3}, "slack": {"status": "healthy", "latency": 156.7}, "uptime": 86400}}}, "degraded": {"summary": "Some services degraded", "value": {"status": "degraded", "timestamp": "2024-01-15T10:30:00Z", "version": "0.1.7", "environment": "production", "checks": {"database": {"status": "healthy", "latency": 45.2}, "qstash": {"status": "error", "latency": 5000, "error": "Connection timeout"}, "apify": {"status": "healthy", "latency": 89.3}, "slack": {"status": "healthy", "latency": 156.7}, "uptime": 86400}}}}}}}, "500": {"description": "System health check failed", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/jobs/process-batch": {"get": {"tags": ["Health"], "summary": "Batch processor health check", "description": "Returns health status of the batch job processing service", "operationId": "getBatchProcessorHealth", "responses": {"200": {"description": "Batch processor health status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ServiceHealthResponse"}}}}}}}, "/api/sources/workable/health": {"get": {"tags": ["Health"], "summary": "Workable integration health check", "description": "Tests connection to Workable via Apify actor and returns health status", "operationId": "getWorkableHealth", "responses": {"200": {"description": "Workable integration is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceHealthResponse"}}}}, "500": {"description": "Workable integration error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceHealthErrorResponse"}}}}}}}, "/api/sources/jobdata-api/health": {"get": {"tags": ["Health"], "summary": "JobDataAPI integration health check", "description": "Tests connection to JobDataAPI via Apify actor and returns health status", "operationId": "getJobDataAPIHealth", "responses": {"200": {"description": "JobDataAPI integration is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceHealthResponse"}}}}, "500": {"description": "JobDataAPI integration error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceHealthErrorResponse"}}}}}}}, "/api/sources/wwr-rss/health": {"get": {"tags": ["Health"], "summary": "WeWorkRemotely integration health check", "description": "Tests connection to WeWorkRemotely via Apify actor and returns health status", "operationId": "getWWRHealth", "responses": {"200": {"description": "WeWorkRemotely integration is healthy", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceHealthResponse"}}}}, "500": {"description": "WeWorkRemotely integration error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SourceHealthErrorResponse"}}}}}}}, "/api/webhook-callbacks": {"get": {"tags": ["Webhooks"], "summary": "QStash webhook callbacks health check", "description": "Returns health status of the QStash webhook callback processing service", "operationId": "getWebhookCallbacksHealth", "x-code-samples": [{"lang": "curl", "source": "curl -X GET \"http://localhost:3000/api/webhook-callbacks\" \\\n  -H \"Accept: application/json\""}, {"lang": "javascript", "source": "// Check webhook service health\nconst checkWebhookHealth = async () => {\n  const response = await fetch('http://localhost:3000/api/webhook-callbacks', {\n    method: 'GET',\n    headers: {\n      'Accept': 'application/json'\n    }\n  });\n  \n  const health = await response.json();\n  console.log('Webhook service status:', health.status);\n  return health;\n};\n\ncheckWebhookHealth();"}, {"lang": "python", "source": "import requests\n\ndef check_webhook_health():\n    \"\"\"Check webhook callback service health.\"\"\"\n    response = requests.get(\n        'http://localhost:3000/api/webhook-callbacks',\n        headers={'Accept': 'application/json'}\n    )\n    response.raise_for_status()\n    \n    health = response.json()\n    print(f\"Webhook service status: {health['status']}\")\n    return health\n\ncheck_webhook_health()"}], "responses": {"200": {"description": "Webhook callbacks service health status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookServiceHealthResponse"}}}}}}, "post": {"tags": ["Webhooks"], "summary": "QStash webhook callbacks", "description": "Handles success and failure callbacks from QStash message processing", "operationId": "handleWebhookCallbacks", "security": [{"QStashSignature": []}], "requestBody": {"required": true, "content": {"application/json": {"schema": {"oneOf": [{"$ref": "#/components/schemas/QStashSuccessCallback"}, {"$ref": "#/components/schemas/QStashFailureCallback"}]}}}}, "responses": {"200": {"description": "Callback processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookCallbackResponse"}, "examples": {"successCallback": {"summary": "Success callback processed", "value": {"success": true, "message": "Success callback processed", "messageId": "msg_123456", "timestamp": "2024-01-15T10:30:00Z"}}, "failureCallback": {"summary": "Failure callback processed", "value": {"success": true, "message": "Failure callback processed", "messageId": "msg_123457", "timestamp": "2024-01-15T10:30:00Z"}}}}}}, "400": {"description": "Invalid webhook payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "examples": {"missingMessageId": {"summary": "Missing message ID", "value": {"error": "Validation Error", "message": "Missing required field: messageId", "details": {"code": "REQUIRED_FIELD_MISSING", "field": "messageId"}, "timestamp": "2024-01-15T10:30:00Z"}}, "invalidStatus": {"summary": "Invalid status value", "value": {"error": "Validation Error", "message": "Invalid status value. Must be 'success' or 'failure'", "details": {"code": "INVALID_ENUM_VALUE", "field": "status", "value": "invalid-status", "allowedValues": ["success", "failure"]}, "timestamp": "2024-01-15T10:30:00Z"}}}}}}, "403": {"description": "Invalid QStash signature", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Forbidden", "message": "Invalid QStash signature verification failed", "details": {"code": "INVALID_SIGNATURE", "component": "authentication"}, "timestamp": "2024-01-15T10:30:00Z"}}}}, "500": {"description": "Internal server error", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}, "example": {"error": "Internal Server Error", "message": "Failed to process webhook callback", "details": {"code": "WEBHOOK_PROCESSING_ERROR", "component": "webhook-handler", "retryable": true}, "timestamp": "2024-01-15T10:30:00Z"}}}}}}}, "/api/workable-webhook": {"get": {"tags": ["Webhooks"], "summary": "Workable webhook health check", "description": "Returns health status of the Workable webhook processing service", "operationId": "getWorkableWebhookHealth", "responses": {"200": {"description": "Workable webhook service health status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookServiceHealthResponse"}}}}}}, "post": {"tags": ["Webhooks"], "summary": "Workable job data webhook", "description": "Receives job data from Workable via Apify and processes it for storage", "operationId": "handleWorkableWebhook", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WorkableWebhookPayload"}}}}, "responses": {"200": {"description": "Webhook processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookProcessingResponse"}}}}, "400": {"description": "Invalid webhook payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/wwr-webhook": {"get": {"tags": ["Webhooks"], "summary": "WeWorkRemotely webhook health check", "description": "Returns health status of the WeWorkRemotely webhook processing service", "operationId": "getWWRWebhookHealth", "responses": {"200": {"description": "WeWorkRemotely webhook service health status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookServiceHealthResponse"}}}}}}, "post": {"tags": ["Webhooks"], "summary": "WeWorkRemotely job data webhook", "description": "Receives job data from WeWorkRemotely via Apify and processes it for storage", "operationId": "handleWWRWebhook", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WWRWebhookPayload"}}}}, "responses": {"200": {"description": "Webhook processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookProcessingResponse"}}}}, "400": {"description": "Invalid webhook payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}, "/api/jobdata-webhook": {"get": {"tags": ["Webhooks"], "summary": "JobDataAPI webhook health check", "description": "Returns health status of the JobDataAPI webhook processing service", "operationId": "getJobDataWebhookHealth", "responses": {"200": {"description": "JobDataAPI webhook service health status", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookServiceHealthResponse"}}}}}}, "post": {"tags": ["Webhooks"], "summary": "JobDataAPI job data webhook", "description": "Receives job data from JobDataAPI via Apify and processes it for storage", "operationId": "handleJobDataWebhook", "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/JobDataWebhookPayload"}}}}, "responses": {"200": {"description": "Webhook processed successfully", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WebhookProcessingResponse"}}}}, "400": {"description": "Invalid webhook payload", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ErrorResponse"}}}}}}}}, "components": {"schemas": {"VersionsResponse": {"type": "object", "required": ["current", "latest_stable", "supported", "deprecated", "retired", "versions", "api_info", "deprecation_policy"], "properties": {"current": {"type": "string", "description": "Current API version", "example": "0.1.7"}, "latest_stable": {"type": "string", "description": "Latest stable API version", "example": "0.1.7"}, "supported": {"type": "array", "items": {"type": "string"}, "description": "List of currently supported versions", "example": ["0.1.7"]}, "deprecated": {"type": "array", "items": {"type": "string"}, "description": "List of deprecated versions", "example": []}, "retired": {"type": "array", "items": {"type": "string"}, "description": "List of retired versions", "example": []}, "versions": {"type": "object", "additionalProperties": {"$ref": "#/components/schemas/VersionInfo"}, "description": "Detailed information for each version"}, "api_info": {"$ref": "#/components/schemas/APIInfo"}, "deprecation_policy": {"$ref": "#/components/schemas/DeprecationPolicy"}}}, "VersionInfo": {"type": "object", "required": ["status", "released", "openapi", "documentation", "changelog", "features", "breaking_changes", "migration_guide", "support_until"], "properties": {"status": {"type": "string", "enum": ["current", "supported", "deprecated", "retired", "planned"], "description": "Version status"}, "released": {"type": "string", "format": "date-time", "nullable": true, "description": "Release date (null for planned versions)"}, "openapi": {"type": "string", "description": "OpenAPI specification URL for this version"}, "documentation": {"type": "string", "description": "Documentation URL for this version"}, "changelog": {"type": "string", "description": "Changelog URL for this version"}, "features": {"type": "array", "items": {"type": "string"}, "description": "List of features in this version"}, "breaking_changes": {"type": "array", "items": {"type": "string"}, "description": "List of breaking changes in this version"}, "migration_guide": {"type": "string", "nullable": true, "description": "Migration guide URL (null if not applicable)"}, "support_until": {"type": "string", "format": "date-time", "nullable": true, "description": "End of support date (null for current version)"}}}, "APIInfo": {"type": "object", "required": ["name", "description", "base_url", "versioning_scheme", "support_policy"], "properties": {"name": {"type": "string", "description": "API name", "example": "Bordfeed API"}, "description": {"type": "string", "description": "API description", "example": "AI-powered job board automation platform API"}, "base_url": {"type": "string", "format": "uri", "description": "Base URL for the API", "example": "https://bordfeed.com"}, "versioning_scheme": {"type": "string", "description": "Versioning scheme used", "example": "Semantic Versioning (SemVer)"}, "support_policy": {"type": "string", "description": "Version support policy", "example": "Current + Previous Major Version"}}}, "DeprecationPolicy": {"type": "object", "required": ["notice_period", "support_duration", "migration_assistance"], "properties": {"notice_period": {"type": "string", "description": "Advance notice period for deprecations", "example": "6 months"}, "support_duration": {"type": "string", "description": "Support duration for deprecated versions", "example": "12 months for major versions"}, "migration_assistance": {"type": "boolean", "description": "Whether migration assistance is provided", "example": true}}}, "PipelineHealthResponse": {"type": "object", "required": ["message", "version", "features", "usage", "timestamp"], "properties": {"message": {"type": "string", "description": "Service status message", "example": "🚀 Pipeline ingestion endpoint ready"}, "version": {"type": "string", "description": "Service version", "example": "v2.0.0"}, "features": {"type": "array", "items": {"type": "string"}, "description": "Available service features", "example": ["✅ QStash webhook verification", "✅ Batch job processing", "✅ AI extraction with GPT-4o-mini", "✅ Database persistence (FIXED!)", "✅ Job normalization & validation", "✅ Error handling & retry", "✅ Cost tracking", "✅ Processing metrics", "✅ Flow control support"]}, "usage": {"type": "object", "properties": {"single": {"type": "string", "description": "Usage for single job processing", "example": "POST with PipelineJob schema"}, "batch": {"type": "string", "description": "Usage for batch job processing", "example": "POST with PipelineBatch schema"}}}, "timestamp": {"type": "string", "format": "date-time", "description": "Response timestamp"}}}, "PipelineJob": {"type": "object", "required": ["content", "sourceUrl"], "properties": {"content": {"type": "string", "minLength": 1, "description": "Raw job posting content to be processed", "example": "Software Engineer position at TechCorp. We are looking for a skilled developer..."}, "sourceUrl": {"type": "string", "format": "uri", "description": "URL of the original job posting", "example": "https://example.com/jobs/software-engineer-123"}, "source": {"type": "string", "description": "Source platform identifier", "default": "pipeline", "example": "linkedin"}, "metadata": {"type": "object", "properties": {"company": {"type": "string", "description": "Company name if known", "example": "TechCorp"}, "title": {"type": "string", "description": "Job title if known", "example": "Software Engineer"}, "postedDate": {"type": "string", "description": "Job posting date", "example": "2024-01-15"}, "batchId": {"type": "string", "description": "Batch identifier for grouping", "example": "batch-001"}}, "description": "Additional metadata about the job"}}}, "PipelineBatch": {"type": "object", "required": ["jobs"], "properties": {"jobs": {"type": "array", "items": {"$ref": "#/components/schemas/PipelineJob"}, "minItems": 1, "description": "Array of jobs to process in batch"}, "batchId": {"type": "string", "description": "Unique identifier for this batch", "example": "batch-001"}, "priority": {"type": "string", "enum": ["low", "normal", "high"], "default": "normal", "description": "Processing priority for the batch"}}}, "PipelineProcessingResponse": {"type": "object", "required": ["success", "message", "summary"], "properties": {"success": {"type": "boolean", "description": "Overall processing success status", "example": true}, "message": {"type": "string", "description": "Processing summary message", "example": "✅ Pipeline processing completed successfully"}, "summary": {"type": "object", "required": ["total", "successful", "failed", "duplicates"], "properties": {"total": {"type": "integer", "description": "Total number of jobs processed", "example": 5}, "successful": {"type": "integer", "description": "Number of successfully processed jobs", "example": 4}, "failed": {"type": "integer", "description": "Number of failed jobs", "example": 0}, "duplicates": {"type": "integer", "description": "Number of duplicate jobs skipped", "example": 1}}}, "results": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessingResult"}, "description": "Detailed results for each processed job"}, "errors": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessingError"}, "description": "Details of any processing errors"}, "duplicates": {"type": "array", "items": {"$ref": "#/components/schemas/ProcessingDuplicate"}, "description": "Details of duplicate jobs found"}, "metrics": {"type": "object", "properties": {"totalCost": {"type": "number", "description": "Total AI processing cost", "example": 0.0045}, "totalDuration": {"type": "integer", "description": "Total processing time in milliseconds", "example": 2500}, "averageDuration": {"type": "number", "description": "Average processing time per job", "example": 625.5}}}}}, "ProcessingResult": {"type": "object", "required": ["jobId", "status"], "properties": {"jobId": {"type": "string", "description": "Unique identifier for the processed job", "example": "job-1"}, "status": {"type": "string", "enum": ["success"], "description": "Processing status"}, "extractedData": {"type": "object", "description": "AI-extracted job data"}, "metadata": {"type": "object", "properties": {"cost": {"type": "number", "description": "AI processing cost for this job"}, "duration": {"type": "integer", "description": "Processing time in milliseconds"}}}}}, "ProcessingError": {"type": "object", "required": ["jobId", "error", "message"], "properties": {"jobId": {"type": "string", "description": "Identifier of the failed job"}, "error": {"type": "string", "description": "Error type"}, "message": {"type": "string", "description": "Error message"}, "sourceUrl": {"type": "string", "description": "U<PERSON> of the failed job"}}}, "ProcessingDuplicate": {"type": "object", "required": ["jobId", "sourceUrl", "reason"], "properties": {"jobId": {"type": "string", "description": "Identifier of the duplicate job"}, "sourceUrl": {"type": "string", "description": "URL of the duplicate job"}, "reason": {"type": "string", "description": "Reason for duplicate detection"}, "existingJobId": {"type": "string", "description": "ID of the existing job in database"}}}, "SystemHealthResponse": {"type": "object", "required": ["status", "timestamp", "version", "environment", "checks"], "properties": {"status": {"type": "string", "enum": ["healthy", "degraded", "unhealthy"], "description": "Overall system health status"}, "timestamp": {"type": "string", "format": "date-time", "description": "Health check timestamp"}, "version": {"type": "string", "description": "Application version"}, "environment": {"type": "string", "description": "Environment (development, production, etc.)"}, "checks": {"type": "object", "required": ["database", "qstash", "apify", "slack", "uptime"], "properties": {"database": {"$ref": "#/components/schemas/HealthCheck"}, "qstash": {"$ref": "#/components/schemas/HealthCheck"}, "apify": {"$ref": "#/components/schemas/HealthCheck"}, "slack": {"$ref": "#/components/schemas/HealthCheck"}, "uptime": {"type": "number", "description": "System uptime in seconds"}}}}}, "HealthCheck": {"type": "object", "required": ["status", "latency"], "properties": {"status": {"type": "string", "enum": ["healthy", "error", "unknown"], "description": "Component health status"}, "latency": {"type": "number", "description": "Response time in milliseconds"}, "error": {"type": "string", "description": "Error message if status is error"}}}, "ServiceHealthResponse": {"type": "object", "required": ["service", "status"], "properties": {"service": {"type": "string", "description": "Service name", "example": "Batch Job Processor"}, "status": {"type": "string", "enum": ["healthy"], "description": "Service status"}, "schedule": {"type": "string", "description": "Service schedule information", "example": "Every 5 minutes via QStash"}, "features": {"type": "array", "items": {"type": "string"}, "description": "Service features", "example": ["Processes 10 jobs per run", "AI extraction with GPT-4o-mini", "Automatic retry on failure", "Pipeline metrics tracking"]}, "environment": {"type": "object", "properties": {"hasOpenAI": {"type": "boolean", "description": "OpenAI API key configured"}, "hasSupabase": {"type": "boolean", "description": "Supabase configuration available"}, "hasQstash": {"type": "boolean", "description": "QStash token configured"}}}}}, "SourceHealthResponse": {"type": "object", "required": ["status", "latency", "message", "source", "timestamp"], "properties": {"status": {"type": "string", "enum": ["healthy"], "description": "Source integration status"}, "latency": {"type": "number", "description": "Response time in milliseconds"}, "message": {"type": "string", "description": "Health check message", "example": "Workable actor connection successful"}, "source": {"type": "string", "description": "Source identifier", "example": "workable"}, "details": {"type": "object", "properties": {"actorName": {"type": "string", "description": "Apify actor name"}, "username": {"type": "string", "description": "Apify username"}, "isPublic": {"type": "boolean", "description": "Whether the actor is public"}, "actorId": {"type": "string", "description": "Apify actor ID"}, "apifyUrl": {"type": "string", "format": "uri", "description": "Apify console URL"}, "hasRuns": {"type": "boolean", "description": "Whether the actor has recent runs"}, "lastRunStatus": {"type": "string", "description": "Status of the last run"}, "totalRuns": {"type": "integer", "description": "Total number of runs"}}}, "timestamp": {"type": "string", "format": "date-time", "description": "Health check timestamp"}}}, "SourceHealthErrorResponse": {"type": "object", "required": ["status", "latency", "error", "source", "timestamp"], "properties": {"status": {"type": "string", "enum": ["error"], "description": "Source integration status"}, "latency": {"type": "number", "description": "Response time in milliseconds"}, "error": {"type": "string", "description": "Error message", "example": "Apify token not configured"}, "source": {"type": "string", "description": "Source identifier"}, "statusCode": {"type": "integer", "description": "HTTP status code from external service"}, "timestamp": {"type": "string", "format": "date-time", "description": "Error timestamp"}}}, "WebhookServiceHealthResponse": {"type": "object", "required": ["service", "status"], "properties": {"service": {"type": "string", "description": "Webhook service name", "example": "QStash Webhook Callbacks"}, "status": {"type": "string", "enum": ["healthy"], "description": "Service status"}, "architecture": {"type": "string", "description": "Service architecture description", "example": "QStash Message Processing"}, "features": {"type": "array", "items": {"type": "string"}, "description": "Service features", "example": ["✅ Success callback handling", "✅ Failure callback (DLQ) handling", "✅ Pipeline step tracking", "✅ Slack notifications", "✅ Batch processing metrics", "✅ QStash signature verification"]}, "environment": {"type": "object", "properties": {"hasQStashToken": {"type": "boolean", "description": "QStash token configured"}, "hasCurrentSigningKey": {"type": "boolean", "description": "Current signing key configured"}, "hasNextSigningKey": {"type": "boolean", "description": "Next signing key configured"}, "tokenLength": {"type": "integer", "description": "QStash token length"}, "version": {"type": "string", "description": "Service version"}, "message": {"type": "string", "description": "Service status message"}}}}}, "QStashSuccessCallback": {"type": "object", "required": ["messageId", "status"], "properties": {"messageId": {"type": "string", "description": "QStash message ID"}, "status": {"type": "string", "enum": ["success"], "description": "Callback status"}, "url": {"type": "string", "format": "uri", "description": "Original destination URL"}, "body": {"type": "string", "description": "Response body from the destination"}, "responseHeaders": {"type": "object", "additionalProperties": {"type": "string"}, "description": "Response headers from the destination"}}}, "QStashFailureCallback": {"type": "object", "required": ["messageId", "status", "error"], "properties": {"messageId": {"type": "string", "description": "QStash message ID"}, "status": {"type": "string", "enum": ["failure"], "description": "Callback status"}, "error": {"type": "string", "description": "Error message"}, "url": {"type": "string", "format": "uri", "description": "Original destination URL"}, "retryCount": {"type": "integer", "description": "Number of retry attempts"}}}, "WebhookCallbackResponse": {"type": "object", "required": ["success", "message"], "properties": {"success": {"type": "boolean", "description": "Callback processing success"}, "message": {"type": "string", "description": "Processing result message"}, "messageId": {"type": "string", "description": "QStash message ID"}, "timestamp": {"type": "string", "format": "date-time", "description": "Processing timestamp"}}}, "WorkableWebhookPayload": {"type": "object", "required": ["jobs"], "properties": {"jobs": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "Job title"}, "company": {"type": "string", "description": "Company name"}, "location": {"type": "string", "description": "Job location"}, "url": {"type": "string", "format": "uri", "description": "Job posting URL"}, "description": {"type": "string", "description": "Job description"}, "postedAt": {"type": "string", "format": "date-time", "description": "Job posting date"}}}, "description": "Array of job data from Workable"}, "metadata": {"type": "object", "properties": {"source": {"type": "string", "default": "workable", "description": "Data source identifier"}, "batchId": {"type": "string", "description": "Batch identifier"}}}}}, "WWRWebhookPayload": {"type": "object", "required": ["jobs"], "properties": {"jobs": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "Job title"}, "company": {"type": "string", "description": "Company name"}, "location": {"type": "string", "description": "Job location"}, "url": {"type": "string", "format": "uri", "description": "Job posting URL"}, "description": {"type": "string", "description": "Job description"}, "postedAt": {"type": "string", "format": "date-time", "description": "Job posting date"}}}, "description": "Array of job data from WeWorkRemotely"}, "metadata": {"type": "object", "properties": {"source": {"type": "string", "default": "wwr", "description": "Data source identifier"}, "batchId": {"type": "string", "description": "Batch identifier"}}}}}, "JobDataWebhookPayload": {"type": "object", "required": ["jobs"], "properties": {"jobs": {"type": "array", "items": {"type": "object", "properties": {"title": {"type": "string", "description": "Job title"}, "company": {"type": "string", "description": "Company name"}, "location": {"type": "string", "description": "Job location"}, "url": {"type": "string", "format": "uri", "description": "Job posting URL"}, "description": {"type": "string", "description": "Job description"}, "postedAt": {"type": "string", "format": "date-time", "description": "Job posting date"}}}, "description": "Array of job data from JobDataAPI"}, "metadata": {"type": "object", "properties": {"source": {"type": "string", "default": "jobdata-api", "description": "Data source identifier"}, "batchId": {"type": "string", "description": "Batch identifier"}}}}}, "WebhookProcessingResponse": {"type": "object", "required": ["success", "message"], "properties": {"success": {"type": "boolean", "description": "Webhook processing success"}, "message": {"type": "string", "description": "Processing result message", "example": "✅ Webhook processed successfully"}, "processed": {"type": "integer", "description": "Number of jobs processed"}, "duplicates": {"type": "integer", "description": "Number of duplicate jobs skipped"}, "errors": {"type": "integer", "description": "Number of processing errors"}, "timestamp": {"type": "string", "format": "date-time", "description": "Processing timestamp"}}}, "ErrorResponse": {"type": "object", "required": ["error", "message", "timestamp"], "properties": {"error": {"type": "string", "description": "High-level error category", "example": "Validation Error", "enum": ["Validation Error", "Authentication Required", "Forbidden", "Not Found", "Unprocessable Entity", "Rate Limit Exceeded", "Internal Server Error", "Service Unavailable"]}, "message": {"type": "string", "description": "Human-readable error description", "example": "Missing required field: content"}, "details": {"type": "object", "description": "Additional structured error details", "properties": {"code": {"type": "string", "description": "Machine-readable error code", "example": "REQUIRED_FIELD_MISSING"}, "field": {"type": "string", "description": "Field that caused the error (for validation errors)", "example": "content"}, "value": {"description": "Invalid value that caused the error", "example": "invalid-value"}, "component": {"type": "string", "description": "System component that generated the error", "example": "database", "enum": ["database", "ai-extraction", "qstash", "validation", "authentication"]}, "retryable": {"type": "boolean", "description": "Whether the operation can be retried", "example": true}, "estimatedRetryAfter": {"type": "string", "description": "Suggested retry delay", "example": "30s"}, "limit": {"type": "integer", "description": "Rate limit threshold (for rate limit errors)", "example": 100}, "remaining": {"type": "integer", "description": "Remaining requests in current window", "example": 0}, "resetTime": {"type": "string", "format": "date-time", "description": "When the rate limit resets", "example": "2024-01-15T10:35:00Z"}}, "additionalProperties": true}, "timestamp": {"type": "string", "format": "date-time", "description": "When the error occurred", "example": "2024-01-15T10:30:00Z"}, "requestId": {"type": "string", "description": "Unique identifier for this request (for debugging)", "example": "req_abc123def456"}}}}, "securitySchemes": {"QStashSignature": {"type": "<PERSON><PERSON><PERSON><PERSON>", "in": "header", "name": "Upstash-Signature", "description": "QStash webhook signature for verification"}}}}