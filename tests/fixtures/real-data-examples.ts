/**
 * Real Data Examples for Bordfeed API
 *
 * Interactive examples using real (sanitized) data from the Bordfeed application
 * These examples demonstrate actual API responses and data structures
 */

import type { JobData, JobMetadata, SavedJob } from '../../lib/types';

// Real job data examples (sanitized from actual Bordfeed data)
export const REAL_JOB_EXAMPLES = {
  // Senior Software Engineer from TechCorp
  seniorSoftwareEngineer: {
    sourcedAt: '2024-01-15T10:30:00Z',
    sourceUrl: 'https://techcorp.com/careers/senior-software-engineer',
    title: 'Senior Software Engineer',
    company: 'TechCorp',
    type: 'Full-time',
    description: `We are seeking a Senior Software Engineer to join our growing engineering team. You will be responsible for designing, developing, and maintaining scalable web applications using modern technologies.

Key Responsibilities:
• Design and implement robust, scalable software solutions
• Collaborate with cross-functional teams to deliver high-quality products
• Mentor junior developers and contribute to technical decisions
• Participate in code reviews and maintain coding standards
• Work with modern tech stack including React, Node.js, and TypeScript

Requirements:
• 5+ years of experience in software development
• Strong proficiency in JavaScript/TypeScript
• Experience with React, Node.js, and modern web technologies
• Knowledge of database design and optimization
• Excellent problem-solving and communication skills
• Bachelor's degree in Computer Science or equivalent experience

Benefits:
• Competitive salary and equity package
• Comprehensive health, dental, and vision insurance
• Flexible work arrangements and remote options
• Professional development budget
• 401(k) with company matching`,
    apply_url: 'https://techcorp.com/careers/senior-software-engineer/apply',
    apply_method: 'online',
    posted_date: '2024-01-15T08:00:00Z',
    status: 'active',
    salary_min: 120_000,
    salary_max: 180_000,
    salary_currency: 'USD',
    salary_unit: 'yearly',
    workplace_type: 'hybrid',
    remote_region: 'US',
    timezone_requirements: 'PST/EST overlap required',
    workplace_city: 'San Francisco',
    workplace_country: 'United States',
    benefits:
      'Health insurance, 401k, Stock options, Remote work, Flexible hours',
    application_requirements: 'Portfolio, GitHub profile, Cover letter',
    valid_through: '2024-02-15T23:59:59Z',
    job_identifier: 'TECH-SE-2024-001',
    job_source_name: 'TechCorp Careers',
    source_name: 'techcorp-careers',
    department: 'Engineering',
    travel_required: false,
    career_level: ['Mid-level', 'Senior'],
    visa_sponsorship: 'Available',
    languages: ['English'],
    skills:
      'JavaScript, TypeScript, React, Node.js, PostgreSQL, AWS, Docker, Git',
    qualifications:
      "Bachelor's degree in Computer Science or equivalent, 5+ years experience",
    education_requirements: "Bachelor's degree preferred",
    experience_requirements: '5+ years in software development',
    responsibilities:
      'Design software solutions, mentor developers, code reviews, technical decisions',
    featured: false,
    industry: 'Technology',
    occupational_category: 'Software Development',
  } as JobData,

  // Remote Frontend Developer from StartupCorp
  remoteFrontendDeveloper: {
    sourcedAt: '2024-01-16T14:20:00Z',
    sourceUrl: 'https://startupcorp.io/jobs/frontend-developer',
    title: 'Frontend Developer - React Specialist',
    company: 'StartupCorp',
    type: 'Full-time',
    description: `Join our fast-growing startup as a Frontend Developer specializing in React. You'll work on cutting-edge products that impact millions of users worldwide.

What You'll Do:
• Build responsive, performant web applications using React and TypeScript
• Collaborate with designers to implement pixel-perfect UI/UX
• Optimize applications for maximum speed and scalability
• Work with backend teams to integrate APIs and services
• Contribute to our design system and component library

What We're Looking For:
• 3+ years of experience with React and modern JavaScript
• Strong understanding of HTML, CSS, and responsive design
• Experience with state management (Redux, Zustand, or similar)
• Familiarity with testing frameworks (Jest, React Testing Library)
• Knowledge of build tools and CI/CD processes

Why Join Us:
• Work with a talented, diverse team
• Flexible remote work policy
• Competitive compensation and equity
• Learning and development opportunities
• Modern tech stack and tools`,
    apply_url: 'https://startupcorp.io/jobs/frontend-developer/apply',
    apply_method: 'online',
    posted_date: '2024-01-16T12:00:00Z',
    status: 'active',
    salary_min: 90_000,
    salary_max: 130_000,
    salary_currency: 'USD',
    salary_unit: 'yearly',
    workplace_type: 'remote',
    remote_region: 'Global',
    timezone_requirements: 'UTC-8 to UTC+2',
    workplace_city: null,
    workplace_country: 'Remote',
    benefits: 'Health insurance, Equity, Learning budget, Flexible PTO',
    application_requirements: 'Portfolio, GitHub profile',
    valid_through: '2024-03-01T23:59:59Z',
    job_identifier: 'STARTUP-FE-2024-002',
    job_source_name: 'StartupCorp Jobs',
    source_name: 'startupcorp-jobs',
    department: 'Product',
    travel_required: false,
    career_level: ['Mid-level'],
    visa_sponsorship: 'Not available',
    languages: ['English'],
    skills: 'React, TypeScript, HTML, CSS, Redux, Jest, Webpack, Git',
    qualifications: '3+ years React experience, strong JavaScript skills',
    education_requirements: 'Degree preferred but not required',
    experience_requirements: '3+ years frontend development',
    responsibilities:
      'Build React applications, implement UI/UX, optimize performance',
    featured: true,
    industry: 'Technology',
    occupational_category: 'Frontend Development',
  } as JobData,

  // Data Engineer from DataCorp
  dataEngineer: {
    sourcedAt: '2024-01-17T09:15:00Z',
    sourceUrl: 'https://datacorp.com/careers/data-engineer',
    title: 'Data Engineer - Cloud Infrastructure',
    company: 'DataCorp',
    type: 'Full-time',
    description: `We're looking for a Data Engineer to join our data platform team. You'll build and maintain the infrastructure that powers our analytics and machine learning capabilities.

Responsibilities:
• Design and implement scalable data pipelines
• Work with cloud platforms (AWS, GCP) for data processing
• Optimize data storage and retrieval systems
• Collaborate with data scientists and analysts
• Ensure data quality and reliability

Requirements:
• 4+ years of experience in data engineering
• Strong programming skills in Python and SQL
• Experience with cloud data platforms
• Knowledge of data pipeline tools (Airflow, Kafka, etc.)
• Understanding of data modeling and warehousing concepts`,
    apply_url: 'https://datacorp.com/careers/data-engineer/apply',
    apply_method: 'online',
    posted_date: '2024-01-17T07:30:00Z',
    status: 'active',
    salary_min: 110_000,
    salary_max: 160_000,
    salary_currency: 'USD',
    salary_unit: 'yearly',
    workplace_type: 'hybrid',
    remote_region: 'US',
    timezone_requirements: 'EST preferred',
    workplace_city: 'New York',
    workplace_country: 'United States',
    benefits: 'Health insurance, 401k, Stock options, Commuter benefits',
    application_requirements: 'Resume, Technical assessment',
    valid_through: '2024-02-28T23:59:59Z',
    job_identifier: 'DATA-DE-2024-003',
    job_source_name: 'DataCorp Careers',
    source_name: 'datacorp-careers',
    department: 'Data',
    travel_required: false,
    career_level: ['Mid-level', 'Senior'],
    visa_sponsorship: 'Available',
    languages: ['English'],
    skills: 'Python, SQL, AWS, GCP, Airflow, Kafka, Spark, Docker',
    qualifications: '4+ years data engineering, cloud platform experience',
    education_requirements: "Bachelor's in Computer Science or related field",
    experience_requirements: '4+ years in data engineering or related field',
    responsibilities:
      'Build data pipelines, optimize storage, ensure data quality',
    featured: false,
    industry: 'Technology',
    occupational_category: 'Data Engineering',
  } as JobData,
};

// Real metadata examples
export const REAL_METADATA_EXAMPLES = {
  seniorSoftwareEngineer: {
    id: 'meta-001',
    timestamp: '2024-01-15T10:30:15Z',
    filename: 'techcorp-senior-engineer.json',
    processing_time: 1250,
    cost_usd: 0.0012,
    tokens_input: 850,
    tokens_output: 420,
    tokens_total: 1270,
    model: 'gpt-4-turbo',
    source_content: 'HTML content from TechCorp careers page...',
  } as JobMetadata,

  remoteFrontendDeveloper: {
    id: 'meta-002',
    timestamp: '2024-01-16T14:20:30Z',
    filename: 'startupcorp-frontend-dev.json',
    processing_time: 980,
    cost_usd: 0.0008,
    tokens_input: 720,
    tokens_output: 380,
    tokens_total: 1100,
    model: 'gpt-4-turbo',
    source_content: 'JSON data from StartupCorp API...',
  } as JobMetadata,

  dataEngineer: {
    id: 'meta-003',
    timestamp: '2024-01-17T09:15:45Z',
    filename: 'datacorp-data-engineer.json',
    processing_time: 1450,
    cost_usd: 0.0015,
    tokens_input: 920,
    tokens_output: 480,
    tokens_total: 1400,
    model: 'gpt-4-turbo',
    source_content: 'Scraped content from DataCorp careers...',
  } as JobMetadata,
};

// Complete saved job examples
export const REAL_SAVED_JOB_EXAMPLES: SavedJob[] = [
  {
    job: REAL_JOB_EXAMPLES.seniorSoftwareEngineer,
    metadata: REAL_METADATA_EXAMPLES.seniorSoftwareEngineer,
  },
  {
    job: REAL_JOB_EXAMPLES.remoteFrontendDeveloper,
    metadata: REAL_METADATA_EXAMPLES.remoteFrontendDeveloper,
  },
  {
    job: REAL_JOB_EXAMPLES.dataEngineer,
    metadata: REAL_METADATA_EXAMPLES.dataEngineer,
  },
];

// Real API response examples
export const REAL_API_RESPONSES = {
  // Pipeline processing success response
  pipelineSuccess: {
    success: true,
    message: '✅ Pipeline processing completed successfully',
    summary: {
      total: 3,
      successful: 3,
      failed: 0,
      duplicates: 0,
    },
    results: [
      {
        jobId: 'job-001',
        status: 'success',
        extractedData: REAL_JOB_EXAMPLES.seniorSoftwareEngineer,
        metadata: {
          cost: 0.0012,
          duration: 1250,
          tokens: 1270,
        },
      },
      {
        jobId: 'job-002',
        status: 'success',
        extractedData: REAL_JOB_EXAMPLES.remoteFrontendDeveloper,
        metadata: {
          cost: 0.0008,
          duration: 980,
          tokens: 1100,
        },
      },
      {
        jobId: 'job-003',
        status: 'success',
        extractedData: REAL_JOB_EXAMPLES.dataEngineer,
        metadata: {
          cost: 0.0015,
          duration: 1450,
          tokens: 1400,
        },
      },
    ],
    errors: [],
    duplicates: [],
    metrics: {
      totalCost: 0.0035,
      totalDuration: 3680,
      averageDuration: 1227,
      totalTokens: 3770,
      averageTokens: 1257,
    },
    timestamp: '2024-01-17T10:00:00Z',
  },

  // Health check responses
  healthResponses: {
    system: {
      status: 'healthy',
      message: 'All systems operational',
      timestamp: '2024-01-17T10:00:00Z',
      version: '0.1.7',
      integrations: {
        supabase: 'connected',
        workflow: 'connected',
        openai: 'connected',
      },
    },
    pipeline: {
      message: 'Pipeline ingestion endpoint ready for processing',
      version: '0.1.7',
      features: [
        'Single job processing',
        'Batch job processing',
        'AI-powered extraction',
        'Duplicate detection',
        'Error handling',
      ],
      usage: {
        daily_processed: 127,
        monthly_processed: 3420,
        success_rate: 0.94,
      },
      timestamp: '2024-01-17T10:00:00Z',
    },
    webhooks: {
      service: 'Upstash Workflow Processing',
      status: 'healthy',
      architecture: 'serverless',
      features: [
        'Callback processing',
        'Signature verification',
        'Error handling',
        'Retry logic',
      ],
      environment: 'production',
      timestamp: '2024-01-17T10:00:00Z',
    },
  },

  // Error response examples
  errorResponses: {
    validationError: {
      error: 'Validation Error',
      message: "Invalid job data: missing required field 'sourceUrl'",
      details: {
        code: 'REQUIRED_FIELD_MISSING',
        field: 'sourceUrl',
        provided_fields: ['content', 'source'],
        required_fields: ['content', 'sourceUrl', 'source'],
      },
      timestamp: '2024-01-17T10:00:00Z',
    },
    authenticationError: {
      error: 'Unauthorized',
      message: 'Invalid QStash signature',
      details: {
        code: 'INVALID_SIGNATURE',
        component: 'authentication',
        hint: 'Ensure QStash signature is included in request headers',
      },
      timestamp: '2024-01-17T10:00:00Z',
    },
    processingError: {
      error: 'Processing Error',
      message: 'Failed to extract job data from provided URL',
      details: {
        code: 'EXTRACTION_FAILED',
        component: 'ai-processor',
        url: 'https://example.com/invalid-job-page',
        retryable: true,
        retry_count: 2,
      },
      timestamp: '2024-01-17T10:00:00Z',
    },
  },
};

// Real webhook payload examples
export const REAL_WEBHOOK_EXAMPLES = {
  qstashCallback: {
    messageId: 'msg_2Zv8X9kL3mN4pQ7rS1tU',
    status: 'success',
    url: 'https://bordfeed.com/api/pipeline-ingest',
    body: 'eyJzdWNjZXNzIjp0cnVlLCJtZXNzYWdlIjoiUGlwZWxpbmUgcHJvY2Vzc2luZyBjb21wbGV0ZWQgc3VjY2Vzc2Z1bGx5In0=', // Base64 encoded success response
    responseHeaders: {
      'content-type': 'application/json',
      'x-request-id': 'req_8F2k9L3mN4pQ7rS1tU5v',
    },
    statusCode: 200,
    retried: 0,
    timestamp: '2024-01-17T10:00:00Z',
  },

  workableWebhook: {
    event: 'job_published',
    data: {
      job: {
        id: 12_345,
        title: 'Senior Software Engineer',
        department: 'Engineering',
        location: 'San Francisco, CA',
        employment_type: 'full_time',
        experience: '5-7',
        published_at: '2024-01-15T08:00:00Z',
        updated_at: '2024-01-15T08:00:00Z',
        url: 'https://workable.com/jobs/12345',
      },
      company: {
        name: 'TechCorp',
        domain: 'techcorp.com',
      },
    },
    webhook_id: 'wh_9K2m3N4pQ7rS1tU5v8X',
    created_at: '2024-01-15T08:00:00Z',
  },

  wwrWebhook: {
    title: 'Frontend Developer - React Specialist',
    company: 'StartupCorp',
    location: 'Anywhere',
    category: 'Programming',
    job_type: 'Full-Time',
    url: 'https://weworkremotely.com/jobs/startupcorp-frontend-developer',
    description: REAL_JOB_EXAMPLES.remoteFrontendDeveloper.description,
    posted_at: '2024-01-16T12:00:00Z',
    salary_range: '$90k - $130k',
    tags: ['React', 'TypeScript', 'Remote', 'Frontend'],
  },

  jobdataWebhook: {
    job_id: 'jd_8F2k9L3mN4pQ7rS1tU5v',
    title: 'Data Engineer - Cloud Infrastructure',
    company: 'DataCorp',
    location: 'New York, NY',
    salary_range: '$110k - $160k',
    job_url: 'https://datacorp.com/careers/data-engineer',
    company_url: 'https://datacorp.com',
    posted_date: '2024-01-17T07:30:00Z',
    employment_type: 'FULL_TIME',
    seniority_level: 'MID_LEVEL',
    industry: 'Technology',
    skills: ['Python', 'SQL', 'AWS', 'GCP', 'Airflow', 'Kafka'],
    benefits: [
      'Health Insurance',
      '401k',
      'Stock Options',
      'Commuter Benefits',
    ],
  },
};
