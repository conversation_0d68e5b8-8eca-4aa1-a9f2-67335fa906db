// Example Job Board Configurations
// Demonstrates proper usage of constants for creating job board configs

import { CAREER_LEVELS } from '../../lib/career-levels';
import { CURRENCY_CODES } from '../../lib/data/currencies';
import { LANGUAGE_CODES } from '../../lib/data/languages';
import {
  DAILY_POSTING_LIMITS,
  JOB_BOARD_DEFAULTS,
  JOB_BOARD_TIMEZONES,
  JOB_SOURCES,
  POSTING_STRATEGIES,
  RECOMMENDED_POSTING_TIMES,
  REPOSTING_AVOIDANCE_DAYS,
} from '../../lib/job-board-constants';
import type { JobBoardConfig as JobBoardServiceConfig } from '../../lib/job-board-service';
import { VISA_SPONSORSHIP_OPTIONS } from '../../lib/job-status';
import { JOB_TYPES } from '../../lib/job-types';
import { REMOTE_REGIONS, WOR<PERSON><PERSON>ACE_TYPES } from '../../lib/workplace';

// Example configurations using proper constants
export const EXAMPLE_JOB_BOARD_CONFIGS: Omit<
  JobBoardServiceConfig,
  'lastPostedAt' | 'totalPosted'
>[] = [
  {
    id: 'remote-react-jobs',
    name: 'Remote React Jobs',
    description: 'React positions for remote developers',
    enabled: true,
    airtable: {
      baseId: '', // Configure in database
      tableName: 'Jobs',
    },
    filters: {
      types: [JOB_TYPES[0], JOB_TYPES[2]], // "Full-time", "Contract"
      workplaceTypes: [WORKPLACE_TYPES[2]], // "Remote"
      remoteRegions: [REMOTE_REGIONS[0], REMOTE_REGIONS[4], REMOTE_REGIONS[5]], // "Worldwide", "US Only", "EU Only"
      careerLevels: [
        CAREER_LEVELS[5], // "Mid Level"
        CAREER_LEVELS[6], // "Senior"
        CAREER_LEVELS[8], // "Lead"
        CAREER_LEVELS[9], // "Principal"
      ],
      includeKeywords: ['React', 'TypeScript', 'JavaScript', 'Frontend'],
      excludeKeywords: ['PHP', 'Ruby', 'Java', 'C++'],
      salaryMin: 80_000,
      salaryCurrencies: [
        CURRENCY_CODES[0], // "USD"
        CURRENCY_CODES[1], // "EUR"
        CURRENCY_CODES[3], // "GBP"
      ],
      sourcePriority: [JOB_SOURCES[0], JOB_SOURCES[1]], // "wwr_rss", "jobdata_api"
    },
    posting: {
      dailyLimit: DAILY_POSTING_LIMITS.STANDARD, // 10
      postingTimes: RECOMMENDED_POSTING_TIMES.GLOBAL_OPTIMAL, // ["09:00", "15:00"]
      timezone: JOB_BOARD_TIMEZONES[1], // "America/New_York"
      strategy: POSTING_STRATEGIES[0], // "newest_first"
      avoidRepostingDays: REPOSTING_AVOIDANCE_DAYS.STANDARD, // 30
    },
  },

  {
    id: 'senior-engineer-jobs',
    name: 'Senior Engineer Jobs',
    description: 'Senior+ engineering positions across all technologies',
    enabled: true,
    airtable: {
      baseId: process.env.AIRTABLE_SENIOR_JOBS_BASE_ID || '',
      tableName: 'Jobs',
    },
    filters: {
      types: [JOB_TYPES[0]], // "Full-time"
      careerLevels: [
        CAREER_LEVELS[6], // "Senior"
        CAREER_LEVELS[7], // "Staff"
        CAREER_LEVELS[8], // "Lead"
        CAREER_LEVELS[9], // "Principal"
        CAREER_LEVELS[10], // "Manager"
        CAREER_LEVELS[12], // "Director"
      ],
      salaryMin: 150_000,
      salaryCurrencies: [CURRENCY_CODES[0]], // "USD"
      visaSponsorship: VISA_SPONSORSHIP_OPTIONS[0], // "Yes"
      languages: [LANGUAGE_CODES[40]], // "en" (English)
    },
    posting: {
      dailyLimit: DAILY_POSTING_LIMITS.ACTIVE, // 15
      strategy: POSTING_STRATEGIES[1], // "best_match"
      avoidRepostingDays: REPOSTING_AVOIDANCE_DAYS.LONG, // 45
      timezone: JOB_BOARD_TIMEZONES[0], // "UTC"
    },
  },

  {
    id: 'data-science-remote',
    name: 'Remote Data Science Jobs',
    description: 'Data science and analytics positions',
    enabled: true,
    airtable: {
      baseId: process.env.AIRTABLE_DATA_SCIENCE_BASE_ID || '',
      tableName: 'Jobs',
    },
    filters: {
      types: [JOB_TYPES[0], JOB_TYPES[2]], // "Full-time", "Contract"
      workplaceTypes: [WORKPLACE_TYPES[2]], // "Remote"
      remoteRegions: [REMOTE_REGIONS[0]], // "Worldwide"
      careerLevels: [
        CAREER_LEVELS[4], // "Junior"
        CAREER_LEVELS[5], // "Mid Level"
        CAREER_LEVELS[6], // "Senior"
      ],
      includeKeywords: [
        'Python',
        'R',
        'SQL',
        'Data Science',
        'Machine Learning',
        'Analytics',
      ],
      excludeKeywords: ['Intern', 'Entry Level'],
      salaryMin: 70_000,
      salaryCurrencies: [CURRENCY_CODES[0], CURRENCY_CODES[1]], // "USD", "EUR"
      languages: [LANGUAGE_CODES[40]], // "en"
    },
    posting: {
      dailyLimit: DAILY_POSTING_LIMITS.MINIMAL, // 5 (more targeted niche)
      postingTimes: RECOMMENDED_POSTING_TIMES.US_MORNING, // ["09:00", "10:00", "11:00"]
      timezone: JOB_BOARD_TIMEZONES[4], // "America/Los_Angeles"
      strategy: POSTING_STRATEGIES[1], // "best_match"
      avoidRepostingDays: REPOSTING_AVOIDANCE_DAYS.STANDARD, // 30
    },
  },

  {
    id: 'freelance-dev-gigs',
    name: 'Freelance Developer Gigs',
    description: 'Contract and freelance development opportunities',
    enabled: false, // Disabled by default
    airtable: {
      baseId: process.env.AIRTABLE_FREELANCE_BASE_ID || '',
      tableName: 'Opportunities',
    },
    filters: {
      types: [JOB_TYPES[2], JOB_TYPES[3]], // "Contract", "Freelance"
      workplaceTypes: [WORKPLACE_TYPES[2]], // "Remote"
      careerLevels: [
        CAREER_LEVELS[5], // "Mid Level"
        CAREER_LEVELS[6], // "Senior"
      ],
      includeKeywords: ['Contract', 'Freelance', 'Project', 'Consulting'],
      salaryMin: 50, // $50/hour minimum
      salaryCurrencies: [CURRENCY_CODES[0]], // "USD"
    },
    posting: {
      dailyLimit: DAILY_POSTING_LIMITS.STANDARD, // 10
      strategy: POSTING_STRATEGIES[2], // "random"
      avoidRepostingDays: REPOSTING_AVOIDANCE_DAYS.SHORT, // 7 (faster turnover for gigs)
      timezone: JOB_BOARD_DEFAULTS.TIMEZONE, // "UTC"
    },
  },

  {
    id: 'fintech-opportunities',
    name: 'FinTech Opportunities',
    description: 'Financial technology positions',
    enabled: true,
    airtable: {
      baseId: process.env.AIRTABLE_FINTECH_BASE_ID || '',
      tableName: 'Positions',
    },
    filters: {
      types: [JOB_TYPES[0]], // "Full-time"
      includeKeywords: [
        'FinTech',
        'Banking',
        'Finance',
        'Payments',
        'Blockchain',
        'Crypto',
      ],
      excludeKeywords: ['Intern', 'Junior'],
      careerLevels: [
        CAREER_LEVELS[5], // "Mid Level"
        CAREER_LEVELS[6], // "Senior"
        CAREER_LEVELS[8], // "Lead"
      ],
      salaryMin: 100_000,
      salaryCurrencies: [CURRENCY_CODES[0], CURRENCY_CODES[3]], // "USD", "GBP"
      visaSponsorship: VISA_SPONSORSHIP_OPTIONS[0], // "Yes"
    },
    posting: {
      dailyLimit: DAILY_POSTING_LIMITS.STANDARD, // 10
      postingTimes: RECOMMENDED_POSTING_TIMES.GLOBAL_OPTIMAL, // ["09:00", "15:00"]
      timezone: JOB_BOARD_TIMEZONES[6], // "Europe/London"
      strategy: POSTING_STRATEGIES[1], // "best_match"
      avoidRepostingDays: REPOSTING_AVOIDANCE_DAYS.LONG, // 45
    },
  },
];

// Helper function to validate job board config uses proper constants
// biome-ignore lint/complexity/noExcessiveCognitiveComplexity: Complex validation logic with multiple conditions is necessary for config validation
export function validateJobBoardConfig(
  config: Partial<JobBoardServiceConfig>
): {
  valid: boolean;
  errors: string[];
} {
  const errors: string[] = [];

  // Validate posting strategy
  if (
    config.posting?.strategy &&
    // biome-ignore lint/suspicious/noExplicitAny: Strategy validation requires any type for dynamic checking
    !POSTING_STRATEGIES.includes(config.posting.strategy as any)
  ) {
    errors.push(
      `Invalid posting strategy: ${
        config.posting.strategy
      }. Must be one of: ${POSTING_STRATEGIES.join(', ')}`
    );
  }

  // Validate timezone
  if (
    config.posting?.timezone &&
    // biome-ignore lint/suspicious/noExplicitAny: Timezone validation requires any type for dynamic checking
    !JOB_BOARD_TIMEZONES.includes(config.posting.timezone as any)
  ) {
    errors.push(
      `Invalid timezone: ${
        config.posting.timezone
      }. Must be one of: ${JOB_BOARD_TIMEZONES.join(', ')}`
    );
  }

  // Validate job types in filters
  if (config.filters?.types) {
    const invalidTypes = config.filters.types.filter(
      // biome-ignore lint/suspicious/noExplicitAny: Type validation requires any for dynamic array checking
      (type) => !JOB_TYPES.includes(type as any)
    );
    if (invalidTypes.length > 0) {
      errors.push(
        `Invalid job types: ${invalidTypes.join(
          ', '
        )}. Must be from: ${JOB_TYPES.join(', ')}`
      );
    }
  }

  // Validate workplace types
  if (config.filters?.workplaceTypes) {
    const invalidWorkplaceTypes = config.filters.workplaceTypes.filter(
      // biome-ignore lint/suspicious/noExplicitAny: Workplace type validation requires any for dynamic array checking
      (type) => !WORKPLACE_TYPES.includes(type as any)
    );
    if (invalidWorkplaceTypes.length > 0) {
      errors.push(
        `Invalid workplace types: ${invalidWorkplaceTypes.join(
          ', '
        )}. Must be from: ${WORKPLACE_TYPES.join(', ')}`
      );
    }
  }

  // Validate career levels
  if (config.filters?.careerLevels) {
    const invalidLevels = config.filters.careerLevels.filter(
      // biome-ignore lint/suspicious/noExplicitAny: Career level validation requires any for dynamic array checking
      (level) => !CAREER_LEVELS.includes(level as any)
    );
    if (invalidLevels.length > 0) {
      errors.push(
        `Invalid career levels: ${invalidLevels.join(
          ', '
        )}. Must be from: ${CAREER_LEVELS.join(', ')}`
      );
    }
  }

  // Validate currencies
  if (config.filters?.salaryCurrencies) {
    const invalidCurrencies = config.filters.salaryCurrencies.filter(
      // biome-ignore lint/suspicious/noExplicitAny: Currency validation requires any for dynamic array checking
      (currency) => !CURRENCY_CODES.includes(currency as any)
    );
    if (invalidCurrencies.length > 0) {
      errors.push(
        `Invalid currencies: ${invalidCurrencies.join(
          ', '
        )}. Must be from: ${CURRENCY_CODES.join(', ')}`
      );
    }
  }

  return {
    valid: errors.length === 0,
    errors,
  };
}

// Example job data for testing board filters
// biome-ignore lint/suspicious/noExplicitAny: Example job data structure is flexible for testing various scenarios
export const exampleJobs: any[] = [];
