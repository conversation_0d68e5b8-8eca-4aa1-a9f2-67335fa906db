/**
 * Jobs API Tests
 *
 * Tests the /api/jobs endpoint including bulk processing functionality
 * Converted from bulk-process-api.test.mjs to use Node.js test runner
 */

import assert from 'node:assert/strict';
import test from 'node:test';
import { testEndpoint } from '../utils/openapi-validator.js';

// Type definitions for API responses
interface APIErrorResponse {
  error: string;
  details?: string;
}

interface APIJobsResponse {
  jobs?: Array<{ id: string; [key: string]: unknown }>;
  [key: string]: unknown;
}

interface APIResponse {
  success?: boolean;
  message?: string;
  error?: string;
  jobs?: Array<{ id: string; [key: string]: unknown }>;
  [key: string]: unknown;
}

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

test('Bulk Process API - Missing IDs array', async () => {
  const { response, data } = await testEndpoint(`${BASE_URL}/api/jobs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'bulkProcessNow',
      // Missing ids array
    }),
  });

  const errorData = data as APIErrorResponse;
  assert.equal(response.status, 400, 'Should return 400 for missing IDs');
  assert.equal(
    errorData.error,
    'ids array is required',
    'Should have correct error message'
  );
});

test('Bulk Process API - Empty IDs array', async () => {
  const { response, data } = await testEndpoint(`${BASE_URL}/api/jobs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'bulkProcessNow',
      ids: [],
    }),
  });

  const errorData = data as APIErrorResponse;
  assert.equal(response.status, 400, 'Should return 400 for empty IDs array');
  assert.equal(
    errorData.error,
    'ids array is required',
    'Should have correct error message'
  );
});

test('Bulk Process API - Invalid UUID format', async () => {
  const { response, data } = await testEndpoint(`${BASE_URL}/api/jobs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'bulkProcessNow',
      ids: ['invalid-uuid', 'another-invalid-uuid'],
    }),
  });

  const errorData = data as APIErrorResponse;
  assert.equal(response.status, 400, 'Should return 400 for invalid UUIDs');
  assert.ok(errorData.error, 'Should have error message');
});

test('Bulk Process API - Non-existent job IDs', async () => {
  const { response, data } = await testEndpoint(`${BASE_URL}/api/jobs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'bulkProcessNow',
      ids: [
        '550e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-************',
      ],
    }),
  });

  const errorData = data as APIErrorResponse;
  assert.equal(
    response.status,
    404,
    'Should return 404 for non-existent job IDs'
  );
  assert.equal(
    errorData.error,
    'No jobs found with provided IDs',
    'Should have correct error message'
  );
});

test('Bulk Process API - Response structure with real job IDs', async () => {
  // First, get some actual job IDs from the database
  const { response: jobsResponse, data: jobsData } = await testEndpoint(
    `${BASE_URL}/api/jobs?limit=2`,
    { method: 'GET' }
  );

  if (!jobsResponse.ok) {
    test.skip('Cannot fetch jobs for testing - database may be empty');
    return;
  }

  const jobsResponseData = jobsData as APIJobsResponse;
  const jobIds =
    jobsResponseData.jobs?.slice(0, 1).map((job: { id: string }) => job.id) ||
    [];

  if (jobIds.length === 0) {
    test.skip('No jobs found in database for testing');
    return;
  }

  // Test the bulk process endpoint with real job IDs
  const { data, validation } = await testEndpoint(`${BASE_URL}/api/jobs`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify({
      action: 'bulkProcessNow',
      ids: jobIds,
    }),
  });

  const responseData = data as APIResponse & {
    stats?: {
      total?: number;
      successful?: number;
      failed?: number;
    };
  };

  // Check response structure
  assert.ok(responseData.success !== undefined, 'Should have success field');
  assert.ok(responseData.message !== undefined, 'Should have message field');
  assert.ok(responseData.stats !== undefined, 'Should have stats object');
  assert.ok(responseData.stats?.total !== undefined, 'Should have stats.total');
  assert.ok(
    responseData.stats?.successful !== undefined,
    'Should have stats.successful'
  );
  assert.ok(
    responseData.stats?.failed !== undefined,
    'Should have stats.failed'
  );

  // Validate against OpenAPI schema
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );

  // Check if processing was attempted
  assert.equal(
    responseData.stats?.total,
    jobIds.length,
    'Should process all provided job IDs'
  );
});

test('Jobs API - GET endpoint basic functionality', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/jobs?limit=5`,
    { method: 'GET' }
  );

  const getResponseData = data as APIJobsResponse;
  assert.equal(response.status, 200, 'Should return 200 for GET request');
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );
  assert.ok(Array.isArray(getResponseData.jobs), 'Should return jobs array');
  assert.ok(
    getResponseData.jobs && getResponseData.jobs.length <= 5,
    'Should respect limit parameter'
  );
});

test('Jobs API - GET with pagination', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/jobs?limit=10&offset=0`,
    { method: 'GET' }
  );

  const paginatedResponseData = data as APIJobsResponse;
  assert.equal(response.status, 200, 'Should return 200 for paginated request');
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );
  assert.ok(
    Array.isArray(paginatedResponseData.jobs),
    'Should return jobs array'
  );
  assert.ok(
    paginatedResponseData.jobs && paginatedResponseData.jobs.length <= 10,
    'Should respect limit parameter'
  );
});
