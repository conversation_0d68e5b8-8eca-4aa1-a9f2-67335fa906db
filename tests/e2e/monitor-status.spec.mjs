import http from 'node:http';
import { expect, test } from '@playwright/test';

const MOCK_HTML =
  '<html><body><p>This job is no longer available</p></body></html>';

async function withMockServer(fn) {
  const server = http.createServer((_req, res) => {
    res.writeHead(200, { 'Content-Type': 'text/html' });
    res.end(MOCK_HTML);
  });
  await new Promise((resolve) => server.listen(0, resolve));
  const { port } = server.address();
  const url = `http://localhost:${port}`;
  try {
    await fn(url);
  } finally {
    server.close();
  }
}

test('monitor API responds when processing mocked closed job', async ({
  request,
}) => {
  if (!process.env.MONITOR_SECRET) {
    test.skip(true, 'MONITOR_SECRET not set');
  }
  await withMockServer(async (url) => {
    const res = await request.post(
      `/api/monitor?key=${process.env.MONITOR_SECRET}&limit=0`,
      {
        data: { mockUrl: url },
      }
    );
    expect(res.status()).toBeLessThan(500);
  });
});
