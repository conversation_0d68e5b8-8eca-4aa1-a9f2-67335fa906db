import { test, expect } from "@playwright/test";

test.describe("Notification Settings UI", () => {
  test.beforeEach(async ({ page }) => {
    // Navigate to the notifications settings page
    await page.goto("/dashboard/notifications");

    // Wait for the page to load
    await page.waitForLoadState("networkidle");
  });

  test("displays notification settings page correctly", async ({ page }) => {
    // Check page title and main heading
    await expect(
      page.locator("h1, h2, h3").filter({ hasText: "Slack Notifications" })
    ).toBeVisible();

    // Check that the table is present
    await expect(page.locator("table")).toBeVisible();

    // Check for key notification categories
    await expect(page.getByText("Critical")).toBeVisible();
    await expect(page.getByText("Warning")).toBeVisible();
    await expect(page.getByText("Info")).toBeVisible();
    await expect(page.getByText("Pipeline")).toBeVisible();
    await expect(page.getByText("Health")).toBeVisible();
    await expect(page.getByText("Jobs scraped")).toBeVisible();
    await expect(page.getByText("Jobs processed")).toBeVisible();
  });

  test("displays loading state initially", async ({ page }) => {
    // Reload page to catch loading state
    await page.reload();

    // Should show loading text briefly
    const loadingText = page.getByText("Loading…");

    // May or may not be visible depending on how fast the API responds
    // Just ensure it doesn't throw an error
    try {
      await expect(loadingText).toBeVisible({ timeout: 1000 });
    } catch {
      // Loading state may be too fast to catch, that's fine
    }
  });

  test("allows toggling notification categories", async ({ page }) => {
    // Wait for settings to load
    await page.waitForFunction(
      () => !document.querySelector('[data-testid="loading"]')
    );

    // Find critical notifications checkbox
    const criticalRow = page.locator("tr").filter({ hasText: "Critical" });
    const criticalCheckbox = criticalRow
      .locator('input[type="checkbox"]')
      .first();

    // Get initial state
    const initialChecked = await criticalCheckbox.isChecked();

    // Toggle the checkbox
    await criticalCheckbox.click();

    // Verify the state changed
    await expect(criticalCheckbox).toBeChecked({ checked: !initialChecked });

    // Test warning notifications
    const warningRow = page.locator("tr").filter({ hasText: "Warning" });
    const warningCheckbox = warningRow
      .locator('input[type="checkbox"]')
      .first();

    const warningInitialChecked = await warningCheckbox.isChecked();
    await warningCheckbox.click();
    await expect(warningCheckbox).toBeChecked({
      checked: !warningInitialChecked,
    });
  });

  test("allows configuring pipeline settings", async ({ page }) => {
    // Wait for settings to load
    await page.waitForFunction(
      () => !document.querySelector('[data-testid="loading"]')
    );

    // Find pipeline row
    const pipelineRow = page.locator("tr").filter({ hasText: "Pipeline" });

    // Enable pipeline notifications
    const pipelineCheckbox = pipelineRow
      .locator('input[type="checkbox"]')
      .first();
    await pipelineCheckbox.check();

    // Test "Only failures" checkbox
    const onlyFailuresCheckbox = pipelineRow
      .locator('input[type="checkbox"]')
      .nth(1);
    await onlyFailuresCheckbox.check();
    await expect(onlyFailuresCheckbox).toBeChecked();

    // Test pipeline steps input
    const stepsInput = pipelineRow.locator('input[type="text"]');
    await stepsInput.fill("SOURCED, DEDUPED, PROCESSED");
    await expect(stepsInput).toHaveValue("SOURCED, DEDUPED, PROCESSED");
  });

  test("allows configuring health cadence", async ({ page }) => {
    // Wait for settings to load
    await page.waitForFunction(
      () => !document.querySelector('[data-testid="loading"]')
    );

    // Find health row
    const healthRow = page.locator("tr").filter({ hasText: "Health" });

    // Find and interact with the health cadence select
    const healthSelect = healthRow.locator('select, [role="combobox"]').first();

    // If it's a custom select component, handle it differently
    if ((await healthSelect.getAttribute("role")) === "combobox") {
      await healthSelect.click();
      await page.getByText("Every 6h").click();
    } else {
      await healthSelect.selectOption("6h");
    }

    // Verify selection (this might need adjustment based on actual component)
    await expect(healthSelect).toContainText("6h");
  });

  test("allows configuring jobs processed settings", async ({ page }) => {
    // Wait for settings to load
    await page.waitForFunction(
      () => !document.querySelector('[data-testid="loading"]')
    );

    // Find jobs processed row
    const jobsProcessedRow = page
      .locator("tr")
      .filter({ hasText: "Jobs processed" });

    // Test "On complete" checkbox
    const onCompleteCheckbox = jobsProcessedRow
      .locator('input[type="checkbox"]')
      .first();
    await onCompleteCheckbox.check();
    await expect(onCompleteCheckbox).toBeChecked();

    // Test "Daily summary" checkbox
    const dailySummaryCheckbox = jobsProcessedRow
      .locator('input[type="checkbox"]')
      .nth(1);
    await dailySummaryCheckbox.check();
    await expect(dailySummaryCheckbox).toBeChecked();

    // Test time input
    const timeInput = jobsProcessedRow.locator('input[type="time"]');
    await timeInput.fill("15:30");
    await expect(timeInput).toHaveValue("15:30");
  });

  test("saves settings successfully", async ({ page }) => {
    // Wait for settings to load
    await page.waitForFunction(
      () => !document.querySelector('[data-testid="loading"]')
    );

    // Make some changes
    const criticalRow = page.locator("tr").filter({ hasText: "Critical" });
    const criticalCheckbox = criticalRow
      .locator('input[type="checkbox"]')
      .first();
    await criticalCheckbox.check();

    const warningRow = page.locator("tr").filter({ hasText: "Warning" });
    const warningCheckbox = warningRow
      .locator('input[type="checkbox"]')
      .first();
    await warningCheckbox.check();

    // Click save button
    const saveButton = page.getByRole("button", { name: "Save" });
    await saveButton.click();

    // Wait for save to complete (might show success message or just complete)
    await page.waitForTimeout(1000);

    // Reload page to verify settings were saved
    await page.reload();
    await page.waitForLoadState("networkidle");

    // Verify the settings persisted
    const criticalRowAfter = page.locator("tr").filter({ hasText: "Critical" });
    const criticalCheckboxAfter = criticalRowAfter
      .locator('input[type="checkbox"]')
      .first();
    await expect(criticalCheckboxAfter).toBeChecked();

    const warningRowAfter = page.locator("tr").filter({ hasText: "Warning" });
    const warningCheckboxAfter = warningRowAfter
      .locator('input[type="checkbox"]')
      .first();
    await expect(warningCheckboxAfter).toBeChecked();
  });

  test("handles disabled state when not configured", async ({ page }) => {
    // This test assumes Slack webhook is not configured
    // In that case, the UI should show disabled state

    // Wait for settings to load
    await page.waitForFunction(
      () => !document.querySelector('[data-testid="loading"]')
    );

    // Check if there's a disabled hint
    const disabledHint = page.getByText(
      /webhook.*not.*configured|not.*configured/i
    );

    if (await disabledHint.isVisible()) {
      // If disabled, all checkboxes should be disabled
      const checkboxes = page.locator('input[type="checkbox"]');
      const count = await checkboxes.count();

      for (let i = 0; i < count; i++) {
        await expect(checkboxes.nth(i)).toBeDisabled();
      }

      // Save button should be disabled
      const saveButton = page.getByRole("button", { name: "Save" });
      await expect(saveButton).toBeDisabled();
    }
  });

  test("validates pipeline steps input format", async ({ page }) => {
    // Wait for settings to load
    await page.waitForFunction(
      () => !document.querySelector('[data-testid="loading"]')
    );

    // Find pipeline row and enable pipeline notifications
    const pipelineRow = page.locator("tr").filter({ hasText: "Pipeline" });
    const pipelineCheckbox = pipelineRow
      .locator('input[type="checkbox"]')
      .first();
    await pipelineCheckbox.check();

    // Test valid pipeline steps format
    const stepsInput = pipelineRow.locator('input[placeholder*="SOURCED"]');

    // Test various formats
    await stepsInput.fill("SOURCED, PROCESSED");
    await expect(stepsInput).toHaveValue("SOURCED, PROCESSED");

    await stepsInput.fill("SOURCED,DEDUPED,PROCESSED");
    await expect(stepsInput).toHaveValue("SOURCED,DEDUPED,PROCESSED");

    await stepsInput.fill("");
    await expect(stepsInput).toHaveValue("");
  });

  test("navigation from sidebar works", async ({ page }) => {
    // Start from dashboard home
    await page.goto("/dashboard");

    // Find and click the Notifications link in sidebar
    const notificationsLink = page.getByRole("link", {
      name: /notifications/i,
    });
    await notificationsLink.click();

    // Should navigate to notifications page
    await expect(page).toHaveURL("/dashboard/notifications");
    await expect(
      page.locator("h1, h2, h3").filter({ hasText: "Slack Notifications" })
    ).toBeVisible();
  });

  test("responsive design works on mobile", async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 375, height: 667 });

    // Navigate to notifications page
    await page.goto("/dashboard/notifications");
    await page.waitForLoadState("networkidle");

    // Check that table is still usable on mobile
    await expect(page.locator("table")).toBeVisible();

    // Check that critical notifications are still accessible
    const criticalRow = page.locator("tr").filter({ hasText: "Critical" });
    await expect(criticalRow).toBeVisible();

    // Checkbox should still be clickable
    const criticalCheckbox = criticalRow
      .locator('input[type="checkbox"]')
      .first();
    await expect(criticalCheckbox).toBeVisible();
  });
});
