import { afterEach, beforeEach, describe, expect, it } from 'vitest';
import { invalidateNotificationSettingsCache } from '@/lib/notification-settings';
import { createServiceRoleClient } from '@/lib/supabase';

describe('Notification Settings API Integration', () => {
  const supabase = createServiceRoleClient();

  beforeEach(async () => {
    // Clean up any existing settings
    await supabase.from('notification_settings').delete().eq('id', 'global');

    // Clear cache
    invalidateNotificationSettingsCache();
  });

  afterEach(async () => {
    // Clean up after tests
    await supabase.from('notification_settings').delete().eq('id', 'global');

    invalidateNotificationSettingsCache();
  });

  describe('GET /api/notifications/settings', () => {
    it('returns default settings when no custom settings exist', async () => {
      const response = await fetch('/api/notifications/settings');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data).toEqual({
        configured: expect.any(Boolean),
        flags: {
          enableCritical: expect.any(Boolean),
          enableAlert: expect.any(Boolean),
          enableInfo: expect.any(Boolean),
          enablePipeline: expect.any(Boolean),
          pipelineOnlyFailures: expect.any(Boolean),
          pipelineSteps: expect.any(Array),
        },
        frequency: {
          healthCadence: expect.any(String),
          jobsScrapedOnComplete: expect.any(Boolean),
          jobsProcessedOnComplete: expect.any(Boolean),
          jobsProcessedDaily: expect.any(Boolean),
          jobsProcessedDailyTime: expect.any(String),
        },
      });

      // Verify default values match what we expect
      expect(data.flags.enableCritical).toBe(true); // Should default to true
      expect(data.frequency.healthCadence).toBe('1h');
      expect(data.frequency.jobsProcessedDailyTime).toBe('09:00');
    });

    it('returns custom settings when they exist in database', async () => {
      // Insert custom settings
      const customSettings = {
        enableCritical: false,
        enableAlert: true,
        enableInfo: true,
        enablePipeline: false,
        pipelineOnlyFailures: false,
        pipelineSteps: ['SOURCED', 'PROCESSED'],
        healthCadence: '6h',
        jobsScrapedOnComplete: false,
        jobsProcessedOnComplete: true,
        jobsProcessedDaily: false,
        jobsProcessedDailyTime: '15:30',
      };

      await supabase.from('notification_settings').insert({
        id: 'global',
        settings: customSettings,
        updated_at: new Date().toISOString(),
      });

      // Clear cache to force fresh fetch
      invalidateNotificationSettingsCache();

      const response = await fetch('/api/notifications/settings');
      const data = await response.json();

      expect(response.ok).toBe(true);
      expect(data.flags.enableCritical).toBe(false);
      expect(data.flags.enableAlert).toBe(true);
      expect(data.flags.enableInfo).toBe(true);
      expect(data.flags.pipelineSteps).toEqual(['SOURCED', 'PROCESSED']);
      expect(data.frequency.healthCadence).toBe('6h');
      expect(data.frequency.jobsProcessedDailyTime).toBe('15:30');
    });
  });

  describe('PUT /api/notifications/settings', () => {
    it('creates new settings when none exist', async () => {
      const newSettings = {
        enableCritical: false,
        enableAlert: true,
        enableInfo: false,
        enablePipeline: true,
        pipelineOnlyFailures: true,
        pipelineSteps: ['DEDUPED'],
        healthCadence: '24h',
        jobsScrapedOnComplete: true,
        jobsProcessedOnComplete: false,
        jobsProcessedDaily: true,
        jobsProcessedDailyTime: '12:00',
      };

      const response = await fetch('/api/notifications/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newSettings),
      });

      const data = await response.json();
      expect(response.ok).toBe(true);
      expect(data.success).toBe(true);
      expect(data.message).toBe('Settings saved successfully');

      // Verify settings were saved to database
      const { data: dbData } = await supabase
        .from('notification_settings')
        .select('settings')
        .eq('id', 'global')
        .single();

      expect(dbData?.settings).toMatchObject(newSettings);
    });

    it('updates existing settings', async () => {
      // Insert initial settings
      const initialSettings = {
        enableCritical: true,
        enableAlert: false,
        healthCadence: '1h',
      };

      await supabase.from('notification_settings').insert({
        id: 'global',
        settings: initialSettings,
        updated_at: new Date().toISOString(),
      });

      // Update with new settings
      const updatedSettings = {
        enableCritical: false,
        enableAlert: true,
        healthCadence: '6h',
        enableInfo: true,
      };

      const response = await fetch('/api/notifications/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(updatedSettings),
      });

      const data = await response.json();
      expect(response.ok).toBe(true);
      expect(data.success).toBe(true);

      // Verify settings were updated in database
      const { data: dbData } = await supabase
        .from('notification_settings')
        .select('settings')
        .eq('id', 'global')
        .single();

      expect(dbData?.settings).toMatchObject(updatedSettings);
    });

    it('handles partial updates correctly', async () => {
      // Insert initial settings
      await supabase.from('notification_settings').insert({
        id: 'global',
        settings: {
          enableCritical: true,
          enableAlert: false,
          enableInfo: false,
          healthCadence: '1h',
        },
        updated_at: new Date().toISOString(),
      });

      // Update only some fields
      const partialUpdate = {
        enableAlert: true,
        healthCadence: '24h',
      };

      const response = await fetch('/api/notifications/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(partialUpdate),
      });

      expect(response.ok).toBe(true);

      // Verify partial update worked and other fields remained
      const { data: dbData } = await supabase
        .from('notification_settings')
        .select('settings')
        .eq('id', 'global')
        .single();

      expect(dbData?.settings.enableCritical).toBe(true); // Unchanged
      expect(dbData?.settings.enableAlert).toBe(true); // Updated
      expect(dbData?.settings.enableInfo).toBe(false); // Unchanged
      expect(dbData?.settings.healthCadence).toBe('24h'); // Updated
    });

    it('invalidates cache after update', async () => {
      // Insert initial settings
      await supabase.from('notification_settings').insert({
        id: 'global',
        settings: { enableCritical: true },
        updated_at: new Date().toISOString(),
      });

      // Fetch to populate cache
      await fetch('/api/notifications/settings');

      // Update settings
      await fetch('/api/notifications/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ enableCritical: false }),
      });

      // Fetch again - should get updated settings, not cached ones
      const response = await fetch('/api/notifications/settings');
      const data = await response.json();

      expect(data.flags.enableCritical).toBe(false);
    });

    it('handles invalid JSON gracefully', async () => {
      const response = await fetch('/api/notifications/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: 'invalid json{',
      });

      expect(response.ok).toBe(false);
      expect(response.status).toBe(500);

      const data = await response.json();
      expect(data.success).toBe(false);
      expect(data.error).toBe('Internal server error');
    });

    it('handles database errors gracefully', async () => {
      // Mock a database error by using invalid data
      const response = await fetch('/api/notifications/settings', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          // This might cause a constraint violation depending on schema
          id: 'invalid-id-format-that-might-cause-error',
        }),
      });

      // Should handle the error gracefully
      const data = await response.json();

      if (!response.ok) {
        expect(data.success).toBe(false);
        expect(data.error).toBeDefined();
      }
    });
  });

  describe('Cache behavior', () => {
    it('serves cached settings within TTL', async () => {
      // Insert settings
      await supabase.from('notification_settings').insert({
        id: 'global',
        settings: { enableCritical: true },
        updated_at: new Date().toISOString(),
      });

      // First fetch
      const response1 = await fetch('/api/notifications/settings');
      const data1 = await response1.json();
      expect(data1.flags.enableCritical).toBe(true);

      // Update database directly (bypassing cache invalidation)
      await supabase
        .from('notification_settings')
        .update({ settings: { enableCritical: false } })
        .eq('id', 'global');

      // Second fetch should still return cached value
      const response2 = await fetch('/api/notifications/settings');
      const data2 = await response2.json();
      expect(data2.flags.enableCritical).toBe(true); // Still cached

      // After cache invalidation, should get updated value
      invalidateNotificationSettingsCache();
      const response3 = await fetch('/api/notifications/settings');
      const data3 = await response3.json();
      expect(data3.flags.enableCritical).toBe(false); // Updated
    });
  });
});
