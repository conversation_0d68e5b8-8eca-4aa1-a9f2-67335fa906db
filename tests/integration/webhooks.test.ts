/**
 * Webhook Integration Tests
 *
 * Tests webhook endpoints and their integration with external services
 * Converted from webhook-callback-production-test.mjs
 */

import assert from 'node:assert/strict';
import test from 'node:test';
import { testEndpoint } from '../utils/openapi-validator.js';

const BASE_URL = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3000';

// Type definitions for webhook API responses
interface WebhookHealthResponse {
  status?: string;
  message?: string;
  timestamp?: string;
  [key: string]: unknown;
}

// Note: WebhookCallbackResponse, WebhookErrorResponse, and WebhookStatusResponse
// interfaces were removed as they are not currently used in the webhook tests.
// They can be re-added when specific webhook callback functionality is tested.

test('Webhook Callback Endpoint Health', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/webhook-callbacks`,
    {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    }
  );

  assert.equal(
    response.status,
    200,
    'Webhook callback endpoint should be healthy'
  );
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );

  // Check for expected features
  const healthData = data as WebhookHealthResponse & { features?: string[] };
  const hasCallbackFeatures =
    healthData.features?.includes('✅ Success callback handling') &&
    healthData.features?.includes('✅ Workflow processing');

  assert.ok(hasCallbackFeatures, 'Should have callback handling features');
});

test('Recent Job Processing Check', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/jobs?limit=5&sort=created_at&order=desc`,
    {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    }
  );

  assert.equal(response.status, 200, 'Jobs endpoint should be accessible');
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );

  const jobsData = data as { jobs?: Array<{ source_name?: string }> };
  const hasRecentJobs = (jobsData.jobs?.length ?? 0) > 0;
  if (hasRecentJobs) {
    const recentJobWithSource = jobsData.jobs?.some(
      (job: { source_name?: string }) =>
        ['jobdata_api', 'workable', 'WeWorkRemotely'].includes(
          job.source_name || ''
        )
    );

    // This is informational - not a hard failure
    if (!recentJobWithSource) {
      // No recent jobs from expected sources - this is informational only
    }
  }

  // Just verify the endpoint works - don't fail if no jobs exist
  const jobsResponseData = data as { jobs?: unknown[] };
  assert.ok(Array.isArray(jobsResponseData.jobs), 'Should return jobs array');
});

test('Workable Webhook Configuration', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/workable-webhook`,
    {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    }
  );

  assert.equal(response.status, 200, 'Workable webhook should be accessible');
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );

  const configData = data as {
    features?: string[];
    bestPractices?: { workflowIntegration?: string };
  };
  const hasCallbackFeatures =
    configData.features?.some((f: string) => f.includes('callback')) ||
    configData.bestPractices?.workflowIntegration?.includes('callback');

  assert.ok(hasCallbackFeatures, 'Should have callback configuration');
});

test('WeWorkRemotely Webhook Configuration', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/wwr-webhook`,
    {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    }
  );

  assert.equal(response.status, 200, 'WWR webhook should be accessible');
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );

  const wwrData = data as {
    features?: string[];
    bestPractices?: { workflowIntegration?: string };
  };
  const hasCallbackFeatures =
    wwrData.features?.some((f: string) => f.includes('callback')) ||
    wwrData.bestPractices?.workflowIntegration?.includes('callback');

  assert.ok(hasCallbackFeatures, 'Should have callback configuration');
});

test('JobData API Webhook Configuration', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/jobdata-webhook`,
    {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    }
  );

  assert.equal(response.status, 200, 'JobData webhook should be accessible');
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );

  const jobDataWebhookData = data as {
    features?: string[];
    bestPractices?: { workflowIntegration?: string };
  };
  const hasCallbackFeatures =
    jobDataWebhookData.features?.some((f: string) => f.includes('callback')) ||
    jobDataWebhookData.bestPractices?.workflowIntegration?.includes('callback');

  assert.ok(hasCallbackFeatures, 'Should have callback configuration');
});

test('Health Check Endpoint', async () => {
  const { response, data, validation } = await testEndpoint(
    `${BASE_URL}/api/health`,
    {
      method: 'GET',
      headers: { 'User-Agent': 'Bordfeed-Test/1.0' },
    }
  );

  const healthCheckData = data as WebhookHealthResponse;
  assert.equal(response.status, 200, 'Health endpoint should return 200');
  assert.equal(
    validation.valid,
    true,
    `Schema validation failed: ${validation.errors.join(', ')}`
  );
  assert.ok(healthCheckData.status, 'Should have status field');
});

// Pipeline ingestion test removed - endpoint migrated to Workflow architecture
