import { type NextRequest, NextResponse } from 'next/server';
import {
  buildJob<PERSON>ilterQuery,
  EXAMPLE_FILTERS,
  type JobFilters,
  tagJob,
} from '../../../lib/tagging-engine';
import type { JobData } from '../../../lib/types';

/**
 * Check career level match
 */
function checkCareerLevel(job: JobData, filters: JobFilters): boolean {
  if (!filters.careerLevels?.length) {
    return true;
  }

  const hasMatchingLevel = job.career_level?.some((level) =>
    filters.careerLevels?.includes(level)
  );

  if (!hasMatchingLevel) {
    return false;
  }

  return true;
}

/**
 * Check workplace type match
 */
function checkWorkplaceType(job: JobData, filters: JobFilters): boolean {
  if (!filters.workplaceTypes?.length) {
    return true;
  }

  if (
    !(job.workplace_type && filters.workplaceTypes.includes(job.workplace_type))
  ) {
    return false;
  }

  return true;
}

/**
 * Check salary requirements
 */
function checkSalary(job: JobD<PERSON>, filters: JobFilters): boolean {
  if (!filters.salaryMin) {
    return true;
  }

  if (!job.salary_min || job.salary_min < filters.salaryMin) {
    return false;
  }

  return true;
}

/**
 * Check department match
 */
function checkDepartment(job: JobData, filters: JobFilters): boolean {
  if (!filters.departments?.length) {
    return true;
  }

  if (!(job.department && filters.departments.includes(job.department))) {
    return false;
  }

  return true;
}

/**
 * Check include keywords
 */
function checkIncludeKeywords(job: JobData, filters: JobFilters): boolean {
  if (!filters.includeKeywords?.length) {
    return true;
  }

  const jobText = `${job.title} ${job.description}`.toLowerCase();
  const hasMatchingKeyword = filters.includeKeywords.some((keyword) =>
    jobText.includes(keyword.toLowerCase())
  );

  if (!hasMatchingKeyword) {
    return false;
  }

  return true;
}

/**
 * Check exclude keywords
 */
function checkExcludeKeywords(job: JobData, filters: JobFilters): boolean {
  if (!filters.excludeKeywords?.length) {
    return true;
  }

  const jobText = `${job.title} ${job.description}`.toLowerCase();
  const hasExcludedKeyword = filters.excludeKeywords.some((keyword) =>
    jobText.includes(keyword.toLowerCase())
  );

  if (hasExcludedKeyword) {
    return false;
  }

  return true;
}

/**
 * Check if a job would match the given filters (simplified logic for demo)
 */
function checkJobMatchesFilter(job: JobData, filters: JobFilters): boolean {
  return (
    checkCareerLevel(job, filters) &&
    checkWorkplaceType(job, filters) &&
    checkSalary(job, filters) &&
    checkDepartment(job, filters) &&
    checkIncludeKeywords(job, filters) &&
    checkExcludeKeywords(job, filters)
  );
}

/**
 * Test endpoint to demonstrate the tagging system
 *
 * Shows how jobs get tagged and how those tags can be used for filtering
 */
export async function POST(request: NextRequest) {
  try {
    const { job, testFilter } = await request.json();

    if (!job) {
      return NextResponse.json({ error: 'Job data required' }, { status: 400 });
    }

    // Tag the job with our new approach (only 3 derived categories)
    const { job: taggedJob, tags } = await tagJob(job as JobData);

    // If testFilter is provided, show how filtering would work
    let filterDemo:
      | {
          filterName: unknown;
          appliedFilters: JobFilters;
          generatedSQL: { whereClause: string; paramCount: number };
          wouldMatch: boolean;
        }
      | undefined;

    if (testFilter) {
      const filters =
        EXAMPLE_FILTERS[testFilter as keyof typeof EXAMPLE_FILTERS] ||
        testFilter;
      const { whereClause, params } = buildJobFilterQuery(filters);

      filterDemo = {
        filterName: testFilter,
        appliedFilters: filters,
        generatedSQL: {
          whereClause,
          paramCount: params.length,
        },
        wouldMatch: checkJobMatchesFilter(job, filters),
      };
    }

    // Build response showing the new approach
    const response = {
      success: true,
      originalJob: job,
      taggedJob,
      tags,
      taggingDetails: {
        totalTags: tags.length,
        // Old hardcoded arrays are now empty (cleaned up!)
        techStackTags: [],
        seniorityTags: [],
        workTypeTags: [],
        industryTags: [],
        domainTags: [],
        frameworkTags: [],
        benefitTags: [],
        categorizedTags: {
          technologies: [],
          levels: [],
          workArrangement: [],
        },
      },
      newApproach: {
        usesDatabaseConstants: true,
        usesTextSearch: true,
        noHardcodedPatterns: true,
        scalesToAllIndustries: true,
      },
      ...(filterDemo && { filterDemo }),
    };

    return NextResponse.json(response);
  } catch (error) {
    // Use logger instead of console.error for production code
    return NextResponse.json(
      {
        error: 'Failed to tag job',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

export function GET() {
  return NextResponse.json({
    message: 'Job tagging test endpoint',
    examples: EXAMPLE_FILTERS,
  });
}
