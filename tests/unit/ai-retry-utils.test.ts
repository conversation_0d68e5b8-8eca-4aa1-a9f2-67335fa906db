import assert from 'node:assert/strict';
import test from 'node:test';
import { DEFAULT_AI_RETRY_OPTIONS } from '../../lib/retry-utils';

test('DEFAULT_AI_RETRY_OPTIONS has expected configuration', () => {
  assert.ok(
    typeof DEFAULT_AI_RETRY_OPTIONS === 'object',
    'Should be an object'
  );
  assert.ok(
    typeof DEFAULT_AI_RETRY_OPTIONS.maxAttempts === 'number',
    'Should have maxAttempts'
  );
  assert.ok(
    typeof DEFAULT_AI_RETRY_OPTIONS.baseDelay === 'number',
    'Should have baseDelay'
  );
  assert.ok(
    DEFAULT_AI_RETRY_OPTIONS.maxAttempts > 1,
    'Should allow multiple attempts'
  );
  assert.ok(
    DEFAULT_AI_RETRY_OPTIONS.baseDelay > 0,
    'Should have positive delay'
  );
});

test('DEFAULT_AI_RETRY_OPTIONS has reasonable values', () => {
  // Test that the retry configuration is reasonable for AI operations
  assert.ok(
    DEFAULT_AI_RETRY_OPTIONS.maxAttempts <= 5,
    'Should not retry too many times'
  );
  assert.ok(
    DEFAULT_AI_RETRY_OPTIONS.maxAttempts >= 2,
    'Should retry at least once'
  );
  assert.ok(
    DEFAULT_AI_RETRY_OPTIONS.baseDelay <= 5000,
    'Should not have excessive delay'
  );
  assert.ok(
    DEFAULT_AI_RETRY_OPTIONS.baseDelay >= 100,
    'Should have meaningful delay'
  );
});

test('DEFAULT_AI_RETRY_OPTIONS includes exponential backoff', () => {
  // Check if exponential backoff is configured
  if ('exponentialBackoff' in DEFAULT_AI_RETRY_OPTIONS) {
    assert.ok(
      typeof DEFAULT_AI_RETRY_OPTIONS.exponentialBackoff === 'boolean',
      'exponentialBackoff should be boolean if present'
    );
  }
});

test('DEFAULT_AI_RETRY_OPTIONS includes jitter option', () => {
  // Check if jitter is configured
  if ('jitter' in DEFAULT_AI_RETRY_OPTIONS) {
    assert.ok(
      typeof DEFAULT_AI_RETRY_OPTIONS.jitter === 'boolean',
      'jitter should be boolean if present'
    );
  }
});

test('retry configuration is suitable for AI operations', () => {
  // AI operations typically need:
  // - Multiple attempts due to rate limits
  // - Reasonable delays to avoid overwhelming the service
  // - Not too many attempts to avoid long waits

  const { maxAttempts, baseDelay } = DEFAULT_AI_RETRY_OPTIONS;

  // Should retry enough times for transient failures
  assert.ok(maxAttempts >= 2, 'Should allow retries for transient failures');

  // Should not retry excessively
  assert.ok(maxAttempts <= 10, 'Should not retry excessively');

  // Should have reasonable delay for AI services
  assert.ok(baseDelay >= 50, 'Should have meaningful delay for AI services');
  assert.ok(baseDelay <= 10_000, 'Should not have excessive delay');
});
