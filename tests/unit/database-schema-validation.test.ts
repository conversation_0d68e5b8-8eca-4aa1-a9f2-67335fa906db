import assert from 'node:assert/strict';
import test from 'node:test';
import { APPLY_METHODS, type ApplyMethod } from '../../lib/apply-methods.js';
import { CAREER_LEVELS } from '../../lib/career-levels.js';
import { CURRENCY_CODES } from '../../lib/data/currencies.js';
import { LANGUAGE_CODES } from '../../lib/data/languages.js';
import {
  JOB_BOARD_STATUSES,
  JOB_SOURCES,
  POSTING_STRATEGIES,
} from '../../lib/job-board-constants.js';
// Import actual production schemas and constants
import { JobExtractionSchema, JobSchema } from '../../lib/job-schema.js';
import {
  JOB_STATUSES,
  type JobStatus,
  VISA_SPONSORSHIP_OPTIONS,
} from '../../lib/job-status.js';
import { JOB_TYPES, type JobType } from '../../lib/job-types.js';
import {
  type JobValidationResult,
  validateJob,
} from '../../lib/job-validation.js';
import { MONITOR_STATUSES } from '../../lib/monitor-schema.js';
import { SALARY_UNITS, type SalaryUnit } from '../../lib/salary-units.js';
import { REMOTE_REGIONS, WORKPLACE_TYPES } from '../../lib/workplace.js';

// Test data using actual schema constants
const createValidJobExtractionData = (
  overrides: Record<string, unknown> = {}
) => ({
  title: 'Senior Software Engineer',
  description:
    'We are looking for a senior software engineer to join our team...',
  status: 'active' as JobStatus,
  company: 'TechCorp Inc.',
  type: 'Full-time' as JobType,
  apply_url: 'https://techcorp.com/careers/senior-engineer',
  apply_method: 'link' as ApplyMethod,
  posted_date: '2024-01-15T08:00:00.000Z',
  salary_min: 120_000,
  salary_max: 180_000,
  salary_currency: 'USD',
  salary_unit: 'year' as SalaryUnit,
  workplace_type: 'Remote', // Using correct capitalized value
  remote_region: 'Worldwide', // Using correct capitalized value
  timezone_requirements: 'UTC-8 to UTC+2',
  workplace_city: null,
  workplace_country: null,
  benefits: 'Health insurance, 401k, unlimited PTO',
  application_requirements:
    "Bachelor's degree in Computer Science or equivalent experience",
  valid_through: '2024-03-15T23:59:59.000Z',
  job_identifier: 'TECH-2024-001',
  job_source_name: 'TechCorp Careers',
  department: 'Engineering',
  travel_required: false,
  career_level: ['Senior'],
  visa_sponsorship: 'No',
  languages: ['en'],
  skills: 'JavaScript, TypeScript, React, Node.js, PostgreSQL',
  qualifications: '5+ years of full-stack development experience',
  education_requirements:
    "Bachelor's degree in Computer Science or related field",
  experience_requirements: '5+ years of professional software development',
  responsibilities:
    'Design and implement scalable web applications, mentor junior developers',
  featured: false,
  industry: 'Technology',
  occupational_category: 'Software Development',
  ...overrides,
});

const createValidJobData = (overrides: Record<string, unknown> = {}) => ({
  sourcedAt: '2024-01-15T10:30:00.000Z',
  sourceUrl: 'https://techcorp.com/careers/senior-engineer',
  ...createValidJobExtractionData(overrides),
});

// Schema Validation Tests
test('JobExtractionSchema validates complete valid job data', () => {
  const validJob = createValidJobExtractionData();

  try {
    const result = JobExtractionSchema.parse(validJob);
    assert.ok(result, 'Should parse valid job data');
    assert.equal(result.title, 'Senior Software Engineer');
    assert.equal(result.company, 'TechCorp Inc.');
    assert.equal(result.status, 'active');
    assert.equal(result.type, 'Full-time');
    assert.equal(result.salary_min, 120_000);
    assert.equal(result.salary_max, 180_000);
  } catch (error) {
    assert.fail(`Valid job data should not throw: ${error}`);
  }
});

test('JobExtractionSchema validates minimal required fields', () => {
  // Create minimal job with all required fields set to null for nullable fields
  const minimalJob = {
    title: 'Software Engineer',
    description: 'A software engineering role',
    status: 'active' as JobStatus,
    // All nullable fields must be explicitly set to null
    company: null,
    type: null,
    apply_url: null,
    apply_method: null,
    posted_date: null,
    salary_min: null,
    salary_max: null,
    salary_currency: null,
    salary_unit: null,
    workplace_type: null,
    remote_region: null,
    timezone_requirements: null,
    workplace_city: null,
    workplace_country: null,
    benefits: null,
    application_requirements: null,
    valid_through: null,
    job_identifier: null,
    job_source_name: null,
    department: null,
    travel_required: null,
    career_level: null,
    visa_sponsorship: null,
    languages: null,
    skills: null,
    qualifications: null,
    education_requirements: null,
    experience_requirements: null,
    responsibilities: null,
    featured: null,
    industry: null,
    occupational_category: null,
  };

  try {
    const result = JobExtractionSchema.parse(minimalJob);
    assert.ok(result, 'Should parse minimal job data');
    assert.equal(result.title, 'Software Engineer');
    assert.equal(result.description, 'A software engineering role');
    assert.equal(result.status, 'active');
  } catch (error) {
    assert.fail(`Minimal job data should not throw: ${error}`);
  }
});

test('JobExtractionSchema rejects invalid data', () => {
  const invalidJobs = [
    { description: 'Missing title' }, // No title
    { title: 'Test', description: '' }, // Empty description
    { title: 'Test', description: 'Valid', salary_min: -1000 }, // Negative salary
    { title: 'Test', description: 'Valid', salary_currency: 'INVALID' }, // Invalid currency
    { title: 'Test', description: 'Valid', type: 'Invalid Type' }, // Invalid job type
  ];

  for (const invalidJob of invalidJobs) {
    try {
      JobExtractionSchema.parse(invalidJob);
      assert.fail(
        `Should throw for invalid job data: ${JSON.stringify(invalidJob)}`
      );
    } catch (error) {
      assert.ok(error, 'Should throw validation error');
    }
  }
});

test('JobSchema validates complete job with metadata', () => {
  const validJob = createValidJobData();

  try {
    const result = JobSchema.parse(validJob);
    assert.ok(result, 'Should parse valid job data with metadata');
    assert.equal(result.sourcedAt, '2024-01-15T10:30:00.000Z');
    assert.equal(
      result.sourceUrl,
      'https://techcorp.com/careers/senior-engineer'
    );
    assert.equal(result.title, 'Senior Software Engineer');
  } catch (error) {
    assert.fail(`Valid job data with metadata should not throw: ${error}`);
  }
});

// Constants Validation Tests
test('JOB_STATUSES contains expected values', () => {
  const expectedStatuses = [
    'draft',
    'active',
    'inactive',
    'expired',
    'filled',
    'cancelled',
  ];

  for (const status of expectedStatuses) {
    assert.ok(
      JOB_STATUSES.includes(status as JobStatus),
      `Should include status: ${status}`
    );
  }

  assert.ok(
    JOB_STATUSES.length >= expectedStatuses.length,
    'Should have at least expected statuses'
  );
});

test('JOB_TYPES contains expected values', () => {
  const expectedTypes = [
    'Full-time',
    'Part-time',
    'Contract',
    'Freelance',
    'Internship',
  ];

  for (const type of expectedTypes) {
    assert.ok(
      JOB_TYPES.includes(type as JobType),
      `Should include type: ${type}`
    );
  }

  assert.ok(
    JOB_TYPES.length >= expectedTypes.length,
    'Should have at least expected types'
  );
});

test('APPLY_METHODS contains expected values', () => {
  const expectedMethods = [
    'link',
    'email',
    'phone',
    'form',
    'platform',
    'other',
  ];

  for (const method of expectedMethods) {
    assert.ok(
      APPLY_METHODS.includes(method as ApplyMethod),
      `Should include method: ${method}`
    );
  }

  assert.ok(
    APPLY_METHODS.length >= expectedMethods.length,
    'Should have at least expected methods'
  );
});

test('SALARY_UNITS contains expected values', () => {
  const expectedUnits = ['hour', 'day', 'week', 'month', 'year', 'project'];

  for (const unit of expectedUnits) {
    assert.ok(
      SALARY_UNITS.includes(unit as SalaryUnit),
      `Should include unit: ${unit}`
    );
  }

  assert.ok(
    SALARY_UNITS.length >= expectedUnits.length,
    'Should have at least expected units'
  );
});

test('VISA_SPONSORSHIP_OPTIONS contains expected values', () => {
  const expectedOptions = ['Yes', 'No', 'Not specified'];

  for (const option of expectedOptions) {
    assert.ok(
      VISA_SPONSORSHIP_OPTIONS.includes(option),
      `Should include option: ${option}`
    );
  }

  assert.equal(
    VISA_SPONSORSHIP_OPTIONS.length,
    expectedOptions.length,
    'Should have exactly expected options'
  );
});

test('CAREER_LEVELS contains expected values', () => {
  assert.ok(Array.isArray(CAREER_LEVELS), 'Should be an array');
  assert.ok(CAREER_LEVELS.length > 0, 'Should have career levels');

  // Test that common career levels exist
  const commonLevels = ['Junior', 'Mid Level', 'Senior'];
  for (const level of commonLevels) {
    assert.ok(
      CAREER_LEVELS.includes(level),
      `Should include career level: ${level}`
    );
  }
});

test('WORKPLACE_TYPES contains expected values', () => {
  assert.ok(Array.isArray(WORKPLACE_TYPES), 'Should be an array');
  assert.ok(WORKPLACE_TYPES.length > 0, 'Should have workplace types');

  // Test that common workplace types exist (using correct capitalized values)
  const commonTypes = ['Remote', 'On-site', 'Hybrid'];
  for (const type of commonTypes) {
    assert.ok(
      WORKPLACE_TYPES.includes(type),
      `Should include workplace type: ${type}`
    );
  }
});

test('REMOTE_REGIONS contains expected values', () => {
  assert.ok(Array.isArray(REMOTE_REGIONS), 'Should be an array');
  assert.ok(REMOTE_REGIONS.length > 0, 'Should have remote regions');

  // Test that common regions exist (using correct capitalized values)
  const commonRegions = ['Worldwide', 'US Only', 'EU Only'];
  for (const region of commonRegions) {
    assert.ok(
      REMOTE_REGIONS.includes(region),
      `Should include remote region: ${region}`
    );
  }
});

test('CURRENCY_CODES contains expected values', () => {
  assert.ok(Array.isArray(CURRENCY_CODES), 'Should be an array');
  assert.ok(CURRENCY_CODES.length > 0, 'Should have currency codes');

  // Test that common currencies exist
  const commonCurrencies = ['USD', 'EUR', 'GBP'];
  for (const currency of commonCurrencies) {
    assert.ok(
      CURRENCY_CODES.includes(currency),
      `Should include currency: ${currency}`
    );
  }
});

test('LANGUAGE_CODES contains expected values', () => {
  assert.ok(Array.isArray(LANGUAGE_CODES), 'Should be an array');
  assert.ok(LANGUAGE_CODES.length > 0, 'Should have language codes');

  // Test that common languages exist
  const commonLanguages = ['en', 'es', 'fr', 'de'];
  for (const language of commonLanguages) {
    assert.ok(
      LANGUAGE_CODES.includes(language),
      `Should include language: ${language}`
    );
  }
});

// Job Board Constants Tests
test('POSTING_STRATEGIES contains expected values', () => {
  const expectedStrategies = ['newest_first', 'best_match', 'random'];

  for (const strategy of expectedStrategies) {
    assert.ok(
      POSTING_STRATEGIES.includes(strategy),
      `Should include strategy: ${strategy}`
    );
  }

  assert.equal(
    POSTING_STRATEGIES.length,
    expectedStrategies.length,
    'Should have exactly expected strategies'
  );
});

test('JOB_SOURCES contains expected values', () => {
  const expectedSources = [
    'wwr_rss',
    'jobdata_api',
    'workable',
    'manual_entry',
  ];

  for (const source of expectedSources) {
    assert.ok(JOB_SOURCES.includes(source), `Should include source: ${source}`);
  }

  assert.ok(
    JOB_SOURCES.length >= expectedSources.length,
    'Should have at least expected sources'
  );
});

test('JOB_BOARD_STATUSES contains expected values', () => {
  const expectedStatuses = ['active', 'paused', 'disabled', 'testing'];

  for (const status of expectedStatuses) {
    assert.ok(
      JOB_BOARD_STATUSES.includes(status),
      `Should include status: ${status}`
    );
  }

  assert.equal(
    JOB_BOARD_STATUSES.length,
    expectedStatuses.length,
    'Should have exactly expected statuses'
  );
});

test('MONITOR_STATUSES contains expected values', () => {
  const expectedStatuses = ['active', 'closed', 'filled', 'unknown'];

  for (const status of expectedStatuses) {
    assert.ok(
      MONITOR_STATUSES.includes(status),
      `Should include status: ${status}`
    );
  }

  assert.equal(
    MONITOR_STATUSES.length,
    expectedStatuses.length,
    'Should have exactly expected statuses'
  );
});

// Job Validation Function Tests
test('validateJob function validates job data correctly', () => {
  const validJobData = {
    title: 'Software Engineer',
    company: 'TechCorp',
    description: 'A great job opportunity',
    sourceUrl: 'https://example.com/job',
  };

  const result = validateJob(validJobData);

  assert.equal(typeof result.isValid, 'boolean');
  assert.ok(Array.isArray(result.missingFields));
  assert.equal(typeof result.externalId, 'string');
  assert.ok(result.externalId.length > 0, 'Should generate external ID');
});

test('validateJob identifies missing required fields', () => {
  const invalidJobData = {
    // Missing title and description
    company: 'TechCorp',
    sourceUrl: 'https://example.com/job',
  };

  const result = validateJob(invalidJobData);

  assert.equal(result.isValid, false);
  assert.ok(result.missingFields.length > 0, 'Should identify missing fields');
  assert.ok(result.externalId.length > 0, 'Should still generate external ID');
});

// Schema Integration Tests
test('Job data flows through validation pipeline correctly', () => {
  const jobData = createValidJobExtractionData();

  // 1. Validate with Zod schema
  const schemaResult = JobExtractionSchema.safeParse(jobData);
  assert.ok(schemaResult.success, 'Should pass Zod validation');

  // 2. Validate with custom validation function
  const validationResult = validateJob(jobData);
  assert.equal(validationResult.isValid, true, 'Should pass custom validation');
  assert.equal(
    validationResult.missingFields.length,
    0,
    'Should have no missing fields'
  );

  // 3. Check that external ID is generated
  assert.ok(
    validationResult.externalId.length > 0,
    'Should generate external ID'
  );
});

test('Invalid job data is properly rejected', () => {
  const invalidJobData = {
    title: '', // Empty title
    description: 'Valid description',
    salary_min: -1000, // Invalid salary
    type: 'Invalid Type', // Invalid job type
  };

  // 1. Should fail Zod validation
  const schemaResult = JobExtractionSchema.safeParse(invalidJobData);
  assert.equal(schemaResult.success, false, 'Should fail Zod validation');

  // 2. Custom validation should also identify issues
  const validationResult = validateJob(invalidJobData);
  assert.equal(
    validationResult.isValid,
    false,
    'Should fail custom validation'
  );
});
