import assert from 'node:assert/strict';
import test from 'node:test';
import { AI_CONFIG } from '../../lib/constants.js';
import {
  isJobExpired,
  JOB_STATUSES,
  STATUS_DISPLAY,
  VISA_SPONSORSHIP_OPTIONS,
} from '../../lib/job-status.js';
// Import actual production code
import {
  JobStatusClassifierSchema,
  MONITOR_STATUSES,
  type MonitorStatus,
} from '../../lib/monitor-schema.js';
// import { getMonitoringUrl } from "../../lib/monitor.js"; // Commented out to avoid Supabase dependencies
import { JOB_STATUS_CLASSIFIER_PROMPT } from '../../lib/prompts.js';

// Define types locally to avoid Supabase dependencies
interface JobRow {
  id: string;
  status: string | null;
  last_checked_at: string | null;
  monitor_attempts: number;
  next_try_at: string | null;
  source_url: string;
  description: string;
}

interface SelectJobsOptions {
  limit?: number;
  lockMinutes?: number;
}

interface PipelineResult {
  jobId: string;
  headStatus: number;
  headOk: boolean;
  status: MonitorStatus;
  confidence: number;
  metadata: {
    duration: number;
    inputTokens?: number;
    outputTokens?: number;
    totalTokens?: number;
    model: string;
  };
  decisionLayer: 'head' | 'phrase' | 'ai';
  error?: string;
}

// Test implementations to avoid Supabase dependencies
function testIsValidHttpUrl(url: string | null | undefined): boolean {
  if (!url) {
    return false;
  }
  return url.startsWith('http://') || url.startsWith('https://');
}

function testGetMonitoringUrl(job: {
  apply_url?: string | null;
  source_url?: string | null;
  apply_email?: string | null;
}): string | null {
  // Skip email-only applications that have no valid URLs
  if (
    job.apply_email &&
    !job.apply_url &&
    !testIsValidHttpUrl(job.source_url)
  ) {
    return null; // Skip monitoring for email-only jobs
  }

  // Check apply_url first, but skip if it's a mailto link
  if (job.apply_url && testIsValidHttpUrl(job.apply_url)) {
    return job.apply_url;
  }

  // Fallback to source_url if apply_url is invalid/mailto/null
  if (job.source_url && testIsValidHttpUrl(job.source_url)) {
    return job.source_url;
  }

  // No valid URLs found
  return null;
}

function testMatchClosedPhrase(
  snippet: string,
  phrases: string[]
): string | false {
  if (!snippet || phrases.length === 0) {
    return false;
  }

  const normalizedSnippet = snippet.toLowerCase();

  for (const phrase of phrases) {
    if (normalizedSnippet.includes(phrase.toLowerCase())) {
      return phrase;
    }
  }

  return false;
}

function testProcessSnippetContent(content: string): {
  wordCount: number;
  hasStructuredData: boolean;
  relevanceScore: number;
} {
  // Remove HTML tags for word counting
  const textContent = content.replace(/<[^>]*>/g, ' ');
  const words = textContent
    .trim()
    .split(/\s+/)
    .filter((word) => word.length > 0);
  const wordCount = words.length;

  // Check for structured job data indicators
  const structureIndicators = [
    'job title',
    'position',
    'requirements',
    'responsibilities',
    'qualifications',
    'experience',
    'skills',
    'salary',
    'benefits',
    'apply',
    'application',
    'deadline',
    'location',
  ];

  const hasStructuredData = structureIndicators.some((indicator) =>
    textContent.toLowerCase().includes(indicator)
  );

  // Calculate relevance score based on length and structure
  let relevanceScore = Math.min(wordCount / 100, 1); // Base score from word count
  if (hasStructuredData) {
    relevanceScore += 0.3; // Bonus for structured data
  }
  relevanceScore = Math.min(relevanceScore, 1); // Cap at 1.0

  return { wordCount, hasStructuredData, relevanceScore };
}

// Test data using real schema types
const createTestJobRow = (overrides: Partial<JobRow> = {}): JobRow => ({
  id: 'test-job-123',
  status: 'active',
  last_checked_at: '2024-01-15T10:30:00.000Z',
  monitor_attempts: 0,
  next_try_at: null,
  source_url: 'https://example.com/job/123',
  description: 'Software Engineer position at TechCorp',
  ...overrides,
});

const createTestMonitoringJob = (overrides: Record<string, unknown> = {}) => ({
  id: 'test-job-456',
  source_url: 'https://example.com/job/456',
  apply_url: 'https://example.com/apply/456',
  apply_email: null,
  description: 'Senior Developer role',
  status: 'active',
  ...overrides,
});

const createTestPipelineResult = (
  overrides: Partial<PipelineResult> = {}
): PipelineResult => ({
  jobId: 'test-job-789',
  headStatus: 200,
  headOk: true,
  status: 'active' as MonitorStatus,
  confidence: 0.85,
  metadata: {
    duration: 1500,
    inputTokens: 100,
    outputTokens: 50,
    totalTokens: 150,
    model: AI_CONFIG.MODEL,
  },
  decisionLayer: 'ai',
  ...overrides,
});

// Real Production Schema Tests
test('MONITOR_STATUSES contains expected values', () => {
  const expectedStatuses: MonitorStatus[] = [
    'active',
    'closed',
    'filled',
    'unknown',
  ];

  assert.equal(
    MONITOR_STATUSES.length,
    expectedStatuses.length,
    'Should have exactly 4 monitor statuses'
  );

  for (const status of expectedStatuses) {
    assert.ok(
      MONITOR_STATUSES.includes(status),
      `Should include status: ${status}`
    );
  }
});

test('JobStatusClassifierSchema validates AI responses', () => {
  const validResponse = {
    status: 'closed' as MonitorStatus,
    confidence: 0.92,
  };

  try {
    const result = JobStatusClassifierSchema.parse(validResponse);
    assert.equal(result.status, 'closed', 'Should parse status correctly');
    assert.equal(result.confidence, 0.92, 'Should parse confidence correctly');
  } catch (error) {
    assert.fail(`Valid response should not throw: ${error}`);
  }
});

test('JobStatusClassifierSchema rejects invalid responses', () => {
  const invalidResponses = [
    { status: 'invalid_status', confidence: 0.5 }, // Invalid status
    { status: 'active', confidence: 1.5 }, // Confidence > 1
    { status: 'closed', confidence: -0.1 }, // Confidence < 0
    { status: 'active' }, // Missing confidence
    { confidence: 0.8 }, // Missing status
  ];

  for (const invalidResponse of invalidResponses) {
    try {
      JobStatusClassifierSchema.parse(invalidResponse);
      assert.fail(
        `Should reject invalid response: ${JSON.stringify(invalidResponse)}`
      );
    } catch (error) {
      assert.ok(error, 'Should throw validation error');
    }
  }
});

test('MonitorStatus type matches schema enum', () => {
  // Test that TypeScript type matches runtime schema
  const testStatuses: MonitorStatus[] = [
    'active',
    'closed',
    'filled',
    'unknown',
  ];

  for (const status of testStatuses) {
    const testResponse = { status, confidence: 0.5 };

    try {
      const result = JobStatusClassifierSchema.parse(testResponse);
      assert.equal(result.status, status, `Should accept status: ${status}`);
    } catch (error) {
      assert.fail(`Status ${status} should be valid: ${error}`);
    }
  }
});

// Real Monitoring URL Selection Tests
test('testGetMonitoringUrl prioritizes apply_url over source_url', () => {
  const job = createTestMonitoringJob({
    apply_url: 'https://company.com/apply/123',
    source_url: 'https://jobboard.com/job/123',
  });

  const result = testGetMonitoringUrl(job);
  assert.equal(
    result,
    'https://company.com/apply/123',
    'Should prioritize apply_url'
  );
});

test('testGetMonitoringUrl falls back to source_url when apply_url is null', () => {
  const job = createTestMonitoringJob({
    apply_url: null,
    source_url: 'https://jobboard.com/job/123',
  });

  const result = testGetMonitoringUrl(job);
  assert.equal(
    result,
    'https://jobboard.com/job/123',
    'Should fall back to source_url'
  );
});

test('testGetMonitoringUrl returns null for email-only jobs', () => {
  const job = createTestMonitoringJob({
    apply_url: null,
    source_url: null,
    apply_email: '<EMAIL>',
  });

  const result = testGetMonitoringUrl(job);
  assert.equal(result, null, 'Should return null for email-only jobs');
});

test('testGetMonitoringUrl skips mailto URLs and uses source_url', () => {
  const job = createTestMonitoringJob({
    apply_url: 'mailto:<EMAIL>',
    source_url: 'https://jobboard.com/job/123',
  });

  const result = testGetMonitoringUrl(job);
  assert.equal(
    result,
    'https://jobboard.com/job/123',
    'Should skip mailto and use source_url'
  );
});

test('testGetMonitoringUrl returns null when no valid URLs exist', () => {
  const job = createTestMonitoringJob({
    apply_url: 'mailto:<EMAIL>',
    source_url: 'ftp://invalid.com/job',
    apply_email: '<EMAIL>',
  });

  const result = testGetMonitoringUrl(job);
  assert.equal(
    result,
    null,
    'Should return null when no valid HTTP URLs exist'
  );
});

test('testGetMonitoringUrl handles empty strings as invalid URLs', () => {
  const job = createTestMonitoringJob({
    apply_url: '',
    source_url: '',
  });

  const result = testGetMonitoringUrl(job);
  assert.equal(result, null, 'Should treat empty strings as invalid URLs');
});

test('testGetMonitoringUrl handles mixed valid/invalid URLs', () => {
  const job = createTestMonitoringJob({
    apply_url: 'invalid-url',
    source_url: 'https://valid.com/job',
  });

  const result = testGetMonitoringUrl(job);
  assert.equal(
    result,
    'https://valid.com/job',
    'Should find valid URL among invalid ones'
  );
});

// Real Job Status Tests
test('JOB_STATUSES contains expected values', () => {
  const expectedStatuses = [
    'draft',
    'active',
    'inactive',
    'expired',
    'filled',
    'cancelled',
  ];

  assert.equal(
    JOB_STATUSES.length,
    expectedStatuses.length,
    'Should have exactly 6 job statuses'
  );

  for (const status of expectedStatuses) {
    assert.ok(
      JOB_STATUSES.includes(status),
      `Should include status: ${status}`
    );
  }
});

test('STATUS_DISPLAY provides configuration for all statuses', () => {
  for (const status of JOB_STATUSES) {
    assert.ok(
      status in STATUS_DISPLAY,
      `Should have display config for: ${status}`
    );
    assert.ok(
      typeof STATUS_DISPLAY[status].label === 'string',
      `Should have label for: ${status}`
    );
    assert.ok(
      typeof STATUS_DISPLAY[status].color === 'string',
      `Should have color for: ${status}`
    );
    assert.ok(
      STATUS_DISPLAY[status].label.length > 0,
      `Label should not be empty for: ${status}`
    );
    assert.ok(
      STATUS_DISPLAY[status].color.length > 0,
      `Color should not be empty for: ${status}`
    );
  }
});

test('isJobExpired correctly identifies expired jobs', () => {
  const expiredDate = '2023-12-01T00:00:00.000Z'; // Past date
  const futureDate = '2025-12-01T00:00:00.000Z'; // Future date

  assert.equal(isJobExpired(expiredDate), true, 'Should identify expired job');
  assert.equal(isJobExpired(futureDate), false, 'Should identify active job');
  assert.equal(isJobExpired(null), false, 'Should handle null dates');
});

test('VISA_SPONSORSHIP_OPTIONS contains expected values', () => {
  const expectedOptions = ['Yes', 'No', 'Not specified'];

  assert.equal(
    VISA_SPONSORSHIP_OPTIONS.length,
    expectedOptions.length,
    'Should have exactly 3 visa options'
  );

  for (const option of expectedOptions) {
    assert.ok(
      VISA_SPONSORSHIP_OPTIONS.includes(option),
      `Should include option: ${option}`
    );
  }
});

// Real Prompt Tests
test('JOB_STATUS_CLASSIFIER_PROMPT contains required elements', () => {
  assert.ok(
    JOB_STATUS_CLASSIFIER_PROMPT.includes('job status classifier'),
    'Should contain classifier description'
  );
  assert.ok(
    JOB_STATUS_CLASSIFIER_PROMPT.includes('active | closed | filled | unknown'),
    'Should list valid statuses'
  );
  assert.ok(
    JOB_STATUS_CLASSIFIER_PROMPT.includes('confidence'),
    'Should mention confidence scoring'
  );
  assert.ok(
    JOB_STATUS_CLASSIFIER_PROMPT.includes('{snippet}'),
    'Should have snippet placeholder'
  );
});

test('JOB_STATUS_CLASSIFIER_PROMPT includes valid status options', () => {
  for (const status of MONITOR_STATUSES) {
    assert.ok(
      JOB_STATUS_CLASSIFIER_PROMPT.includes(status),
      `Should include status: ${status}`
    );
  }
});

// Real Data Structure Tests
test('JobRow interface matches expected structure', () => {
  const jobRow = createTestJobRow();

  // Test required fields
  assert.ok(typeof jobRow.id === 'string', 'Should have string id');
  assert.ok(
    typeof jobRow.source_url === 'string',
    'Should have string source_url'
  );
  assert.ok(
    typeof jobRow.description === 'string',
    'Should have string description'
  );
  assert.ok(
    typeof jobRow.monitor_attempts === 'number',
    'Should have number monitor_attempts'
  );

  // Test nullable fields
  assert.ok(
    jobRow.status === null || typeof jobRow.status === 'string',
    'Status should be string or null'
  );
  assert.ok(
    jobRow.last_checked_at === null ||
      typeof jobRow.last_checked_at === 'string',
    'last_checked_at should be string or null'
  );
  assert.ok(
    jobRow.next_try_at === null || typeof jobRow.next_try_at === 'string',
    'next_try_at should be string or null'
  );
});

test('PipelineResult interface matches expected structure', () => {
  const result = createTestPipelineResult();

  // Test required fields
  assert.ok(typeof result.jobId === 'string', 'Should have string jobId');
  assert.ok(
    typeof result.headStatus === 'number',
    'Should have number headStatus'
  );
  assert.ok(typeof result.headOk === 'boolean', 'Should have boolean headOk');
  assert.ok(
    typeof result.confidence === 'number',
    'Should have number confidence'
  );
  assert.ok(typeof result.metadata === 'object', 'Should have object metadata');
  assert.ok(
    typeof result.decisionLayer === 'string',
    'Should have string decisionLayer'
  );

  // Test status is valid MonitorStatus
  assert.ok(
    MONITOR_STATUSES.includes(result.status),
    'Status should be valid MonitorStatus'
  );

  // Test confidence range
  assert.ok(
    result.confidence >= 0 && result.confidence <= 1,
    'Confidence should be between 0 and 1'
  );
});

test('SelectJobsOptions interface has reasonable defaults', () => {
  const defaultOptions: SelectJobsOptions = {};

  // Test that interface allows empty object (all fields optional)
  assert.ok(typeof defaultOptions === 'object', 'Should accept empty options');

  // Test with explicit values
  const explicitOptions: SelectJobsOptions = {
    limit: 50,
    lockMinutes: 15,
  };

  assert.equal(explicitOptions.limit, 50, 'Should accept limit option');
  assert.equal(
    explicitOptions.lockMinutes,
    15,
    'Should accept lockMinutes option'
  );
});

// Real AI Configuration Tests
test('AI_CONFIG contains monitoring configuration', () => {
  assert.ok(
    typeof AI_CONFIG.MODEL === 'string',
    'Should have model configuration'
  );
  assert.ok(AI_CONFIG.MODEL.length > 0, 'Should have non-empty model name');
});

// Real Utility Function Tests
test('testIsValidHttpUrl validates HTTP URLs correctly', () => {
  const validUrls = [
    'http://example.com',
    'https://example.com',
    'https://example.com/path',
    'http://subdomain.example.com',
  ];

  const invalidUrls = [
    null,
    undefined,
    '',
    'ftp://example.com',
    'mailto:<EMAIL>',
    "javascript:alert('xss')",
    'not-a-url',
  ];

  for (const url of validUrls) {
    assert.equal(testIsValidHttpUrl(url), true, `Should validate URL: ${url}`);
  }

  for (const url of invalidUrls) {
    assert.equal(testIsValidHttpUrl(url), false, `Should reject URL: ${url}`);
  }
});

test('testMatchClosedPhrase detects exact phrase matches', () => {
  const snippet =
    'This position has been filled and is no longer accepting applications.';
  const phrases = [
    'position has been filled',
    'no longer accepting',
    'applications closed',
  ];

  const result = testMatchClosedPhrase(snippet, phrases);
  assert.equal(
    result,
    'position has been filled',
    'Should find first matching phrase'
  );
});

test('testMatchClosedPhrase is case insensitive', () => {
  const snippet = 'POSITION HAS BEEN FILLED';
  const phrases = ['position has been filled'];

  const result = testMatchClosedPhrase(snippet, phrases);
  assert.equal(
    result,
    'position has been filled',
    'Should match regardless of case'
  );
});

test('testMatchClosedPhrase returns false for open job snippets', () => {
  const snippet =
    'We are actively hiring for this position. Please apply today!';
  const phrases = [
    'position has been filled',
    'no longer accepting',
    'applications closed',
  ];

  const result = testMatchClosedPhrase(snippet, phrases);
  assert.equal(result, false, 'Should not match open job snippets');
});

test('testMatchClosedPhrase handles empty snippet', () => {
  const snippet = '';
  const phrases = ['position has been filled'];

  const result = testMatchClosedPhrase(snippet, phrases);
  assert.equal(result, false, 'Should handle empty snippet');
});

test('testMatchClosedPhrase handles empty phrases array', () => {
  const snippet = 'Some job content';
  const phrases: string[] = [];

  const result = testMatchClosedPhrase(snippet, phrases);
  assert.equal(result, false, 'Should handle empty phrases array');
});

test('testProcessSnippetContent removes HTML tags', () => {
  const htmlContent =
    '<h1>Job Title</h1><p>Job description with <strong>requirements</strong></p>';
  const result = testProcessSnippetContent(htmlContent);

  assert.ok(result.wordCount > 0, 'Should count words after removing HTML');
  assert.ok(result.hasStructuredData, 'Should detect structured job data');
});

test('testProcessSnippetContent counts words correctly', () => {
  const content =
    'Software Engineer position at TechCorp with competitive salary';
  const result = testProcessSnippetContent(content);

  assert.equal(result.wordCount, 8, 'Should count words correctly');
});

test('testProcessSnippetContent detects structured job data', () => {
  const structuredContent =
    'Job Title: Software Engineer. Requirements: 5+ years experience. Salary: $100k';
  const unstructuredContent =
    'Lorem ipsum dolor sit amet consectetur adipiscing elit';

  const structuredResult = testProcessSnippetContent(structuredContent);
  const unstructuredResult = testProcessSnippetContent(unstructuredContent);

  assert.equal(
    structuredResult.hasStructuredData,
    true,
    'Should detect structured data'
  );
  assert.equal(
    unstructuredResult.hasStructuredData,
    false,
    'Should not detect structure in random text'
  );
});

test('testProcessSnippetContent estimates relevance based on length and structure', () => {
  const shortContent = 'Job available';
  const longStructuredContent =
    'Software Engineer position with requirements qualifications experience skills salary benefits application deadline location'.repeat(
      10
    );

  const shortResult = testProcessSnippetContent(shortContent);
  const longResult = testProcessSnippetContent(longStructuredContent);

  assert.ok(
    shortResult.relevanceScore < longResult.relevanceScore,
    'Longer structured content should have higher relevance'
  );
  assert.ok(
    longResult.relevanceScore <= 1,
    'Relevance score should not exceed 1'
  );
});
