import assert from 'node:assert/strict';
import test from 'node:test';

// Import actual production code
import { JobExtractionSchema } from '../../lib/job-schema.js';
import { SCHEMA_LIMITS, INPUT_LIMITS } from '../../lib/constants.js';

// Test data for breaking points
const createOversizedContent = (field: string, multiplier: number = 2) => {
  const limit = SCHEMA_LIMITS[field as keyof typeof SCHEMA_LIMITS] || 1000;
  return 'A'.repeat(limit * multiplier);
};

const createValidBaseJob = () => ({
  title: 'Software Engineer',
  description: 'A software engineering role',
  status: 'active' as const,
  company: 'TechCorp',
  type: 'Full-time' as const,
  apply_url: 'https://example.com/apply',
  apply_method: 'link' as const,
  posted_date: '2024-01-15T08:00:00.000Z',
  salary_min: 50000,
  salary_max: 100000,
  salary_currency: 'USD' as const,
  salary_unit: 'year' as const,
  workplace_type: 'Remote' as const,
});

// Character Limit Breaking Points
test('Benefits field character limit breaking point', () => {
  const baseJob = createValidBaseJob();
  
  // Test at limit (should pass)
  const atLimit = {
    ...baseJob,
    benefits: 'A'.repeat(SCHEMA_LIMITS.benefits),
  };
  
  try {
    const result = JobExtractionSchema.parse(atLimit);
    assert.ok(result, 'Should parse job at character limit');
  } catch (error) {
    assert.fail(`Should parse at limit: ${error}`);
  }
  
  // Test over limit (should fail)
  const overLimit = {
    ...baseJob,
    benefits: 'A'.repeat(SCHEMA_LIMITS.benefits + 1),
  };
  
  try {
    JobExtractionSchema.parse(overLimit);
    assert.fail('Should reject job over character limit');
  } catch (error) {
    assert.ok(error, 'Should throw validation error for oversized benefits');
  }
});

test('Responsibilities field character limit breaking point', () => {
  const baseJob = createValidBaseJob();
  
  // Test at limit
  const atLimit = {
    ...baseJob,
    responsibilities: 'A'.repeat(SCHEMA_LIMITS.responsibilities),
  };
  
  try {
    JobExtractionSchema.parse(atLimit);
    assert.ok(true, 'Should parse at responsibilities limit');
  } catch (error) {
    assert.fail(`Should parse at limit: ${error}`);
  }
  
  // Test over limit
  const overLimit = {
    ...baseJob,
    responsibilities: 'A'.repeat(SCHEMA_LIMITS.responsibilities + 1),
  };
  
  try {
    JobExtractionSchema.parse(overLimit);
    assert.fail('Should reject oversized responsibilities');
  } catch (error) {
    assert.ok(error, 'Should throw validation error');
  }
});

test('Skills field character limit breaking point', () => {
  const baseJob = createValidBaseJob();
  
  const overLimit = {
    ...baseJob,
    skills: 'A'.repeat(SCHEMA_LIMITS.skills + 1),
  };
  
  try {
    JobExtractionSchema.parse(overLimit);
    assert.fail('Should reject oversized skills');
  } catch (error) {
    assert.ok(error, 'Should throw validation error for oversized skills');
  }
});

// Salary Breaking Points
test('Salary validation breaking points', () => {
  const baseJob = createValidBaseJob();
  
  // Negative salary (should fail)
  const negativeSalary = {
    ...baseJob,
    salary_min: -1000,
  };
  
  try {
    JobExtractionSchema.parse(negativeSalary);
    assert.fail('Should reject negative salary');
  } catch (error) {
    assert.ok(error, 'Should throw validation error for negative salary');
  }
  
  // Min > Max salary (should fail)
  const invalidRange = {
    ...baseJob,
    salary_min: 100000,
    salary_max: 50000,
  };
  
  try {
    JobExtractionSchema.parse(invalidRange);
    assert.fail('Should reject min > max salary');
  } catch (error) {
    assert.ok(error, 'Should throw validation error for invalid salary range');
  }
  
  // Very large salary (should pass)
  const largeSalary = {
    ...baseJob,
    salary_min: 1000000,
    salary_max: 2000000,
  };
  
  try {
    const result = JobExtractionSchema.parse(largeSalary);
    assert.ok(result, 'Should parse large but valid salary');
  } catch (error) {
    assert.fail(`Should parse large salary: ${error}`);
  }
});

// Enum Breaking Points
test('Invalid enum values breaking points', () => {
  const baseJob = createValidBaseJob();
  
  const invalidEnums = [
    { ...baseJob, status: 'invalid_status' },
    { ...baseJob, type: 'Invalid Type' },
    { ...baseJob, apply_method: 'invalid_method' },
    { ...baseJob, workplace_type: 'Invalid Workplace' },
    { ...baseJob, salary_currency: 'INVALID' },
    { ...baseJob, salary_unit: 'invalid_unit' },
  ];
  
  for (const invalidJob of invalidEnums) {
    try {
      JobExtractionSchema.parse(invalidJob);
      assert.fail(`Should reject invalid enum: ${JSON.stringify(invalidJob)}`);
    } catch (error) {
      assert.ok(error, 'Should throw validation error for invalid enum');
    }
  }
});

// Required Field Breaking Points
test('Missing required fields breaking points', () => {
  const requiredFields = ['title', 'description', 'status'];
  
  for (const field of requiredFields) {
    const incompleteJob = createValidBaseJob();
    delete incompleteJob[field as keyof typeof incompleteJob];
    
    try {
      JobExtractionSchema.parse(incompleteJob);
      assert.fail(`Should reject job missing ${field}`);
    } catch (error) {
      assert.ok(error, `Should throw validation error for missing ${field}`);
    }
  }
});

// Empty String Breaking Points
test('Empty string validation breaking points', () => {
  const baseJob = createValidBaseJob();
  
  const emptyStringFields = [
    { ...baseJob, title: '' },
    { ...baseJob, description: '' },
    { ...baseJob, company: '' },
  ];
  
  for (const invalidJob of emptyStringFields) {
    try {
      JobExtractionSchema.parse(invalidJob);
      assert.fail(`Should reject empty string: ${JSON.stringify(invalidJob)}`);
    } catch (error) {
      assert.ok(error, 'Should throw validation error for empty string');
    }
  }
});

// URL Validation Breaking Points
test('URL validation breaking points', () => {
  const baseJob = createValidBaseJob();
  
  const invalidUrls = [
    'not-a-url',
    'ftp://invalid-protocol.com',
    'javascript:alert("xss")',
    'data:text/html,<script>alert("xss")</script>',
    '',
  ];
  
  for (const invalidUrl of invalidUrls) {
    const jobWithInvalidUrl = {
      ...baseJob,
      apply_url: invalidUrl,
    };
    
    try {
      JobExtractionSchema.parse(jobWithInvalidUrl);
      assert.fail(`Should reject invalid URL: ${invalidUrl}`);
    } catch (error) {
      assert.ok(error, `Should throw validation error for invalid URL: ${invalidUrl}`);
    }
  }
});

// Date Validation Breaking Points
test('Date validation breaking points', () => {
  const baseJob = createValidBaseJob();
  
  const invalidDates = [
    'not-a-date',
    '2024-13-01', // Invalid month
    '2024-01-32', // Invalid day
    '2024/01/01', // Wrong format
    '',
  ];
  
  for (const invalidDate of invalidDates) {
    const jobWithInvalidDate = {
      ...baseJob,
      posted_date: invalidDate,
    };
    
    try {
      JobExtractionSchema.parse(jobWithInvalidDate);
      assert.fail(`Should reject invalid date: ${invalidDate}`);
    } catch (error) {
      assert.ok(error, `Should throw validation error for invalid date: ${invalidDate}`);
    }
  }
});

// Array Field Breaking Points
test('Array field validation breaking points', () => {
  const baseJob = createValidBaseJob();
  
  // Invalid array types (should be arrays, not strings)
  const invalidArrays = [
    { ...baseJob, career_level: 'Senior' }, // Should be array
    { ...baseJob, languages: 'en' }, // Should be array
    { ...baseJob, career_level: ['Invalid Level'] }, // Invalid enum in array
    { ...baseJob, languages: ['invalid-lang'] }, // Invalid language code
  ];
  
  for (const invalidJob of invalidArrays) {
    try {
      JobExtractionSchema.parse(invalidJob);
      assert.fail(`Should reject invalid array: ${JSON.stringify(invalidJob)}`);
    } catch (error) {
      assert.ok(error, 'Should throw validation error for invalid array');
    }
  }
});

// Edge Case: All Fields at Maximum Length
test('All optional fields at maximum character limits', () => {
  const maxLengthJob = {
    ...createValidBaseJob(),
    benefits: 'A'.repeat(SCHEMA_LIMITS.benefits),
    application_requirements: 'A'.repeat(SCHEMA_LIMITS.application_requirements),
    skills: 'A'.repeat(SCHEMA_LIMITS.skills),
    qualifications: 'A'.repeat(SCHEMA_LIMITS.qualifications),
    education_requirements: 'A'.repeat(SCHEMA_LIMITS.education_requirements),
    experience_requirements: 'A'.repeat(SCHEMA_LIMITS.experience_requirements),
    responsibilities: 'A'.repeat(SCHEMA_LIMITS.responsibilities),
  };
  
  try {
    const result = JobExtractionSchema.parse(maxLengthJob);
    assert.ok(result, 'Should parse job with all fields at maximum length');
  } catch (error) {
    assert.fail(`Should parse job at max limits: ${error}`);
  }
});

// Summary Test: Document All Breaking Points
test('Document all identified breaking points', () => {
  const breakingPoints = {
    characterLimits: SCHEMA_LIMITS,
    inputLimits: INPUT_LIMITS,
    requiredFields: ['title', 'description', 'status'],
    urlValidation: 'Must be valid HTTP/HTTPS URL',
    dateValidation: 'Must be valid ISO 8601 date string',
    salaryValidation: 'Must be non-negative, min <= max',
    enumValidation: 'Must match predefined enum values',
    arrayValidation: 'Arrays must contain valid enum values',
  };
  
  console.log('🔍 Job Extraction Breaking Points Summary:');
  console.log(JSON.stringify(breakingPoints, null, 2));
  
  assert.ok(true, 'Breaking points documented');
});
