#!/usr/bin/env tsx

/**
 * Integration Test Runner
 * Runs all integration tests in tests/integration/ folder
 */

import { spawn } from 'node:child_process';
import { readdir } from 'node:fs/promises';
import { join } from 'node:path';

async function getTestFiles(): Promise<string[]> {
  try {
    const integrationTestDir = join(process.cwd(), 'tests/integration');
    const files = await readdir(integrationTestDir);
    return files
      .filter((file) => file.endsWith('.test.ts') || file.endsWith('.test.js'))
      .map((file) => join(integrationTestDir, file));
  } catch (_error) {
    return [];
  }
}

function runTest(
  testFile: string
): Promise<{ file: string; passed: boolean; duration: number }> {
  const start = Date.now();
  const fileName = testFile.split('/').pop() || testFile;

  return new Promise((resolve) => {
    const child = spawn('npx', ['tsx', testFile], {
      stdio: 'inherit',
      cwd: process.cwd(),
    });

    child.on('close', (code) => {
      const duration = Date.now() - start;
      const passed = code === 0;

      if (passed) {
        // Test passed - no additional action needed
      } else {
        // Test failed - error details captured in result
      }

      resolve({ file: fileName, passed, duration });
    });

    child.on('error', (_error) => {
      resolve({ file: fileName, passed: false, duration: Date.now() - start });
    });
  });
}

async function main() {
  const testFiles = await getTestFiles();

  if (testFiles.length === 0) {
    process.exit(0);
  }

  const results: Array<{ file: string; passed: boolean; duration: number }> =
    [];

  // Run integration tests in parallel since they're independent
  const testPromises = testFiles.map((testFile) => runTest(testFile));
  const testResults = await Promise.all(testPromises);
  results.push(...testResults);

  let _totalPassed = 0;
  let _totalDuration = 0;

  for (const result of results) {
    const _status = result.passed ? '✅' : '❌';

    if (result.passed) {
      _totalPassed++;
    }
    _totalDuration += result.duration;
  }

  // Exit with error if any tests failed
  const allPassed = results.every((r) => r.passed);
  process.exit(allPassed ? 0 : 1);
}

main().catch((_error) => {
  process.exit(1);
});
