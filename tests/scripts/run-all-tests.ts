#!/usr/bin/env tsx

/**
 * Master test runner - runs all test suites
 * Usage: bun run test
 */

import { spawn } from 'node:child_process';

interface TestResult {
  suite: string;
  passed: boolean;
  duration: number;
  error?: string;
}

const results: TestResult[] = [];

function runTestSuite(
  name: string,
  command: string,
  args: string[]
): Promise<TestResult> {
  const start = Date.now();

  return new Promise((resolve) => {
    const child = spawn(command, args, {
      stdio: 'inherit',
      cwd: process.cwd(),
    });

    child.on('close', (code) => {
      const duration = Date.now() - start;
      const passed = code === 0;

      if (passed) {
        // Test passed - no additional action needed
      } else {
        // Test failed - error details captured in result
      }

      resolve({
        suite: name,
        passed,
        duration,
        error: passed ? undefined : `Exit code: ${code}`,
      });
    });

    child.on('error', (error) => {
      resolve({
        suite: name,
        passed: false,
        duration: Date.now() - start,
        error: error.message,
      });
    });
  });
}

async function main() {
  // Run test suites in sequence
  const suites = [
    {
      name: 'Schema Validation',
      cmd: 'npx',
      args: ['tsx', 'tests/utils/validate-openapi.ts'],
    },
    {
      name: 'Unit Tests',
      cmd: 'npx',
      args: ['tsx', 'tests/scripts/run-unit-tests.ts'],
    },
    {
      name: 'API Tests',
      cmd: 'npx',
      args: ['tsx', 'tests/scripts/run-api-tests.ts'],
    },
    {
      name: 'Integration Tests',
      cmd: 'npx',
      args: ['tsx', 'tests/scripts/run-integration-tests.ts'],
    },
    { name: 'E2E Tests', cmd: 'npx', args: ['playwright', 'test'] },
  ];

  // Run test suites sequentially to avoid resource conflicts and port collisions
  for (const suite of suites) {
    // biome-ignore lint/nursery/noAwaitInLoop: intentionally sequential to avoid resource conflicts and port collisions
    const result = await runTestSuite(suite.name, suite.cmd, suite.args);
    results.push(result);
  }

  let _totalPassed = 0;
  let _totalDuration = 0;

  for (const result of results) {
    const _status = result.passed ? '✅' : '❌';

    if (result.error) {
      // Error details already captured in result object
    }

    if (result.passed) {
      _totalPassed++;
    }
    _totalDuration += result.duration;
  }

  // Exit with error if any tests failed
  const allPassed = results.every((r) => r.passed);
  process.exit(allPassed ? 0 : 1);
}

main().catch((_error) => {
  process.exit(1);
});
