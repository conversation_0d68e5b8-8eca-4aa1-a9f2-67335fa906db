#!/usr/bin/env node

/**
 * Test script to trigger Upstash Workflow in production
 * This bypasses the Vercel auth by using the workflow client directly
 */

import { Client } from '@upstash/workflow';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const workflowClient = new Client({
  token: process.env.QSTASH_TOKEN,
});

async function testWorkflow() {
  try {
    console.log('🚀 Testing Upstash Workflow in production...');

    const response = await workflowClient.trigger({
      url: 'https://bordfeed-j6k8bm6zu-growthlog.vercel.app/api/workflows/process-jobs',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        batchSize: 2,
        source: 'production_test_script',
      }),
    });

    console.log('✅ Workflow triggered successfully!');
    console.log('📊 Response:', {
      workflowRunId: response.workflowRunId,
      url: response.url,
    });

    return response.workflowRunId;
  } catch (error) {
    console.error('❌ Failed to trigger workflow:', error);
    throw error;
  }
}

// Run the test
testWorkflow()
  .then((runId) => {
    console.log(`\n🎯 Workflow Run ID: ${runId}`);
    console.log('📈 Check the Upstash console for execution details');
    process.exit(0);
  })
  .catch((error) => {
    console.error('\n💥 Test failed:', error.message);
    process.exit(1);
  });
